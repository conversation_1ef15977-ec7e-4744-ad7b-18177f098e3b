#!/usr/bin/env python3
"""
实时预测脚本
基于重构后的公共模块进行实时预测
"""

import sys
import os
import argparse
import pandas as pd
import time
import signal
from datetime import datetime, timedelta
from typing import Dict, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.features import FeatureEngine
from core.data_processor import DataProcessor
from core.model_manager import ModelManager
from utils.logger import Logger, AlertSound
from utils.data_fetcher import BinanceDataFetcher

class RealTimePredictor:
    """实时预测器"""
    
    def __init__(self, model_name: str = "btc_percentage_model",
                 max_wait_hours: float = 4.0,
                 update_interval: int = 60,
                 enable_sound: bool = True,
                 log_file: str = "prediction_log.txt"):
        """
        初始化实时预测器
        
        Args:
            model_name: 模型名称
            max_wait_hours: 最大等待时间（小时）
            update_interval: 更新间隔（秒）
            enable_sound: 是否启用报警声
            log_file: 日志文件路径
        """
        self.model_name = model_name
        self.max_wait_hours = max_wait_hours
        self.max_wait_minutes = int(max_wait_hours * 60)
        self.update_interval = update_interval
        self.running = True
        
        # 初始化组件
        self.feature_engine = FeatureEngine()
        self.data_processor = DataProcessor()
        self.model_manager = ModelManager(model_dir="models")
        self.logger = Logger(log_file)
        self.alert_sound = AlertSound(enabled=enable_sound)
        self.data_fetcher = BinanceDataFetcher()
        
        # 预测状态
        self.active_predictions: Dict[str, dict] = {}
        self.total_predictions = 0
        self.successful_predictions = 0
        self.failed_predictions = 0
        self.timeout_predictions = 0
        
        # 设置信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        print(f"🚀 实时预测器启动")
        print(f"🤖 模型: {model_name}")
        print(f"⏰ 最大等待时间: {max_wait_hours}小时")
        print(f"🔄 更新间隔: {update_interval}秒")
        print(f"🔔 报警声: {'启用' if enable_sound else '禁用'}")
        print("按 Ctrl+C 停止预测\n")
    
    def signal_handler(self, signum, frame):
        """信号处理器，优雅退出"""
        print(f"\n📋 接收到退出信号 {signum}，正在停止...")
        self.running = False
        self.logger.log_message("系统停止", "用户中断")
    
    def run(self):
        """运行实时预测"""
        try:
            # 1. 加载模型
            model, config = self.model_manager.load_model(self.model_name)
            feature_list = config['feature_list']
            
            model_info = self.model_manager.get_model_info()
            print(f"✅ 模型加载成功")
            print(f"📊 模型类型: {model_info['model_type']}")
            print(f"🎯 最优阈值: {model_info['best_threshold']:.3f}")
            print(f"📈 特征数量: {model_info['feature_count']}")
            
            self.logger.log_message("模型加载", "成功加载模型和配置")
            
            # 2. 检查网络连接
            if not self.data_fetcher.check_connection():
                print("❌ 网络连接失败")
                return
            
            # 3. 获取初始历史数据
            print("📚 获取初始历史数据 (1000条记录)...")
            df = self.data_fetcher.get_historical_data(limit=1000)
            if df is None:
                print("❌ 获取历史数据失败")
                return
            
            # 计算特征
            df = self.feature_engine.calculate_features(df)
            
            # 验证特征
            is_valid, missing_features = self.feature_engine.validate_features(df)
            if not is_valid:
                print(f"❌ 缺少特征: {missing_features}")
                return
            
            print(f"✅ 获取到 {len(df)} 条历史数据")
            self.logger.log_message("数据获取", f"初始数据获取成功，{len(df)}条记录")
            
            print("\n🔄 开始实时预测...")
            print("-" * 60)
            
            last_timestamp = None
            
            # 4. 实时预测主循环
            while self.running:
                try:
                    # 获取最新数据
                    latest_data = self.data_fetcher.get_latest_kline()
                    if latest_data is None:
                        print("⚠️  获取最新数据失败，等待重试...")
                        time.sleep(5)
                        continue
                    
                    # 检查是否有新数据
                    if last_timestamp and latest_data['Timestamp'] <= last_timestamp:
                        time.sleep(self.update_interval)
                        continue
                    
                    # 更新数据
                    current_time = datetime.fromtimestamp(latest_data['Timestamp'])
                    current_price = latest_data['Close']
                    
                    # 添加新数据到DataFrame
                    new_row = pd.DataFrame([{
                        'Timestamp': latest_data['Timestamp'],
                        'open': latest_data['Open'],
                        'high': latest_data['High'],
                        'low': latest_data['Low'],
                        'close': latest_data['Close'],
                        'volume': latest_data['Volume']
                    }])
                    
                    new_row['Timestamp_dt'] = pd.to_datetime(new_row['Timestamp'], unit='s', utc=True).dt.tz_convert('Asia/Shanghai')
                    new_row.set_index('Timestamp_dt', inplace=True)
                    
                    # 计算新行的特征
                    df_temp = pd.concat([df, new_row])
                    df_temp = self.feature_engine.calculate_features(df_temp)
                    new_row_with_features = df_temp.iloc[-1:]
                    
                    df = pd.concat([df, new_row_with_features])
                    df = df.tail(1000)  # 保持最近1000条数据
                    
                    print(f"📈 {current_time.strftime('%Y-%m-%d %H:%M:%S')} | 价格: ${current_price:.2f}")
                    
                    # 检查现有预测
                    self.check_predictions(current_price, current_time)
                    
                    # 进行新预测
                    guess, probability = self.model_manager.predict(df, feature_list)
                    
                    if guess is not None:
                        self.add_prediction(guess, probability, current_price, current_time)
                    else:
                        print("📊 模型分析: 信心不足，放弃本次预测")
                        self.logger.log_message("分析结果", "信心不足，放弃预测", {
                            'probability': probability,
                            'price': current_price,
                            'threshold': config.get('best_threshold', 0.5),
                            'reason': '模型信心度未达到阈值要求'
                        })
                    
                    last_timestamp = latest_data['Timestamp']
                    
                    # 每10次更新显示一次状态
                    if self.total_predictions % 10 == 0 and self.total_predictions > 0:
                        self.print_status()
                    
                    # 等待下次更新
                    time.sleep(self.update_interval)
                    
                except KeyboardInterrupt:
                    break
                except Exception as e:
                    self.logger.log_message("错误", f"运行时错误: {e}")
                    time.sleep(5)
            
        except Exception as e:
            self.logger.log_message("错误", f"启动失败: {e}")
            raise
        finally:
            self.print_final_summary()
    
    def add_prediction(self, guess: int, probability: float, price: float, current_time: datetime):
        """添加新预测"""
        prediction_id = f"pred_{current_time.strftime('%Y%m%d_%H%M%S')}"
        expire_time = current_time + timedelta(hours=self.max_wait_hours)
        
        prediction = {
            'id': prediction_id,
            'guess': guess,
            'probability': probability,
            'start_price': price,
            'start_time': current_time,
            'expire_time': expire_time,
            'up_target': price * 1.01,
            'down_target': price * 0.99,
            'status': 'active'
        }
        
        self.active_predictions[prediction_id] = prediction
        self.total_predictions += 1
        
        direction_str = "先涨1%" if guess == 1 else "先跌1%"
        
        # 播放报警声
        print("🔔 播放报警声...")
        self.alert_sound.play_alert()
        
        print(f"🎯 新预测: {prediction_id}")
        print(f"   方向: {direction_str} (信心: {probability:.3f})")
        print(f"   价格: ${price:.2f}")
        print(f"   目标: 涨至${prediction['up_target']:.2f} 或 跌至${prediction['down_target']:.2f}")
        print(f"   过期: {expire_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        self.logger.log_message("新预测", f"{prediction_id} - {direction_str}", {
            'probability': probability,
            'price': price,
            'up_target': prediction['up_target'],
            'down_target': prediction['down_target']
        })
    
    def check_predictions(self, current_price: float, current_time: datetime):
        """检查活跃预测的状态"""
        completed_predictions = []
        
        for pred_id, prediction in self.active_predictions.items():
            if prediction['status'] != 'active':
                continue
            
            # 检查是否达到目标
            if current_price >= prediction['up_target']:
                # 达到上涨目标
                if prediction['guess'] == 1:
                    self.successful_predictions += 1
                    result = "成功✅"
                    reason = "达到上涨目标"
                else:
                    self.failed_predictions += 1
                    result = "失败❌"
                    reason = "预测下跌但实际上涨"
                
                duration = (current_time - prediction['start_time']).total_seconds() / 60
                print(f"🎉 预测完成: {pred_id}")
                print(f"   结果: {result} - {reason}")
                print(f"   持续时间: {duration:.1f}分钟")
                
                self.logger.log_message("预测完成", f"{pred_id} - {result}", {
                    'direction': "先涨1%" if prediction['guess'] == 1 else "先跌1%",
                    'probability': prediction['probability'],
                    'start_price': prediction['start_price'],
                    'end_price': current_price,
                    'duration_minutes': duration,
                    'reason': reason
                })
                
                completed_predictions.append(pred_id)
                
            elif current_price <= prediction['down_target']:
                # 达到下跌目标
                if prediction['guess'] == 0:
                    self.successful_predictions += 1
                    result = "成功✅"
                    reason = "达到下跌目标"
                else:
                    self.failed_predictions += 1
                    result = "失败❌"
                    reason = "预测上涨但实际下跌"
                
                duration = (current_time - prediction['start_time']).total_seconds() / 60
                print(f"🎉 预测完成: {pred_id}")
                print(f"   结果: {result} - {reason}")
                print(f"   持续时间: {duration:.1f}分钟")
                
                self.logger.log_message("预测完成", f"{pred_id} - {result}", {
                    'direction': "先涨1%" if prediction['guess'] == 1 else "先跌1%",
                    'probability': prediction['probability'],
                    'start_price': prediction['start_price'],
                    'end_price': current_price,
                    'duration_minutes': duration,
                    'reason': reason
                })
                
                completed_predictions.append(pred_id)
                
            elif current_time >= prediction['expire_time']:
                # 超时
                self.timeout_predictions += 1
                duration = (current_time - prediction['start_time']).total_seconds() / 60
                print(f"⏰ 预测超时: {pred_id}")
                print(f"   持续时间: {duration:.1f}分钟")
                
                self.logger.log_message("预测超时", f"{pred_id}", {
                    'direction': "先涨1%" if prediction['guess'] == 1 else "先跌1%",
                    'probability': prediction['probability'],
                    'start_price': prediction['start_price'],
                    'end_price': current_price,
                    'duration_minutes': duration
                })
                
                completed_predictions.append(pred_id)
        
        # 移除已完成的预测
        for pred_id in completed_predictions:
            del self.active_predictions[pred_id]
    
    def print_status(self):
        """打印当前状态"""
        print(f"\n📊 当前状态:")
        print(f"   总预测数: {self.total_predictions}")
        print(f"   活跃预测: {len(self.active_predictions)}")
        print(f"   成功: {self.successful_predictions}")
        print(f"   失败: {self.failed_predictions}")
        print(f"   超时: {self.timeout_predictions}")
        print()
    
    def print_final_summary(self):
        """打印最终总结"""
        print(f"\n" + "="*50)
        print("实时预测总结")
        print("="*50)
        
        total_completed = self.successful_predictions + self.failed_predictions + self.timeout_predictions
        success_rate = (self.successful_predictions / max(1, total_completed - self.timeout_predictions)) * 100
        
        print(f"总预测数: {self.total_predictions}")
        print(f"已完成: {total_completed}")
        print(f"活跃中: {len(self.active_predictions)}")
        print(f"成功: {self.successful_predictions}")
        print(f"失败: {self.failed_predictions}")
        print(f"超时: {self.timeout_predictions}")
        print(f"成功率: {success_rate:.1f}% (基于非超时预测)")
        print("="*50)
        
        self.logger.log_message("系统停止", "预测总结", {
            'total_predictions': self.total_predictions,
            'successful': self.successful_predictions,
            'failed': self.failed_predictions,
            'timeout': self.timeout_predictions,
            'success_rate': success_rate
        })

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="实时BTC价格预测器")
    parser.add_argument("--model-name", default="btc_percentage_model", help="模型名称")
    parser.add_argument("--max-wait-hours", type=float, default=4.0, help="最大等待时间（小时）")
    parser.add_argument("--update-interval", type=int, default=60, help="更新间隔（秒）")
    parser.add_argument("--no-sound", action="store_true", help="禁用报警声")
    parser.add_argument("--log-file", default="prediction_log.txt", help="日志文件路径")
    
    args = parser.parse_args()
    
    # 检查模型文件是否存在
    model_path = f"models/{args.model_name}.joblib"
    config_path = f"models/{args.model_name}_config.json"
    
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        print("请先运行 train.py 训练模型")
        return
    
    if not os.path.exists(config_path):
        print(f"❌ 配置文件不存在: {config_path}")
        print("请先运行 train.py 训练模型")
        return
    
    # 创建预测器并运行
    predictor = RealTimePredictor(
        model_name=args.model_name,
        max_wait_hours=args.max_wait_hours,
        update_interval=args.update_interval,
        enable_sound=not args.no_sound,
        log_file=args.log_file
    )
    
    predictor.run()

if __name__ == "__main__":
    main()
