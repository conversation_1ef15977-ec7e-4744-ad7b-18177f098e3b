#!/usr/bin/env python3
"""
数据获取工具模块
"""

import requests
import pandas as pd
import time
from datetime import datetime, timezone
from typing import Optional, List, Dict, Any

class BinanceDataFetcher:
    """Binance数据获取器"""
    
    def __init__(self, symbol: str = 'BTCUSDT', interval: str = '1m'):
        """
        初始化数据获取器
        
        Args:
            symbol: 交易对符号
            interval: K线间隔
        """
        self.symbol = symbol
        self.interval = interval
        self.base_url = 'https://api.binance.com'
    
    def get_latest_kline(self) -> Optional[Dict[str, Any]]:
        """
        获取最新的K线数据
        
        Returns:
            最新K线数据字典，失败返回None
        """
        try:
            url = f"{self.base_url}/api/v3/klines"
            params = {
                'symbol': self.symbol,
                'interval': self.interval,
                'limit': 1
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            klines = response.json()
            if not klines:
                return None
            
            kline = klines[0]
            data = {
                'Timestamp': int(kline[0]) // 1000,
                'Open': float(kline[1]),
                'High': float(kline[2]),
                'Low': float(kline[3]),
                'Close': float(kline[4]),
                'Volume': float(kline[5]),
                'CloseTime': int(kline[6]) // 1000,
                'QuoteVolume': float(kline[7]),
                'TradeCount': int(kline[8]),
                'TakerBuyBaseVolume': float(kline[9]),
                'TakerBuyQuoteVolume': float(kline[10])
            }
            
            return data
            
        except Exception as e:
            print(f"❌ 获取最新数据失败: {e}")
            return None
    
    def get_historical_data(self, limit: int = 1000) -> Optional[pd.DataFrame]:
        """
        获取历史数据
        
        Args:
            limit: 数据条数
            
        Returns:
            历史数据DataFrame，失败返回None
        """
        try:
            print(f"📚 获取历史数据 ({limit}条记录)...")
            
            # 分批获取数据
            all_data = []
            max_limit_per_request = 1000
            
            if limit <= max_limit_per_request:
                klines = self._fetch_klines(limit=limit)
                if klines:
                    all_data.extend(klines)
            else:
                remaining = limit
                end_time = None
                batch_count = 0
                
                while remaining > 0 and batch_count < 100:
                    batch_count += 1
                    current_limit = min(remaining, max_limit_per_request)
                    
                    klines = self._fetch_klines(limit=current_limit, end_time=end_time)
                    if not klines:
                        break
                    
                    all_data = klines + all_data
                    remaining -= len(klines)
                    end_time = int(klines[0][0])
                    
                    time.sleep(0.1)  # 避免请求过快
                    
                    if len(klines) < current_limit:
                        break
            
            if not all_data:
                return None
            
            # 转换为DataFrame
            data_list = []
            for kline in all_data:
                data = {
                    'Timestamp': int(kline[0]) // 1000,
                    'Open': float(kline[1]),
                    'High': float(kline[2]),
                    'Low': float(kline[3]),
                    'Close': float(kline[4]),
                    'Volume': float(kline[5]),
                    'CloseTime': int(kline[6]) // 1000,
                    'QuoteVolume': float(kline[7]),
                    'TradeCount': int(kline[8]),
                    'TakerBuyBaseVolume': float(kline[9]),
                    'TakerBuyQuoteVolume': float(kline[10])
                }
                data_list.append(data)
            
            # 按时间排序并去重
            data_list.sort(key=lambda x: x['Timestamp'])
            seen_timestamps = set()
            unique_data = []
            for data in data_list:
                if data['Timestamp'] not in seen_timestamps:
                    seen_timestamps.add(data['Timestamp'])
                    unique_data.append(data)
            
            df = pd.DataFrame(unique_data)
            df['Timestamp_dt'] = pd.to_datetime(df['Timestamp'], unit='s', utc=True).dt.tz_convert('Asia/Shanghai')
            df.set_index('Timestamp_dt', inplace=True)
            
            print(f"✅ 获取到 {len(df)} 条历史数据")
            
            return df
            
        except Exception as e:
            print(f"❌ 获取历史数据失败: {e}")
            return None
    
    def _fetch_klines(self, limit: int = 1000, end_time: Optional[int] = None) -> Optional[List]:
        """
        获取单批K线数据
        
        Args:
            limit: 数据条数
            end_time: 结束时间
            
        Returns:
            K线数据列表
        """
        try:
            url = f"{self.base_url}/api/v3/klines"
            params = {
                'symbol': self.symbol,
                'interval': self.interval,
                'limit': limit
            }
            
            if end_time:
                params['endTime'] = end_time
            
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            return response.json()
            
        except Exception as e:
            print(f"❌ 获取K线数据失败: {e}")
            return None
    
    def check_connection(self) -> bool:
        """
        检查网络连接
        
        Returns:
            连接是否正常
        """
        try:
            url = f"{self.base_url}/api/v3/time"
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            
            server_time = response.json()['serverTime']
            dt = datetime.fromtimestamp(server_time/1000, tz=timezone.utc)
            beijing_dt = dt.astimezone(timezone.utc).replace(tzinfo=None) + pd.Timedelta(hours=8)
            
            print(f"✅ 连接正常，服务器时间: {beijing_dt.strftime('%Y-%m-%d %H:%M:%S')}")
            return True
            
        except Exception as e:
            print(f"❌ 连接检查失败: {e}")
            return False
    
    def format_timestamp(self, timestamp: float) -> str:
        """
        格式化时间戳为北京时间
        
        Args:
            timestamp: Unix时间戳
            
        Returns:
            格式化的时间字符串
        """
        dt = datetime.fromtimestamp(timestamp, tz=timezone.utc)
        beijing_dt = dt.astimezone(timezone.utc).replace(tzinfo=None) + pd.Timedelta(hours=8)
        return beijing_dt.strftime('%Y-%m-%d %H:%M:%S')
