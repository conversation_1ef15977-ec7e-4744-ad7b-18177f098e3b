#!/usr/bin/env python3
"""
日志和报警工具模块
"""

import os
import platform
from datetime import datetime
from typing import Dict, Any, Optional

class Logger:
    """日志记录器"""
    
    def __init__(self, log_file: str = "prediction_log.txt"):
        """
        初始化日志记录器
        
        Args:
            log_file: 日志文件路径
        """
        self.log_file = log_file
    
    def log_message(self, event_type: str, message: str, data: Dict[str, Any] = None):
        """
        记录日志消息
        
        Args:
            event_type: 事件类型
            message: 消息内容
            data: 附加数据
        """
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = f"[{timestamp}] {event_type}: {message}"
        
        if data:
            log_entry += f" | 数据: {data}"
        
        # 写入日志文件
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry + '\n')

class AlertSound:
    """报警声管理器"""
    
    def __init__(self, enabled: bool = True):
        """
        初始化报警声管理器
        
        Args:
            enabled: 是否启用报警声
        """
        self.enabled = enabled
    
    def play_alert(self):
        """播放报警声"""
        if not self.enabled:
            return
        
        try:
            system = platform.system().lower()
            
            if system == "windows":
                self._play_windows_sound()
            elif system == "darwin":  # macOS
                self._play_macos_sound()
            elif system == "linux":
                self._play_linux_sound()
            else:
                self._play_fallback_sound()
                
        except Exception:
            self._play_fallback_sound()
    
    def _play_windows_sound(self):
        """播放Windows系统声音"""
        try:
            import winsound
            winsound.MessageBeep(winsound.MB_ICONEXCLAMATION)
            winsound.Beep(1000, 500)
        except ImportError:
            self._play_fallback_sound()
    
    def _play_macos_sound(self):
        """播放macOS系统声音"""
        try:
            result = os.system('afplay /System/Library/Sounds/Glass.aiff')
            if result != 0:
                os.system('say "预测信号"')
        except:
            self._play_fallback_sound()
    
    def _play_linux_sound(self):
        """播放Linux系统声音"""
        try:
            result = os.system('paplay /usr/share/sounds/alsa/Front_Left.wav 2>/dev/null')
            if result != 0:
                result = os.system('beep -f 1000 -l 500 2>/dev/null')
                if result != 0:
                    self._play_fallback_sound()
        except:
            self._play_fallback_sound()
    
    def _play_fallback_sound(self):
        """播放备用声音（终端bell）"""
        print('\a' * 3)
