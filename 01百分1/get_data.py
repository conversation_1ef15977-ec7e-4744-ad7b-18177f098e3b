#!/usr/bin/env python3
"""
数据获取脚本
基于重构后的公共模块获取历史数据
"""

import sys
import os
import argparse

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.data_fetcher import BinanceDataFetcher

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="获取BTC历史数据")
    parser.add_argument("--symbol", default="BTCUSDT", help="交易对符号")
    parser.add_argument("--interval", default="1m", help="K线间隔")
    parser.add_argument("--limit", type=int, default=10000, help="获取数据条数")
    parser.add_argument("--output", default="data/btcusd_1-min_data.csv", help="输出文件路径")
    
    args = parser.parse_args()
    
    print("🚀 BTC历史数据获取工具")
    print("="*50)
    print(f"📊 交易对: {args.symbol}")
    print(f"⏰ 间隔: {args.interval}")
    print(f"📈 数据条数: {args.limit}")
    print(f"💾 输出文件: {args.output}")
    print("="*50)
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(args.output), exist_ok=True)
    
    # 初始化数据获取器
    fetcher = BinanceDataFetcher(symbol=args.symbol, interval=args.interval)
    
    # 检查网络连接
    if not fetcher.check_connection():
        print("❌ 网络连接失败")
        return
    
    # 获取历史数据
    df = fetcher.get_historical_data(limit=args.limit)
    
    if df is None:
        print("❌ 数据获取失败")
        return
    
    # 保存数据
    try:
        df.to_csv(args.output, index=True)
        print(f"✅ 数据已保存到: {args.output}")
        print(f"📊 数据统计:")
        print(f"   总条数: {len(df)}")
        print(f"   时间范围: {df.index[0]} 到 {df.index[-1]}")
        print(f"   价格范围: ${df['Close'].min():.2f} - ${df['Close'].max():.2f}")
        
    except Exception as e:
        print(f"❌ 保存数据失败: {e}")

if __name__ == "__main__":
    main()
