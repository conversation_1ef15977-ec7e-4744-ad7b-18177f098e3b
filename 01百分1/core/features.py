#!/usr/bin/env python3
"""
特征工程公共模块
确保训练、测试、实时执行的特征计算一致性
"""

import pandas as pd
import numpy as np
from typing import Optional

class FeatureEngine:
    """特征工程引擎"""

    def __init__(self, epsilon: float = 1e-9):
        """
        初始化特征工程引擎

        Args:
            epsilon: 防止除零的小数值
        """
        self.epsilon = epsilon

    def calculate_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算所有特征（与LightGBM_percentage_v2.py保持一致）

        Args:
            df: 包含OHLCV数据的DataFrame

        Returns:
            包含所有特征的DataFrame
        """
        df_work = df.copy()

        # 确保列名统一
        df_work = self._standardize_columns(df_work)

        # 1. 时间特征（最重要）
        df_work = self._add_time_features(df_work)

        # 2. ATR特征（第3重要）
        df_work = self._add_atr_features(df_work)

        # 3. 价格/动量特征
        df_work = self._add_price_momentum_features(df_work)

        # 4. 波动率特征
        df_work = self._add_volatility_features(df_work)

        # 5. 趋势特征
        df_work = self._add_trend_features(df_work)

        # 6. VWAP特征
        df_work = self._add_vwap_features(df_work)

        # 7. 成交量特征
        df_work = self._add_volume_features(df_work)

        # 8. 滚动K线特征
        df_work = self._add_rolling_kline_features(df_work)

        # 9. 主动买卖成交量特征
        df_work = self._add_taker_volume_features(df_work)

        return df_work

    def _standardize_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化列名"""
        column_mapping = {
            'Open': 'open',
            'High': 'high',
            'Low': 'low',
            'Close': 'close',
            'Volume': 'volume'
        }

        for old_col, new_col in column_mapping.items():
            if old_col in df.columns:
                df = df.rename(columns={old_col: new_col})

        return df

    def _add_time_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加时间特征"""
        df['hour'] = df.index.hour  # 重要性: 290
        df['day_of_week'] = df.index.dayofweek  # 重要性: 191
        return df

    def _add_atr_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加ATR相关特征"""
        return self.get_standardized_kline_features(df, timeframe_suffix='_1m')

    def _add_price_momentum_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加价格动量特征"""
        df['return_60min'] = df['close'].pct_change(60)  # 重要性: 134
        df['return_30min'] = df['close'].pct_change(30)  # 重要性: 41
        df['return_10min'] = df['close'].pct_change(10)  # 重要性: 13
        df['return_5min'] = df['close'].pct_change(5)   # 重要性: 11
        df['return_3min'] = df['close'].pct_change(3)   # 重要性: 3
        return df

    def _add_volatility_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加波动率特征"""
        for n in [10, 30, 60]:
            df[f'volatility_{n}'] = df['close'].rolling(window=n).std()
            df[f'volatility_ratio_{n}'] = df[f'volatility_{n}'] / (df['close'] + self.epsilon)
        return df

    def _add_trend_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加趋势特征"""
        for n in [10, 30, 60]:
            df[f'sma_{n}'] = df['close'].rolling(window=n).mean()
            df[f'price_div_sma_{n}'] = df['close'] / (df[f'sma_{n}'] + self.epsilon)

        df['sma_10_div_sma_30'] = df['sma_10'] / (df['sma_30'] + self.epsilon)  # 重要性: 14
        return df

    def _add_vwap_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加VWAP特征"""
        df['price_x_volume'] = df['close'] * df['volume']
        vwap_numerator = df['price_x_volume'].rolling(window=30).sum()
        vwap_denominator = df['volume'].rolling(window=30).sum()
        df['vwap_30'] = vwap_numerator / (vwap_denominator + self.epsilon)
        df['price_div_vwap_30'] = df['close'] / (df['vwap_30'] + self.epsilon)  # 重要性: 46
        df.drop('price_x_volume', axis=1, inplace=True)
        return df

    def _add_volume_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加成交量特征"""
        df['vma_60'] = df['volume'].rolling(window=60).mean()
        df['volume_div_vma_60'] = df['volume'] / (df['vma_60'] + self.epsilon)  # 重要性: 5
        return df

    def _add_rolling_kline_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加滚动K线特征"""
        for window in [3, 5]:
            if f'range_norm_by_atr_1m' in df.columns:
                df[f'range_norm_by_atr_mean_{window}m'] = df['range_norm_by_atr_1m'].rolling(window=window).mean()

        if f'body_percent_of_range_1m' in df.columns:
            df[f'body_percent_mean_5m'] = df['body_percent_of_range_1m'].rolling(window=5).mean()

        return df

    def _add_taker_volume_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加主动买卖成交量特征"""
        try:
            # 确保必要的列存在
            required_cols = ['TakerBuyBaseVolume', 'TakerBuyQuoteVolume', 'volume', 'QuoteVolume']
            missing_cols = [col for col in required_cols if col not in df.columns]

            if missing_cols:
                print(f"⚠️  缺少主动买卖成交量列: {missing_cols}，跳过相关特征")
                return df

            # 1. 买卖比例特征
            df['taker_buy_ratio'] = df['TakerBuyBaseVolume'] / (df['volume'] + self.epsilon)
            df['taker_sell_ratio'] = (df['volume'] - df['TakerBuyBaseVolume']) / (df['volume'] + self.epsilon)

            # 2. 买卖压力指标
            df['buy_sell_pressure'] = (df['TakerBuyBaseVolume'] - (df['volume'] - df['TakerBuyBaseVolume'])) / (df['volume'] + self.epsilon)

            # 3. 资金流向特征
            df['taker_buy_quote_ratio'] = df['TakerBuyQuoteVolume'] / (df['QuoteVolume'] + self.epsilon)
            df['taker_sell_quote_ratio'] = (df['QuoteVolume'] - df['TakerBuyQuoteVolume']) / (df['QuoteVolume'] + self.epsilon)

            # 4. 滚动窗口特征
            for window in [5, 10, 30]:
                df[f'taker_buy_ratio_ma_{window}'] = df['taker_buy_ratio'].rolling(window=window).mean()
                df[f'buy_sell_pressure_ma_{window}'] = df['buy_sell_pressure'].rolling(window=window).mean()
                df[f'taker_buy_quote_ratio_ma_{window}'] = df['taker_buy_quote_ratio'].rolling(window=window).mean()

            # 5. 买卖强度变化
            df['taker_buy_ratio_change'] = df['taker_buy_ratio'].diff()
            df['buy_sell_pressure_change'] = df['buy_sell_pressure'].diff()

        except Exception as e:
            print(f"主动买卖成交量特征计算出错: {e}")

        return df

    def get_standardized_kline_features(self, df: pd.DataFrame, timeframe_suffix: str = '') -> pd.DataFrame:
        """
        计算标准化的K线特征

        Args:
            df: 数据框
            timeframe_suffix: 时间框架后缀

        Returns:
            添加了K线特征的数据框
        """
        try:
            high_low = df['high'] - df['low']
            high_prev_close = abs(df['high'] - df['close'].shift(1))
            low_prev_close = abs(df['low'] - df['close'].shift(1))
            tr = pd.concat([high_low, high_prev_close, low_prev_close], axis=1).max(axis=1)
            atr_col = f'atr_14{timeframe_suffix}'
            df[atr_col] = tr.rolling(window=14).mean()

            body_size = abs(df['close'] - df['open'])
            price_range = df['high'] - df['low']
            upper_shadow = df['high'] - df[['open', 'close']].max(axis=1)
            lower_shadow = df[['open', 'close']].min(axis=1) - df['low']

            df[f'body_percent_of_range{timeframe_suffix}'] = body_size / (price_range + self.epsilon)
            df[f'upper_shadow_percent_of_range{timeframe_suffix}'] = upper_shadow / (price_range + self.epsilon)
            df[f'lower_shadow_percent_of_range{timeframe_suffix}'] = lower_shadow / (price_range + self.epsilon)
            df[f'range_norm_by_atr{timeframe_suffix}'] = price_range / (df[atr_col] + self.epsilon)

        except Exception as e:
            print(f"K线特征计算出错: {e}")

        return df

    def get_feature_list(self) -> list:
        """
        获取特征列表 - 与原始LightGBM_percentage.py保持一致的20个特征
        """
        return [
            'hour',                           # 时间特征
            'day_of_week',                   # 时间特征
            'atr_14_1m',                     # ATR特征
            'return_60min',                  # 价格动量
            'return_30min',                  # 价格动量
            'return_10min',                  # 价格动量
            'return_5min',                   # 价格动量
            'return_3min',                   # 价格动量
            'volatility_ratio_60',           # 波动率比率
            'volatility_ratio_30',           # 波动率比率
            'volatility_ratio_10',           # 波动率比率
            'price_div_sma_60',             # 价格趋势
            'price_div_vwap_30',            # VWAP特征
            'price_div_sma_30',             # 价格趋势
            'sma_10_div_sma_30',            # 趋势特征
            'price_div_sma_10',             # 价格趋势
            'range_norm_by_atr_mean_3m',    # K线特征
            'range_norm_by_atr_mean_5m',    # K线特征
            'body_percent_mean_5m',         # K线特征
            'volume_div_vma_60'             # 成交量特征
        ]

    def get_enhanced_feature_list(self) -> list:
        """
        获取包含主动买卖成交量特征的增强版特征列表
        """
        original_features = self.get_feature_list()
        taker_features = [
            'taker_buy_ratio',              # 主动买入占比
            'buy_sell_pressure',            # 买卖压力差
            'taker_buy_quote_ratio',        # 主动买入资金占比
            'taker_buy_ratio_ma_10',        # 买入比例10期移动平均
            'buy_sell_pressure_ma_10'       # 买卖压力10期移动平均
        ]
        return original_features + taker_features

    def validate_features(self, df: pd.DataFrame) -> tuple[bool, list]:
        """
        验证特征是否完整

        Args:
            df: 特征数据框

        Returns:
            (是否有效, 缺失的特征列表)
        """
        required_features = self.get_feature_list()
        missing_features = [f for f in required_features if f not in df.columns]

        return len(missing_features) == 0, missing_features
