#!/usr/bin/env python3
"""
使用示例脚本
演示如何使用重构后的系统进行完整的工作流程
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def example_complete_workflow():
    """完整工作流程示例"""
    print("🚀 BTC百分比预测系统 - 完整工作流程示例")
    print("="*60)
    
    # 1. 数据获取示例
    print("\n📊 步骤1: 数据获取")
    print("-" * 30)
    print("# 获取历史数据")
    print("python get_data.py --limit 10000 --output data/btcusd_1-min_data.csv")
    
    # 2. 特征工程示例
    print("\n🔧 步骤2: 特征工程")
    print("-" * 30)
    
    from core.features import FeatureEngine
    
    engine = FeatureEngine()
    feature_list = engine.get_feature_list()
    
    print(f"✅ 特征工程引擎初始化完成")
    print(f"📈 重要特征数量: {len(feature_list)}")
    print(f"🎯 核心特征: {feature_list[:5]}...")
    
    # 3. 数据处理示例
    print("\n📋 步骤3: 数据处理")
    print("-" * 30)
    
    from core.data_processor import DataProcessor
    
    processor = DataProcessor()
    print(f"✅ 数据处理器初始化完成")
    print(f"🎯 支持功能: 数据加载、标签生成、数据分割、质量验证")
    
    # 4. 模型管理示例
    print("\n🤖 步骤4: 模型管理")
    print("-" * 30)
    
    from core.model_manager import ModelManager
    
    manager = ModelManager()
    print(f"✅ 模型管理器初始化完成")
    print(f"🎯 支持功能: 训练、评估、保存、加载、预测")
    
    # 5. 工具模块示例
    print("\n🛠️  步骤5: 工具模块")
    print("-" * 30)
    
    from utils.logger import Logger, AlertSound
    from utils.data_fetcher import BinanceDataFetcher
    
    logger = Logger()
    alert = AlertSound(enabled=False)  # 示例中禁用声音
    fetcher = BinanceDataFetcher()
    
    print(f"✅ 工具模块初始化完成")
    print(f"🎯 支持功能: 日志记录、报警提醒、数据获取")
    
    # 6. 完整流程命令
    print("\n🔄 步骤6: 完整流程命令")
    print("-" * 30)
    print("# 训练模型")
    print("python train.py --data-file data/btcusd_1-min_data.csv --save-data")
    print()
    print("# 回测验证")
    print("python backtest.py data/btcusd_1-min_data.csv --mode limited --max-wait-hours 4")
    print()
    print("# 实时预测")
    print("python realtime.py --max-wait-hours 4 --update-interval 60")
    
    print("\n🎉 工作流程示例完成！")

def example_feature_consistency():
    """特征一致性示例"""
    print("\n🔬 特征一致性验证示例")
    print("="*40)
    
    from core.features import FeatureEngine
    import pandas as pd
    import numpy as np
    
    # 创建相同的测试数据
    np.random.seed(42)
    dates = pd.date_range('2025-01-01', periods=500, freq='1min')
    
    test_data = {
        'open': np.random.randn(500).cumsum() + 100,
        'high': np.random.randn(500).cumsum() + 102,
        'low': np.random.randn(500).cumsum() + 98,
        'close': np.random.randn(500).cumsum() + 100,
        'volume': np.random.randint(1000, 10000, 500)
    }
    
    df = pd.DataFrame(test_data, index=dates)
    
    # 使用特征引擎计算特征
    engine = FeatureEngine()
    
    # 第一次计算
    df_features_1 = engine.calculate_features(df.copy())
    
    # 第二次计算（应该得到相同结果）
    df_features_2 = engine.calculate_features(df.copy())
    
    # 验证一致性
    feature_list = engine.get_feature_list()
    
    print(f"📊 测试数据: {len(df)} 行")
    print(f"🔧 特征数量: {len(feature_list)}")
    
    # 检查特征值是否一致
    consistent = True
    for feature in feature_list:
        if feature in df_features_1.columns and feature in df_features_2.columns:
            diff = (df_features_1[feature] - df_features_2[feature]).abs().max()
            if diff > 1e-10:  # 允许极小的浮点误差
                print(f"❌ 特征 {feature} 不一致，最大差异: {diff}")
                consistent = False
    
    if consistent:
        print("✅ 所有特征计算结果一致！")
        print("🎯 训练、测试、实时执行将使用相同的特征")
    else:
        print("❌ 发现特征不一致问题")
    
    return consistent

def example_model_pipeline():
    """模型流水线示例"""
    print("\n🔄 模型流水线示例")
    print("="*30)
    
    from core.features import FeatureEngine
    from core.data_processor import DataProcessor
    from core.model_manager import ModelManager
    
    print("1️⃣ 初始化组件")
    feature_engine = FeatureEngine()
    data_processor = DataProcessor()
    model_manager = ModelManager()
    
    print("2️⃣ 获取特征列表")
    feature_list = feature_engine.get_feature_list()
    print(f"   📈 特征数量: {len(feature_list)}")
    
    print("3️⃣ 模拟数据流水线")
    print("   📊 数据加载 → 特征计算 → 标签生成 → 数据分割")
    print("   🤖 模型训练 → 模型评估 → 模型保存")
    print("   🔮 模型加载 → 实时预测")
    
    print("4️⃣ 一致性保证")
    print("   ✅ 所有阶段使用相同的特征计算逻辑")
    print("   ✅ 统一的数据处理接口")
    print("   ✅ 标准化的模型管理")
    
    print("🎉 流水线示例完成！")

def main():
    """主函数"""
    print("🎯 BTC百分比预测系统 - 使用示例")
    print("="*60)
    
    try:
        # 运行各种示例
        example_complete_workflow()
        
        # 验证特征一致性
        if example_feature_consistency():
            print("\n✅ 特征一致性验证通过")
        else:
            print("\n❌ 特征一致性验证失败")
        
        # 展示模型流水线
        example_model_pipeline()
        
        print("\n" + "="*60)
        print("🎉 所有示例运行完成！")
        print("📚 更多详细信息请查看 README.md")
        print("🚀 开始使用: python train.py --help")
        print("="*60)
        
    except Exception as e:
        print(f"❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
