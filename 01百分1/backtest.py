#!/usr/bin/env python3
"""
回测脚本
基于重构后的公共模块进行回测
"""

import sys
import os
import argparse
import pandas as pd
import time
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.features import FeatureEngine
from core.data_processor import DataProcessor
from core.model_manager import ModelManager
from utils.logger import Logger

def run_backtest(data_file: str, model_name: str, start_row: int = 5000, 
                start_time: str = None, speed: float = 0.1, 
                max_wait_hours: float = 4.0, mode: str = "limited"):
    """
    运行回测
    
    Args:
        data_file: 数据文件路径
        model_name: 模型名称
        start_row: 起始行号
        start_time: 起始时间
        speed: 模拟速度
        max_wait_hours: 最大等待时间（小时）
        mode: 模式（limited=限制模式，concurrent=并发模式）
    """
    print("🚀 BTC百分比目标回测系统")
    print("="*50)
    
    # 初始化组件
    feature_engine = FeatureEngine()
    data_processor = DataProcessor()
    model_manager = ModelManager(model_dir="models")
    logger = Logger(f"logs/backtest_{model_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")
    
    max_wait_minutes = int(max_wait_hours * 60)
    
    print(f"📊 回测配置:")
    print(f"   模型: {model_name}")
    print(f"   数据文件: {data_file}")
    print(f"   模式: {'限制模式' if mode == 'limited' else '并发模式'}")
    print(f"   最大等待时间: {max_wait_hours}小时 ({max_wait_minutes}分钟)")
    print(f"   模拟速度: {speed}秒/行")
    
    # 1. 加载数据
    df = data_processor.load_data(data_file)
    
    # 2. 加载模型
    try:
        model, config = model_manager.load_model(model_name)
        feature_list = config['feature_list']
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return
    
    # 3. 计算特征
    print("🔧 计算特征...")
    df_with_features = feature_engine.calculate_features(df)
    
    # 4. 验证特征
    is_valid, missing_features = feature_engine.validate_features(df_with_features)
    if not is_valid:
        print(f"❌ 缺少特征: {missing_features}")
        return
    
    # 5. 确定起始位置
    if start_time:
        try:
            start_dt = pd.to_datetime(start_time).tz_localize('Asia/Shanghai')
            start_idx = df_with_features.index.get_indexer([start_dt], method='nearest')[0]
            if start_idx == -1:
                print(f"❌ 找不到指定时间: {start_time}")
                return
        except Exception as e:
            print(f"❌ 时间解析失败: {e}")
            return
    else:
        start_idx = start_row
    
    if start_idx >= len(df_with_features) - max_wait_minutes:
        print(f"❌ 起始位置太靠后，没有足够的数据进行回测")
        return
    
    print(f"📍 回测起始位置: 第{start_idx}行 ({df_with_features.index[start_idx]})")
    
    # 6. 初始化回测状态
    if mode == "limited":
        current_guess = None  # 限制模式：一次只能一个预测
    else:
        pending_guesses = {}  # 并发模式：多个预测
    
    total_score, wins, losses, timeouts = 0, 0, 0, 0
    
    logger.log_message("回测开始", f"模式: {mode}, 起始位置: {start_idx}")
    
    print("\n🔄 开始回测模拟...")
    print("-" * 60)
    
    # 7. 回测主循环
    for i in range(start_idx, len(df_with_features) - max_wait_minutes):
        current_time = df_with_features.index[i]
        current_price = df_with_features.iloc[i]['close']
        
        print(f"📈 {current_time.strftime('%Y-%m-%d %H:%M:%S')} | 价格: ${current_price:.2f}")
        
        if mode == "limited":
            # 限制模式逻辑
            if current_guess is not None:
                # 检查现有预测
                if current_time >= current_guess['resolve_time']:
                    # 验证预测结果
                    price_history = df_with_features.iloc[
                        current_guess['start_idx']:i+1
                    ]['close'].tolist()
                    
                    result, actual_minutes = data_processor.check_percentage_result(
                        current_guess['guess_price'], 
                        price_history,
                        up_threshold=0.01,
                        down_threshold=0.01,
                        max_minutes=max_wait_minutes
                    )
                    
                    # 记录结果
                    if result is not None and current_guess['guess_direction'] == result:
                        total_score += 1
                        wins += 1
                        direction_str = "先涨1%" if result == 1 else "先跌1%"
                        print(f"✅ 猜对了! 实际{direction_str} (用时{actual_minutes}分钟)")
                        print(f"\033[92m得分: +1, 当前总分: {total_score:+d}\033[0m")
                        
                        logger.log_message("预测成功", f"{current_guess['id']} - {direction_str}", {
                            'probability': current_guess['probability'],
                            'duration_minutes': actual_minutes
                        })
                    elif result is not None:
                        total_score -= 1
                        losses += 1
                        actual_str = "先涨1%" if result == 1 else "先跌1%"
                        guess_str = "先涨1%" if current_guess['guess_direction'] == 1 else "先跌1%"
                        print(f"❌ 猜错了! 猜测{guess_str}，实际{actual_str}")
                        print(f"\033[91m得分: -1, 当前总分: {total_score:+d}\033[0m")
                        
                        logger.log_message("预测失败", f"{current_guess['id']} - {guess_str} vs {actual_str}")
                    else:
                        timeouts += 1
                        print(f"⏰ 超时! {max_wait_hours}小时内都没有达到1%的涨跌幅")
                        print(f"\033[93m得分: 0 (超时不计分), 当前总分: {total_score:+d}\033[0m")
                        
                        logger.log_message("预测超时", f"{current_guess['id']}")
                    
                    current_guess = None  # 清除当前预测
            
            else:
                # 没有活跃预测，尝试新预测
                if i >= 300:  # 确保有足够的历史数据
                    window_data = df_with_features.iloc[i-300:i+1]
                    guess, probability = model_manager.predict(window_data, feature_list)
                    
                    if guess is not None:
                        resolve_time = current_time + pd.Timedelta(minutes=max_wait_minutes)
                        current_guess = {
                            'id': f"pred_{current_time.strftime('%Y%m%d_%H%M%S')}",
                            'guess_price': current_price,
                            'guess_direction': guess,
                            'probability': probability,
                            'start_idx': i,
                            'resolve_time': resolve_time
                        }
                        
                        direction_str = "先涨1%" if guess == 1 else "先跌1%"
                        up_target = current_price * 1.01
                        down_target = current_price * 0.99
                        
                        print(f"🔒 模型决策: 预测【{direction_str}】 (信心: {probability:.3f})")
                        print(f"📈 上涨目标: ${up_target:.2f}, 📉 下跌目标: ${down_target:.2f}")
                        print(f"🔒 此猜测将在 {resolve_time} 进行验证")
                        
                        logger.log_message("新预测", f"{current_guess['id']} - {direction_str}", {
                            'probability': probability,
                            'price': current_price,
                            'up_target': up_target,
                            'down_target': down_target
                        })
                    else:
                        print("📊 模型分析: 信心不足，放弃本次预测")
                        logger.log_message("分析结果", "信心不足，放弃预测", {
                            'probability': probability,
                            'price': current_price
                        })
        
        # 模拟时间流逝
        time.sleep(speed)
    
    # 8. 输出最终结果
    print("\n" + "="*25 + " 回测总结 " + "="*25)
    total_trades = wins + losses
    total_guesses = wins + losses + timeouts
    win_rate = (wins / total_trades * 100) if total_trades > 0 else 0
    valid_rate = (total_trades / total_guesses * 100) if total_guesses > 0 else 0
    
    print(f"总得分: {total_score:+d}")
    print(f"总猜测次数: {total_guesses}")
    print(f"有效猜测: {total_trades} (成功: {wins}, 失败: {losses})")
    print(f"超时猜测: {timeouts} (不计分)")
    print(f"成功率: {win_rate:.2f}% (基于有效猜测)")
    print(f"有效率: {valid_rate:.2f}% (有效猜测/总猜测)")
    print("="*64)
    
    logger.log_message("回测完成", "总结", {
        'total_score': total_score,
        'total_guesses': total_guesses,
        'wins': wins,
        'losses': losses,
        'timeouts': timeouts,
        'win_rate': win_rate,
        'valid_rate': valid_rate
    })

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="BTC百分比目标回测系统")
    parser.add_argument("data_file", help="数据文件路径")
    parser.add_argument("--model-name", default="btc_percentage_model", help="模型名称")
    parser.add_argument("--start-row", type=int, default=5000, help="起始行号")
    parser.add_argument("--start-time", type=str, default=None, help="起始时间 (YYYY-MM-DD HH:MM:SS)")
    parser.add_argument("--speed", type=float, default=0.1, help="模拟速度 (秒/行)")
    parser.add_argument("--max-wait-hours", type=float, default=4.0, help="最大等待时间 (小时)")
    parser.add_argument("--mode", choices=["limited", "concurrent"], default="limited", help="回测模式")
    
    args = parser.parse_args()
    
    # 确保日志目录存在
    os.makedirs("logs", exist_ok=True)
    
    run_backtest(
        data_file=args.data_file,
        model_name=args.model_name,
        start_row=args.start_row,
        start_time=args.start_time,
        speed=args.speed,
        max_wait_hours=args.max_wait_hours,
        mode=args.mode
    )

if __name__ == "__main__":
    main()
