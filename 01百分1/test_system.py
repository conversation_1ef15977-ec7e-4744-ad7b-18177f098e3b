#!/usr/bin/env python3
"""
系统测试脚本
验证重构后的各个模块是否正常工作
"""

import sys
import os
import pandas as pd
import numpy as np

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_feature_engine():
    """测试特征工程模块"""
    print("🧪 测试特征工程模块...")
    
    try:
        from core.features import FeatureEngine
        
        # 创建测试数据
        dates = pd.date_range('2025-01-01', periods=1000, freq='1min')
        np.random.seed(42)
        
        test_data = {
            'open': np.random.randn(1000).cumsum() + 100,
            'high': np.random.randn(1000).cumsum() + 102,
            'low': np.random.randn(1000).cumsum() + 98,
            'close': np.random.randn(1000).cumsum() + 100,
            'volume': np.random.randint(1000, 10000, 1000)
        }
        
        df = pd.DataFrame(test_data, index=dates)
        
        # 测试特征计算
        engine = FeatureEngine()
        df_with_features = engine.calculate_features(df)
        
        # 验证特征
        feature_list = engine.get_feature_list()
        is_valid, missing_features = engine.validate_features(df_with_features)
        
        print(f"   ✅ 特征计算完成，共 {len(df_with_features.columns)} 列")
        print(f"   ✅ 重要特征: {len(feature_list)} 个")
        print(f"   ✅ 特征验证: {'通过' if is_valid else '失败'}")
        
        if not is_valid:
            print(f"   ❌ 缺失特征: {missing_features}")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 特征工程测试失败: {e}")
        return False

def test_data_processor():
    """测试数据处理模块"""
    print("🧪 测试数据处理模块...")
    
    try:
        from core.data_processor import DataProcessor
        
        # 创建测试数据
        dates = pd.date_range('2025-01-01', periods=1000, freq='1min')
        np.random.seed(42)
        
        test_data = {
            'Timestamp': [int(d.timestamp()) for d in dates],
            'Open': np.random.randn(1000).cumsum() + 100,
            'High': np.random.randn(1000).cumsum() + 102,
            'Low': np.random.randn(1000).cumsum() + 98,
            'Close': np.random.randn(1000).cumsum() + 100,
            'Volume': np.random.randint(1000, 10000, 1000)
        }
        
        df = pd.DataFrame(test_data)
        
        # 保存测试数据
        test_file = "test_data.csv"
        df.to_csv(test_file, index=False)
        
        # 测试数据处理
        processor = DataProcessor()
        
        # 测试数据加载
        loaded_df = processor.load_data(test_file)
        print(f"   ✅ 数据加载完成，共 {len(loaded_df)} 行")
        
        # 测试数据质量验证
        is_valid = processor.validate_data_quality(loaded_df)
        print(f"   ✅ 数据质量验证: {'通过' if is_valid else '失败'}")
        
        # 测试标签生成
        features_df, labels = processor.prepare_features_and_labels(loaded_df)
        print(f"   ✅ 标签生成完成，有效样本: {len(features_df)}")
        
        # 测试数据分割
        X_train, X_val, X_test, y_train, y_val, y_test = processor.split_data(features_df, labels)
        print(f"   ✅ 数据分割完成: 训练{len(X_train)}, 验证{len(X_val)}, 测试{len(X_test)}")
        
        # 清理测试文件
        os.remove(test_file)
        
        return True
        
    except Exception as e:
        print(f"   ❌ 数据处理测试失败: {e}")
        return False

def test_model_manager():
    """测试模型管理模块"""
    print("🧪 测试模型管理模块...")
    
    try:
        from core.model_manager import ModelManager
        from core.features import FeatureEngine
        
        # 创建测试数据
        np.random.seed(42)
        feature_list = FeatureEngine().get_feature_list()
        n_samples = 1000
        
        # 生成随机特征数据
        X_train = pd.DataFrame(np.random.randn(n_samples, len(feature_list)), columns=feature_list)
        X_val = pd.DataFrame(np.random.randn(200, len(feature_list)), columns=feature_list)
        X_test = pd.DataFrame(np.random.randn(200, len(feature_list)), columns=feature_list)
        
        # 生成随机标签
        y_train = pd.Series(np.random.randint(0, 2, n_samples))
        y_val = pd.Series(np.random.randint(0, 2, 200))
        y_test = pd.Series(np.random.randint(0, 2, 200))
        
        # 测试模型管理
        manager = ModelManager(model_dir="test_models")
        
        # 测试模型训练
        model = manager.train_model(X_train, y_train, X_val, y_val, feature_list)
        print(f"   ✅ 模型训练完成")
        
        # 测试模型评估
        results = manager.evaluate_model(X_test, y_test, feature_list)
        print(f"   ✅ 模型评估完成，准确率: {results['optimal_accuracy']:.3f}")
        
        # 测试特征重要性
        importance_df = manager.get_feature_importance(feature_list)
        print(f"   ✅ 特征重要性计算完成，共 {len(importance_df)} 个特征")
        
        # 测试模型保存
        config_data = {
            'model_type': 'test_model',
            'best_threshold': results['best_threshold'],
            'feature_list': feature_list
        }
        manager.save_model("test_model", config_data)
        print(f"   ✅ 模型保存完成")
        
        # 测试模型加载
        loaded_model, loaded_config = manager.load_model("test_model")
        print(f"   ✅ 模型加载完成")
        
        # 测试预测
        guess, probability = manager.predict(X_test.head(1), feature_list)
        print(f"   ✅ 预测完成: {guess}, 概率: {probability:.3f}")
        
        # 清理测试文件
        import shutil
        if os.path.exists("test_models"):
            shutil.rmtree("test_models")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 模型管理测试失败: {e}")
        return False

def test_utils():
    """测试工具模块"""
    print("🧪 测试工具模块...")
    
    try:
        from utils.logger import Logger, AlertSound
        from utils.data_fetcher import BinanceDataFetcher
        
        # 测试日志记录
        logger = Logger("test_log.txt")
        logger.log_message("测试", "这是一条测试日志", {"test_data": 123})
        print(f"   ✅ 日志记录测试完成")
        
        # 测试报警声（不实际播放）
        alert = AlertSound(enabled=False)
        alert.play_alert()
        print(f"   ✅ 报警声测试完成")
        
        # 测试数据获取器（不实际请求网络）
        fetcher = BinanceDataFetcher()
        print(f"   ✅ 数据获取器初始化完成")
        
        # 清理测试文件
        if os.path.exists("test_log.txt"):
            os.remove("test_log.txt")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 工具模块测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 BTC百分比预测系统测试")
    print("="*50)
    
    # 确保测试目录存在
    os.makedirs("test_models", exist_ok=True)
    
    tests = [
        ("特征工程模块", test_feature_engine),
        ("数据处理模块", test_data_processor),
        ("模型管理模块", test_model_manager),
        ("工具模块", test_utils)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        if test_func():
            passed += 1
            print(f"   ✅ {test_name} 测试通过")
        else:
            print(f"   ❌ {test_name} 测试失败")
    
    print("\n" + "="*50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统重构成功！")
        return True
    else:
        print("❌ 部分测试失败，请检查相关模块")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
