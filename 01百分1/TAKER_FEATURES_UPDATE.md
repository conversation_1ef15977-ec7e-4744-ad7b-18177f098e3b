# 主动买卖成交量特征更新

## 概述

成功为BTC百分比预测系统添加了主动买卖成交量特征，这些特征能够反映市场的真实买卖压力和资金流向，对价格预测具有重要意义。

## 新增特征

### 核心特征 (5个)

1. **taker_buy_ratio**: 主动买入占比
   - 计算公式: `TakerBuyBaseVolume / Volume`
   - 意义: 反映主动买入力量的强弱
   - 范围: 0-1，>0.5表示买盘占优

2. **buy_sell_pressure**: 买卖压力差
   - 计算公式: `(TakerBuyBaseVolume - TakerSellBaseVolume) / Volume`
   - 意义: 正值表示买压大于卖压，负值相反
   - 范围: -1到1

3. **taker_buy_quote_ratio**: 主动买入资金占比
   - 计算公式: `TakerBuyQuoteVolume / QuoteVolume`
   - 意义: 反映主动买入的资金流向
   - 比基础资产占比更能反映大单影响

4. **taker_buy_ratio_ma_10**: 买入比例10期移动平均
   - 计算公式: `taker_buy_ratio`的10期移动平均
   - 意义: 平滑短期波动，识别趋势

5. **buy_sell_pressure_ma_10**: 买卖压力10期移动平均
   - 计算公式: `buy_sell_pressure`的10期移动平均
   - 意义: 平滑买卖压力变化，识别趋势转换

### 辅助特征

系统还计算了以下辅助特征（未包含在核心特征列表中）：

- `taker_sell_ratio`: 主动卖出占比
- `taker_sell_quote_ratio`: 主动卖出资金占比
- `taker_buy_ratio_ma_5/30`: 5期和30期移动平均
- `buy_sell_pressure_ma_5/30`: 5期和30期移动平均
- `taker_buy_quote_ratio_ma_5/10/30`: 资金占比移动平均
- `taker_buy_ratio_change`: 买入比例变化
- `buy_sell_pressure_change`: 买卖压力变化
- `taker_buy_ratio_max/min/std_10/30`: 极值和标准差特征

## 数据来源

### Binance API字段

- **TakerBuyBaseVolume**: 主动买入基础资产成交量（BTC数量）
- **TakerBuyQuoteVolume**: 主动买入计价资产成交量（USDT金额）
- **Volume**: 总成交量（BTC数量）
- **QuoteVolume**: 总成交金额（USDT金额）

### 数据示例

```csv
Timestamp,Volume,TakerBuyBaseVolume,QuoteVolume,TakerBuyQuoteVolume
1747296600,32.28288,18.89504,3292647.9961533,1927102.4579027
```

## 特征意义

### 市场微观结构

主动买卖成交量反映了市场的微观结构：

1. **主动买入**: 市场参与者愿意以当前卖价成交
2. **主动卖出**: 市场参与者愿意以当前买价成交
3. **比例变化**: 反映市场情绪和预期的变化

### 预测价值

1. **方向预测**: 主动买入占比高通常预示价格上涨
2. **强度预测**: 买卖压力差的大小反映价格变动的强度
3. **资金流向**: 资金占比比数量占比更能反映大资金的意图
4. **趋势识别**: 移动平均特征帮助识别趋势变化

## 技术实现

### 特征计算

```python
def _add_taker_volume_features(self, df: pd.DataFrame) -> pd.DataFrame:
    """添加主动买卖成交量特征"""
    # 1. 买卖比例特征
    df['taker_buy_ratio'] = df['TakerBuyBaseVolume'] / (df['volume'] + self.epsilon)
    df['taker_sell_ratio'] = (df['volume'] - df['TakerBuyBaseVolume']) / (df['volume'] + self.epsilon)
    
    # 2. 买卖压力指标
    df['buy_sell_pressure'] = (df['TakerBuyBaseVolume'] - (df['volume'] - df['TakerBuyBaseVolume'])) / (df['volume'] + self.epsilon)
    
    # 3. 资金流向特征
    df['taker_buy_quote_ratio'] = df['TakerBuyQuoteVolume'] / (df['QuoteVolume'] + self.epsilon)
    
    # 4. 移动平均特征
    for window in [5, 10, 30]:
        df[f'taker_buy_ratio_ma_{window}'] = df['taker_buy_ratio'].rolling(window=window).mean()
        df[f'buy_sell_pressure_ma_{window}'] = df['buy_sell_pressure'].rolling(window=window).mean()
    
    return df
```

### 特征列表更新

特征数量从20个扩展到25个：

```python
def get_feature_list(self) -> list:
    return [
        # 原有20个特征...
        
        # 新增主动买卖成交量特征 (5个)
        'taker_buy_ratio',              # 主动买入占比
        'buy_sell_pressure',            # 买卖压力差
        'taker_buy_quote_ratio',        # 主动买入资金占比
        'taker_buy_ratio_ma_10',        # 买入比例10期移动平均
        'buy_sell_pressure_ma_10'       # 买卖压力10期移动平均
    ]
```

## 系统集成

### 自动检测

系统会自动检测数据中是否包含主动买卖成交量字段：

```python
required_cols = ['TakerBuyBaseVolume', 'TakerBuyQuoteVolume', 'volume', 'QuoteVolume']
missing_cols = [col for col in required_cols if col not in df.columns]

if missing_cols:
    print(f"⚠️  缺少主动买卖成交量列: {missing_cols}，跳过相关特征")
    return df
```

### 向后兼容

- 如果数据中没有主动买卖成交量字段，系统会跳过相关特征计算
- 不会影响其他特征的正常计算
- 保持系统的稳定性

## 使用方法

### 训练新模型

```bash
# 使用包含主动买卖成交量特征的新模型
cd 01百分1
python train.py --data-file data/btcusd_1-min_data.csv --model-name taker_model
```

### 回测验证

```bash
# 验证新特征的效果
python backtest.py data/btcusd_1-min_data.csv --model-name taker_model --mode limited
```

### 实时预测

```bash
# 使用新特征进行实时预测
python realtime.py --model-name taker_model --max-wait-hours 4
```

## 预期效果

### 模型性能提升

1. **更准确的方向预测**: 主动买卖信息提供更直接的市场情绪指标
2. **更好的时机把握**: 买卖压力变化帮助识别转折点
3. **更强的泛化能力**: 资金流向特征在不同市场条件下更稳定

### 特征重要性

预期新增的主动买卖成交量特征将在特征重要性排名中占据重要位置，特别是：

- `taker_buy_ratio`: 预期进入前10重要特征
- `buy_sell_pressure`: 预期具有较高的预测价值
- `taker_buy_quote_ratio`: 预期在大额交易时表现突出

## 后续优化

### 可能的改进方向

1. **时间窗口优化**: 测试不同的移动平均窗口大小
2. **组合特征**: 创建主动买卖特征与价格特征的组合
3. **异常检测**: 识别异常的买卖行为模式
4. **多时间框架**: 结合不同时间框架的主动买卖数据

### 监控指标

1. **特征稳定性**: 监控特征值的分布变化
2. **预测贡献**: 分析新特征对预测结果的贡献度
3. **市场适应性**: 在不同市场条件下的表现

## 总结

主动买卖成交量特征的添加为BTC价格预测系统提供了重要的市场微观结构信息，预期将显著提升模型的预测能力。这些特征反映了市场参与者的真实意图，比传统的价格和成交量指标更具预测价值。

通过模块化的设计，新特征无缝集成到现有系统中，保持了训练、测试、实时执行的一致性，为系统的持续优化奠定了基础。
