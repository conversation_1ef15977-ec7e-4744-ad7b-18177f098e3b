#!/usr/bin/env python3
"""
多币种暴涨信号系统主程序
"""

import argparse
import logging
import os
import sys
import time
from datetime import datetime
from typing import Dict, List, Optional

from config import Config
from data_collector import CryptoDataCollector
from technical_indicators import TechnicalIndicators
from ml_predictor import MLPredictor
from signal_generator import SignalGenerator
from risk_manager import RiskManager
from multi_coin_monitor import MultiCoinMonitor
from train_models import MultiCoinTrainer

def setup_environment():
    """设置环境"""
    config = Config()
    
    # 创建必要的目录
    for dir_path in [config.DATA_DIR, config.MODELS_DIR, config.LOGS_DIR]:
        os.makedirs(dir_path, exist_ok=True)
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(f"{config.LOGS_DIR}/main.log"),
            logging.StreamHandler()
        ]
    )
    
    # 验证配置
    if not config.validate_config():
        logging.error("配置验证失败，程序退出")
        sys.exit(1)
    
    return config

def download_data(symbols: List[str] = None, days: int = 30):
    """下载历史数据"""
    config = Config()
    symbols = symbols or config.SYMBOLS
    
    logging.info(f"📥 开始下载 {len(symbols)} 个币种的历史数据")
    
    collector = CryptoDataCollector()
    
    for i, symbol in enumerate(symbols, 1):
        logging.info(f"[{i}/{len(symbols)}] 下载 {symbol} 数据...")
        
        try:
            # 计算需要的数据点数量 (每天1440分钟)
            limit = days * 1440
            
            # 下载数据
            data = collector.fetch_ohlcv_sync(symbol, '1m', limit)
            
            if not data.empty:
                # 保存数据
                collector.save_data(data, symbol)
                logging.info(f"✅ {symbol} 数据下载完成: {len(data)} 条记录")
            else:
                logging.error(f"❌ {symbol} 数据下载失败: 返回空数据")
                
        except Exception as e:
            logging.error(f"❌ {symbol} 数据下载出错: {e}")
        
        # 避免请求过快
        time.sleep(1)
    
    logging.info("📥 数据下载完成")

def analyze_single_coin(symbol: str, show_details: bool = False):
    """分析单个币种"""
    config = Config()
    
    if symbol not in config.SYMBOLS:
        logging.warning(f"⚠️ {symbol} 不在配置的币种列表中")
    
    logging.info(f"🔍 开始分析 {symbol}")
    
    try:
        # 加载数据
        collector = CryptoDataCollector()
        data = collector.load_data(symbol)
        
        if data.empty:
            logging.error(f"❌ {symbol} 数据为空，尝试下载...")
            data = collector.fetch_ohlcv_sync(symbol, '1m', 1000)
            
            if not data.empty:
                collector.save_data(data, symbol)
            else:
                logging.error(f"❌ 无法获取 {symbol} 数据")
                return
        
        # 计算技术指标
        indicators = TechnicalIndicators()
        data_with_indicators = indicators.calculate_all_indicators(data)
        
        # 检测暴涨信号
        surge_signals = indicators.detect_surge_signals(data_with_indicators)
        
        # 生成综合信号
        signal_gen = SignalGenerator()
        signal = signal_gen.generate_composite_signal(data_with_indicators, symbol)
        
        # 打印结果
        print(f"\n📊 {symbol} 分析结果:")
        print(f"信号: {signal['signal']} ({signal['action']})")
        print(f"置信度: {signal['confidence']:.4f}")
        print(f"得分: {signal['score']:.4f}")
        
        if surge_signals:
            print("\n🚀 暴涨信号:")
            for name, strength in surge_signals.items():
                print(f"  - {name}: {strength:.2f}")
        
        if show_details and 'details' in signal:
            print("\n📝 详细信号:")
            for name, detail in signal['details'].items():
                print(f"  - {name}: {detail['signal']} (强度: {detail['strength']:.2f})")
                print(f"    原因: {detail['reason']}")
        
        # 获取最新价格
        latest_price = data['Close'].iloc[-1]
        print(f"\n💰 最新价格: {latest_price:.6f}")
        
        # 风险评估
        risk_mgr = RiskManager()
        position_info = risk_mgr.calculate_position_size(signal, 10000, latest_price)
        is_valid, reason = risk_mgr.validate_trade(position_info)
        
        print("\n⚖️ 风险评估:")
        if is_valid:
            print(f"✅ 交易有效")
            print(f"建议仓位: {position_info['quantity']:.4f} {symbol}")
            print(f"止损价: {position_info['stop_loss']:.6f}")
            print(f"止盈价: {position_info['take_profit']:.6f}")
        else:
            print(f"❌ 交易无效: {reason}")
        
    except Exception as e:
        logging.error(f"❌ 分析 {symbol} 时出错: {e}")

def start_monitoring(symbols: List[str] = None):
    """启动监控系统"""
    config = Config()
    symbols = symbols or config.SYMBOLS
    
    logging.info(f"🚀 启动监控系统，监控 {len(symbols)} 个币种")
    
    # 创建监控器
    monitor = MultiCoinMonitor()
    
    try:
        # 启动监控
        monitor.start_monitoring()
    except KeyboardInterrupt:
        logging.info("收到停止信号，正在关闭监控系统...")
        monitor.stop_monitoring()
    except Exception as e:
        logging.error(f"监控系统出错: {e}")
        monitor.stop_monitoring()

def train_models(symbols: List[str] = None, retrain: bool = False):
    """训练模型"""
    config = Config()
    symbols = symbols or config.SYMBOLS
    
    logging.info(f"🧠 开始训练 {len(symbols)} 个币种的模型")
    
    trainer = MultiCoinTrainer()
    trainer.train_all_models(symbols, retrain)

def main():
    """主函数"""
    # 设置环境
    config = setup_environment()
    
    # 命令行参数
    parser = argparse.ArgumentParser(description='多币种暴涨信号系统')
    parser.add_argument('--download', action='store_true', help='下载历史数据')
    parser.add_argument('--days', type=int, default=30, help='下载数据的天数')
    parser.add_argument('--analyze', type=str, help='分析单个币种')
    parser.add_argument('--details', action='store_true', help='显示详细分析结果')
    parser.add_argument('--monitor', action='store_true', help='启动监控系统')
    parser.add_argument('--train', action='store_true', help='训练模型')
    parser.add_argument('--retrain', action='store_true', help='重新训练已存在的模型')
    parser.add_argument('--symbols', nargs='+', help='指定要处理的币种')
    
    args = parser.parse_args()
    
    # 处理命令
    if args.download:
        download_data(args.symbols, args.days)
    elif args.analyze:
        analyze_single_coin(args.analyze, args.details)
    elif args.train:
        train_models(args.symbols, args.retrain)
    elif args.monitor:
        start_monitoring(args.symbols)
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
