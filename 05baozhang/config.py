#!/usr/bin/env python3
"""
多币种暴涨信号系统配置文件
"""

import os
from typing import Dict, List, Any

class Config:
    """系统配置类"""
    
    # 基础配置
    BASE_DIR = os.path.dirname(os.path.abspath(__file__))
    DATA_DIR = os.path.join(BASE_DIR, 'data')
    MODELS_DIR = os.path.join(BASE_DIR, 'models')
    LOGS_DIR = os.path.join(BASE_DIR, 'logs')
    
    # 确保目录存在
    for dir_path in [DATA_DIR, MODELS_DIR, LOGS_DIR]:
        os.makedirs(dir_path, exist_ok=True)
    
    # 交易所配置
    EXCHANGES = {
        'binance': {
            'api_key': '',  # 在实际使用时填入
            'secret': '',   # 在实际使用时填入
            'sandbox': True,
            'base_url': 'https://api.binance.com'
        }
    }
    
    # 监控币种配置
    SYMBOLS = [
        'BTCUSDT',   # BTC
        'ETHUSDT',   # ETH
        'SOLUSDT',   # SOL
        'SUIUSDT',   # SUI
        'PENGUUSDT', # PENGU
        'ADAUSDT',   # ADA
        'DOTUSDT',   # DOT
        'LINKUSDT',  # LINK
        'AVAXUSDT',  # AVAX
        'MATICUSDT'  # MATIC
    ]
    
    # 数据获取配置
    DATA_CONFIG = {
        'interval': '1m',           # K线间隔
        'limit': 1000,             # 历史数据条数
        'update_interval': 60,      # 数据更新间隔(秒)
        'request_timeout': 30,      # 请求超时时间
        'retry_times': 3,           # 重试次数
        'retry_delay': 5            # 重试延迟(秒)
    }
    
    # 技术指标配置
    TECHNICAL_CONFIG = {
        'ma_periods': [7, 14, 30, 50, 200],    # 移动平均线周期
        'rsi_period': 14,                       # RSI周期
        'macd_fast': 12,                        # MACD快线
        'macd_slow': 26,                        # MACD慢线
        'macd_signal': 9,                       # MACD信号线
        'bb_period': 20,                        # 布林带周期
        'bb_std': 2,                           # 布林带标准差
        'volume_ma_period': 20,                # 成交量均线周期
        'atr_period': 14,                      # ATR周期
        'support_resistance_window': 20         # 支撑阻力窗口
    }
    
    # 机器学习配置
    ML_CONFIG = {
        'model_type': 'lightgbm',
        'prediction_horizon': 6,        # 预测时间窗口
        'threshold': 0.02,              # 涨跌阈值(2%)
        'confidence_threshold': 0.6,    # 置信度阈值
        'train_test_split': 0.8,        # 训练测试分割比例
        'model_params': {
            'objective': 'multiclass',
            'num_class': 3,
            'learning_rate': 0.1,
            'n_estimators': 200,
            'max_depth': 6,
            'random_state': 42,
            'verbose': -1
        }
    }
    
    # 信号生成配置
    SIGNAL_CONFIG = {
        'weights': {
            'technical': 0.4,   # 技术指标权重
            'ml': 0.3,          # 机器学习权重
            'market': 0.2,      # 市场结构权重
            'risk': 0.1         # 风险权重
        },
        'thresholds': {
            'buy': 0.3,         # 买入阈值
            'sell': -0.3,       # 卖出阈值
            'strong_buy': 0.6,  # 强买入阈值
            'strong_sell': -0.6 # 强卖出阈值
        },
        'min_confidence': 0.5   # 最小置信度
    }
    
    # 风险管理配置
    RISK_CONFIG = {
        'max_position_size': 0.1,       # 最大仓位10%
        'stop_loss_pct': 0.05,          # 止损5%
        'take_profit_pct': 0.15,        # 止盈15%
        'max_daily_loss': 0.02,         # 日最大亏损2%
        'max_drawdown': 0.10,           # 最大回撤10%
        'volatility_threshold': 2.0,     # 波动率阈值
        'volume_threshold': 2.0          # 成交量阈值
    }
    
    # 暴涨信号特殊配置
    SURGE_CONFIG = {
        'surge_threshold': 0.05,        # 暴涨阈值5%
        'surge_timeframe': 30,          # 暴涨时间窗口(分钟)
        'volume_surge_ratio': 3.0,      # 成交量暴增比例
        'breakout_threshold': 0.98,     # 突破阈值(布林带上轨98%)
        'momentum_threshold': 0.8,      # 动量阈值
        'min_market_cap': 1000000000,   # 最小市值(10亿美元)
        'max_coins_per_signal': 3       # 每次最多推荐币种数
    }
    
    # 监控配置
    MONITOR_CONFIG = {
        'scan_interval': 300,           # 扫描间隔(秒)
        'alert_cooldown': 3600,         # 报警冷却时间(秒)
        'max_alerts_per_hour': 5,       # 每小时最大报警数
        'enable_sound': True,           # 启用声音报警
        'enable_telegram': False,       # 启用Telegram通知
        'enable_email': False           # 启用邮件通知
    }
    
    # 日志配置
    LOG_CONFIG = {
        'level': 'INFO',
        'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        'file_max_size': 10 * 1024 * 1024,  # 10MB
        'backup_count': 5,
        'console_output': True
    }
    
    # 通知配置
    NOTIFICATION_CONFIG = {
        'telegram': {
            'bot_token': '',
            'chat_id': '',
            'enabled': False
        },
        'email': {
            'smtp_server': 'smtp.gmail.com',
            'smtp_port': 587,
            'username': '',
            'password': '',
            'to_email': '',
            'enabled': False
        }
    }

    @classmethod
    def get_symbol_config(cls, symbol: str) -> Dict[str, Any]:
        """获取特定币种的配置"""
        base_config = {
            'symbol': symbol,
            'exchange': 'binance',
            'interval': cls.DATA_CONFIG['interval'],
            'min_volume': 1000000,  # 最小24h成交量
            'min_price': 0.001,     # 最小价格
            'max_price': 100000     # 最大价格
        }
        
        # 针对不同币种的特殊配置
        special_configs = {
            'BTCUSDT': {
                'min_volume': 100000000,
                'min_price': 10000,
                'max_price': 200000
            },
            'ETHUSDT': {
                'min_volume': 50000000,
                'min_price': 1000,
                'max_price': 10000
            },
            'SOLUSDT': {
                'min_volume': 10000000,
                'min_price': 10,
                'max_price': 1000
            }
        }
        
        if symbol in special_configs:
            base_config.update(special_configs[symbol])
            
        return base_config
    
    @classmethod
    def validate_config(cls) -> bool:
        """验证配置有效性"""
        try:
            # 检查必要目录
            for dir_path in [cls.DATA_DIR, cls.MODELS_DIR, cls.LOGS_DIR]:
                if not os.path.exists(dir_path):
                    print(f"警告: 目录不存在 {dir_path}")
                    return False
            
            # 检查币种配置
            if not cls.SYMBOLS:
                print("错误: 未配置监控币种")
                return False
            
            # 检查阈值配置
            if cls.ML_CONFIG['confidence_threshold'] <= 0 or cls.ML_CONFIG['confidence_threshold'] >= 1:
                print("错误: 置信度阈值配置无效")
                return False
                
            print("✅ 配置验证通过")
            return True
            
        except Exception as e:
            print(f"❌ 配置验证失败: {e}")
            return False
