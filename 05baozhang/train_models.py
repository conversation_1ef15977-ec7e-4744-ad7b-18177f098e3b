#!/usr/bin/env python3
"""
多币种模型训练脚本
为每个币种训练专门的预测模型
"""

import pandas as pd
import numpy as np
import os
import logging
import json
from datetime import datetime
from typing import Dict, List, Optional
import argparse

from config import Config
from data_collector import CryptoDataCollector
from ml_predictor import MLPredictor
from technical_indicators import TechnicalIndicators

class MultiCoinTrainer:
    """多币种模型训练器"""
    
    def __init__(self):
        self.config = Config()
        self.data_collector = CryptoDataCollector()
        self.indicators = TechnicalIndicators()
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(f"{self.config.LOGS_DIR}/training.log"),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # 训练结果
        self.training_results = {}
    
    def train_all_models(self, symbols: List[str] = None, retrain: bool = False):
        """训练所有币种的模型"""
        symbols = symbols or self.config.SYMBOLS
        
        self.logger.info(f"🚀 开始训练 {len(symbols)} 个币种的模型")
        
        for i, symbol in enumerate(symbols, 1):
            self.logger.info(f"📊 [{i}/{len(symbols)}] 训练 {symbol} 模型...")
            
            try:
                # 检查是否已有模型
                model_path = f"{self.config.MODELS_DIR}/{symbol}_lightgbm_model.joblib"
                if os.path.exists(model_path) and not retrain:
                    self.logger.info(f"⏭️ {symbol} 模型已存在，跳过训练")
                    continue
                
                # 训练模型
                result = self.train_single_model(symbol)
                self.training_results[symbol] = result
                
                if result['success']:
                    self.logger.info(f"✅ {symbol} 模型训练完成，准确率: {result['accuracy']:.4f}")
                else:
                    self.logger.error(f"❌ {symbol} 模型训练失败: {result['error']}")
                    
            except Exception as e:
                self.logger.error(f"❌ {symbol} 训练过程出错: {e}")
                self.training_results[symbol] = {'success': False, 'error': str(e)}
        
        # 生成训练报告
        self._generate_training_report()
    
    def train_single_model(self, symbol: str) -> Dict:
        """训练单个币种的模型"""
        try:
            # 1. 加载数据
            data = self._load_training_data(symbol)
            if data.empty:
                return {'success': False, 'error': '数据为空'}
            
            # 2. 数据质量检查
            quality_report = self.data_collector.get_data_quality_report(data)
            if quality_report['status'] != 'complete':
                return {'success': False, 'error': f'数据质量问题: {quality_report}'}
            
            # 3. 创建预测器
            predictor = MLPredictor(model_type='lightgbm')
            
            # 4. 训练模型
            accuracy = predictor.train_model(data)
            
            # 5. 保存模型
            predictor.save_model(symbol=symbol)
            
            # 6. 获取模型信息
            model_info = predictor.get_model_info()
            
            return {
                'success': True,
                'accuracy': accuracy,
                'data_records': len(data),
                'model_info': model_info,
                'training_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _load_training_data(self, symbol: str) -> pd.DataFrame:
        """加载训练数据"""
        try:
            # 尝试从文件加载
            data = self.data_collector.load_data(symbol)
            
            if data.empty:
                self.logger.info(f"本地无 {symbol} 数据，从API获取...")
                # 从API获取更多历史数据
                data = self.data_collector.fetch_ohlcv_sync(
                    symbol, 
                    self.config.DATA_CONFIG['interval'],
                    5000  # 获取更多数据用于训练
                )
                
                if not data.empty:
                    self.data_collector.save_data(data, symbol)
            
            # 数据预处理
            if not data.empty:
                # 确保时间戳列
                if 'Timestamp' in data.columns:
                    data['Timestamp'] = pd.to_datetime(data['Timestamp'])
                    data = data.set_index('Timestamp')
                
                # 删除重复数据
                data = data.drop_duplicates()
                
                # 按时间排序
                data = data.sort_index()
                
                self.logger.info(f"{symbol} 数据加载完成: {len(data)} 条记录")
            
            return data
            
        except Exception as e:
            self.logger.error(f"加载 {symbol} 数据失败: {e}")
            return pd.DataFrame()
    
    def _generate_training_report(self):
        """生成训练报告"""
        report = {
            'training_time': datetime.now().isoformat(),
            'total_symbols': len(self.training_results),
            'successful_trainings': len([r for r in self.training_results.values() if r['success']]),
            'failed_trainings': len([r for r in self.training_results.values() if not r['success']]),
            'results': self.training_results
        }
        
        # 计算统计信息
        successful_results = [r for r in self.training_results.values() if r['success']]
        if successful_results:
            accuracies = [r['accuracy'] for r in successful_results]
            report['statistics'] = {
                'avg_accuracy': np.mean(accuracies),
                'min_accuracy': np.min(accuracies),
                'max_accuracy': np.max(accuracies),
                'std_accuracy': np.std(accuracies)
            }
        
        # 保存报告
        report_file = f"{self.config.LOGS_DIR}/training_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        
        # 打印摘要
        self.logger.info("📋 训练报告摘要:")
        self.logger.info(f"   总币种数: {report['total_symbols']}")
        self.logger.info(f"   成功训练: {report['successful_trainings']}")
        self.logger.info(f"   失败训练: {report['failed_trainings']}")
        
        if 'statistics' in report:
            stats = report['statistics']
            self.logger.info(f"   平均准确率: {stats['avg_accuracy']:.4f}")
            self.logger.info(f"   准确率范围: {stats['min_accuracy']:.4f} - {stats['max_accuracy']:.4f}")
        
        self.logger.info(f"详细报告已保存: {report_file}")
    
    def evaluate_models(self, symbols: List[str] = None) -> Dict:
        """评估已训练的模型"""
        symbols = symbols or self.config.SYMBOLS
        evaluation_results = {}
        
        self.logger.info(f"🔍 开始评估 {len(symbols)} 个币种的模型")
        
        for symbol in symbols:
            try:
                model_path = f"{self.config.MODELS_DIR}/{symbol}_lightgbm_model.joblib"
                
                if not os.path.exists(model_path):
                    evaluation_results[symbol] = {'error': '模型文件不存在'}
                    continue
                
                # 加载模型
                predictor = MLPredictor()
                predictor.load_model(model_path)
                
                # 加载测试数据
                data = self._load_training_data(symbol)
                if data.empty:
                    evaluation_results[symbol] = {'error': '测试数据为空'}
                    continue
                
                # 进行预测测试
                prediction_result = predictor.predict(data)
                model_info = predictor.get_model_info()
                
                evaluation_results[symbol] = {
                    'model_info': model_info,
                    'latest_prediction': prediction_result,
                    'data_records': len(data)
                }
                
                self.logger.info(f"✅ {symbol} 模型评估完成")
                
            except Exception as e:
                self.logger.error(f"❌ {symbol} 模型评估失败: {e}")
                evaluation_results[symbol] = {'error': str(e)}
        
        return evaluation_results
    
    def update_models(self, symbols: List[str] = None, min_accuracy: float = 0.6):
        """更新表现不佳的模型"""
        symbols = symbols or self.config.SYMBOLS
        
        self.logger.info(f"🔄 检查需要更新的模型 (最低准确率: {min_accuracy})")
        
        # 评估现有模型
        evaluation_results = self.evaluate_models(symbols)
        
        # 找出需要更新的模型
        symbols_to_update = []
        for symbol, result in evaluation_results.items():
            if 'error' in result:
                symbols_to_update.append(symbol)
                self.logger.info(f"🔄 {symbol} 需要重新训练: {result['error']}")
        
        # 重新训练
        if symbols_to_update:
            self.train_all_models(symbols_to_update, retrain=True)
        else:
            self.logger.info("✅ 所有模型都符合要求，无需更新")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='多币种模型训练脚本')
    parser.add_argument('--symbols', nargs='+', help='指定要训练的币种')
    parser.add_argument('--retrain', action='store_true', help='重新训练已存在的模型')
    parser.add_argument('--evaluate', action='store_true', help='评估已训练的模型')
    parser.add_argument('--update', action='store_true', help='更新表现不佳的模型')
    
    args = parser.parse_args()
    
    trainer = MultiCoinTrainer()
    
    if args.evaluate:
        # 评估模型
        results = trainer.evaluate_models(args.symbols)
        print("\n📊 模型评估结果:")
        for symbol, result in results.items():
            if 'error' in result:
                print(f"❌ {symbol}: {result['error']}")
            else:
                print(f"✅ {symbol}: 特征数量 {result['model_info']['feature_count']}")
    
    elif args.update:
        # 更新模型
        trainer.update_models(args.symbols)
    
    else:
        # 训练模型
        trainer.train_all_models(args.symbols, args.retrain)

if __name__ == "__main__":
    main()
