#!/usr/bin/env python3
"""
交易信号生成模块
基于README.md中的SignalGenerator设计
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging

from config import Config
from technical_indicators import TechnicalIndicators
from ml_predictor import MLPredictor

class SignalGenerator:
    """交易信号生成器"""

    def __init__(self, config: Optional[Dict] = None):
        self.config = config or Config.SIGNAL_CONFIG
        self.ml_predictor = MLPredictor()
        self.indicators = TechnicalIndicators()
        self.signals_history = []
        self.logger = logging.getLogger(__name__)

    def generate_composite_signal(self, df: pd.DataFrame, symbol: str) -> Dict:
        """生成综合交易信号"""
        try:
            # 计算所有技术指标
            df = self.indicators.calculate_all_indicators(df)

            # 获取最新数据
            latest = df.iloc[-1]

            # 1. 技术指标信号
            technical_signals = self._get_technical_signals(df, latest)

            # 2. 机器学习信号
            ml_signals = self._get_ml_signals(df)

            # 3. 市场结构信号
            market_signals = self._get_market_structure_signals(df, latest)

            # 4. 风险信号
            risk_signals = self._get_risk_signals(df, latest)

            # 5. 暴涨特殊信号
            surge_signals = self._get_surge_signals(df, latest)

            # 6. 综合信号计算
            composite_signal = self._calculate_composite_signal(
                technical_signals, ml_signals, market_signals, risk_signals, surge_signals
            )

            # 7. 生成最终信号
            final_signal = self._generate_final_signal(composite_signal, symbol)

            return final_signal

        except Exception as e:
            self.logger.error(f"生成信号时出错: {e}")
            return self._empty_signal()

    def _get_technical_signals(self, df: pd.DataFrame, latest: pd.Series) -> Dict:
        """获取技术指标信号"""
        signals = {}

        # RSI信号
        if 'RSI' in latest.index and pd.notna(latest['RSI']):
            if latest['RSI'] < 30:
                signals['RSI'] = {'signal': 'BUY', 'strength': 0.8, 'reason': 'RSI超卖'}
            elif latest['RSI'] > 70:
                signals['RSI'] = {'signal': 'SELL', 'strength': 0.8, 'reason': 'RSI超买'}
            else:
                signals['RSI'] = {'signal': 'HOLD', 'strength': 0.3, 'reason': 'RSI中性'}

        # MACD信号
        if 'MACD_bullish' in latest.index and 'MACD_bearish' in latest.index:
            if pd.notna(latest['MACD_bullish']) and latest['MACD_bullish']:
                signals['MACD'] = {'signal': 'BUY', 'strength': 0.7, 'reason': 'MACD金叉'}
            elif pd.notna(latest['MACD_bearish']) and latest['MACD_bearish']:
                signals['MACD'] = {'signal': 'SELL', 'strength': 0.7, 'reason': 'MACD死叉'}
            else:
                signals['MACD'] = {'signal': 'HOLD', 'strength': 0.2, 'reason': 'MACD中性'}

        # 布林带信号
        if 'BB_position' in latest.index and pd.notna(latest['BB_position']):
            if latest['BB_position'] < 0.1:
                signals['BB'] = {'signal': 'BUY', 'strength': 0.6, 'reason': '价格触及布林带下轨'}
            elif latest['BB_position'] > 0.9:
                signals['BB'] = {'signal': 'SELL', 'strength': 0.6, 'reason': '价格触及布林带上轨'}
            else:
                signals['BB'] = {'signal': 'HOLD', 'strength': 0.2, 'reason': '布林带中性'}

        # 移动平均线信号
        ma_cols = ['Close', 'SMA_7', 'SMA_14', 'SMA_30']
        if all(col in latest.index for col in ma_cols) and all(pd.notna(latest[col]) for col in ma_cols):
            if latest['Close'] > latest['SMA_7'] > latest['SMA_14'] > latest['SMA_30']:
                signals['MA'] = {'signal': 'BUY', 'strength': 0.8, 'reason': '多头排列'}
            elif latest['Close'] < latest['SMA_7'] < latest['SMA_14'] < latest['SMA_30']:
                signals['MA'] = {'signal': 'SELL', 'strength': 0.8, 'reason': '空头排列'}
            else:
                signals['MA'] = {'signal': 'HOLD', 'strength': 0.3, 'reason': '均线混乱'}

        # ADX趋势强度信号
        adx_cols = ['ADX', 'PLUS_DI', 'MINUS_DI']
        if all(col in latest.index for col in adx_cols) and all(pd.notna(latest[col]) for col in adx_cols):
            if latest['ADX'] > 25:
                if latest['PLUS_DI'] > latest['MINUS_DI']:
                    signals['ADX'] = {'signal': 'BUY', 'strength': 0.6, 'reason': '强势上涨趋势'}
                else:
                    signals['ADX'] = {'signal': 'SELL', 'strength': 0.6, 'reason': '强势下跌趋势'}
            else:
                signals['ADX'] = {'signal': 'HOLD', 'strength': 0.2, 'reason': '趋势不明显'}

        return signals

    def _get_ml_signals(self, df: pd.DataFrame) -> Dict:
        """获取机器学习信号"""
        try:
            prediction_result = self.ml_predictor.predict(df)

            return {
                'ML': {
                    'signal': prediction_result['signal'],
                    'strength': prediction_result['confidence'],
                    'reason': f"ML预测({prediction_result['confidence']:.2f}置信度)"
                }
            }
        except Exception as e:
            self.logger.error(f"ML预测失败: {e}")
            return {'ML': {'signal': 'HOLD', 'strength': 0.1, 'reason': 'ML预测失败'}}

    def _get_market_structure_signals(self, df: pd.DataFrame, latest: pd.Series) -> Dict:
        """获取市场结构信号"""
        signals = {}

        # 成交量信号
        if 'volume_ratio' in latest.index and pd.notna(latest['volume_ratio']):
            if latest['volume_ratio'] > 2.0:
                signals['VOLUME'] = {'signal': 'BUY', 'strength': 0.6, 'reason': '成交量放大'}
            elif latest['volume_ratio'] < 0.5:
                signals['VOLUME'] = {'signal': 'HOLD', 'strength': 0.2, 'reason': '成交量萎缩'}
            else:
                signals['VOLUME'] = {'signal': 'HOLD', 'strength': 0.1, 'reason': '成交量正常'}

        # 支撑阻力信号
        support_cols = ['Close', 'support', 'resistance']
        if all(col in latest.index for col in support_cols) and all(pd.notna(latest[col]) for col in support_cols):
            current_price = latest['Close']
            support = latest['support']
            resistance = latest['resistance']

            if current_price <= support * 1.02:
                signals['SUPPORT'] = {'signal': 'BUY', 'strength': 0.7, 'reason': '价格接近支撑位'}
            elif current_price >= resistance * 0.98:
                signals['SUPPORT'] = {'signal': 'SELL', 'strength': 0.7, 'reason': '价格接近阻力位'}
            else:
                signals['SUPPORT'] = {'signal': 'HOLD', 'strength': 0.2, 'reason': '价格在支撑阻力区间'}

        # 价格形态信号
        pattern_score = 0
        pattern_reasons = []

        for pattern in ['hammer', 'morning_star', 'engulfing']:
            if pattern in latest.index and pd.notna(latest[pattern]) and latest[pattern] > 0:
                pattern_score += 0.3
                pattern_reasons.append(pattern)

        if pattern_score > 0:
            signals['PATTERN'] = {
                'signal': 'BUY',
                'strength': min(pattern_score, 0.8),
                'reason': f'看涨形态: {", ".join(pattern_reasons)}'
            }

        return signals

    def _get_risk_signals(self, df: pd.DataFrame, latest: pd.Series) -> Dict:
        """获取风险信号"""
        signals = {}

        # 波动率信号
        if 'volatility_ratio' in latest.index and pd.notna(latest['volatility_ratio']):
            if latest['volatility_ratio'] > 2.0:
                signals['VOLATILITY'] = {'signal': 'CAUTION', 'strength': 0.8, 'reason': '波动率过高'}
            elif latest['volatility_ratio'] < 0.5:
                signals['VOLATILITY'] = {'signal': 'NORMAL', 'strength': 0.2, 'reason': '波动率正常'}
            else:
                signals['VOLATILITY'] = {'signal': 'NORMAL', 'strength': 0.1, 'reason': '波动率适中'}

        # 流动性风险
        if 'Volume' in latest.index and 'Volume' in df.columns and len(df) > 20:
            volume_mean = df['Volume'].rolling(20).mean().iloc[-1]
            if pd.notna(volume_mean) and latest['Volume'] < volume_mean * 0.3:
                signals['LIQUIDITY'] = {'signal': 'CAUTION', 'strength': 0.6, 'reason': '流动性不足'}
            else:
                signals['LIQUIDITY'] = {'signal': 'NORMAL', 'strength': 0.1, 'reason': '流动性正常'}

        return signals

    def _get_surge_signals(self, df: pd.DataFrame, latest: pd.Series) -> Dict:
        """获取暴涨特殊信号"""
        surge_signals = self.indicators.detect_surge_signals(df)

        # 转换为标准格式
        formatted_signals = {}
        for signal_name, strength in surge_signals.items():
            formatted_signals[signal_name] = {
                'signal': 'BUY',
                'strength': strength,
                'reason': f'暴涨信号: {signal_name}'
            }

        return formatted_signals

    def _calculate_composite_signal(self, technical_signals: Dict, ml_signals: Dict,
                                  market_signals: Dict, risk_signals: Dict, surge_signals: Dict) -> Dict:
        """计算综合信号"""
        # 权重配置
        weights = self.config['weights'].copy()
        weights['surge'] = 0.2  # 暴涨信号权重

        # 重新归一化权重
        total_weight = sum(weights.values())
        for key in weights:
            weights[key] /= total_weight

        # 信号值映射
        signal_values = {'BUY': 1, 'HOLD': 0, 'SELL': -1, 'CAUTION': 0, 'NORMAL': 0}

        # 计算加权得分
        total_score = 0
        total_weight = 0
        signal_details = {}

        # 处理各类信号
        signal_groups = [
            (technical_signals, 'technical'),
            (ml_signals, 'ml'),
            (market_signals, 'market'),
            (risk_signals, 'risk'),
            (surge_signals, 'surge')
        ]

        for signals, group_name in signal_groups:
            if not signals:
                continue

            group_weight = weights.get(group_name, 0)
            signal_weight = group_weight / len(signals)

            for signal_name, signal_info in signals.items():
                score = signal_values.get(signal_info['signal'], 0) * signal_info['strength']
                total_score += score * signal_weight
                total_weight += signal_weight
                signal_details[f"{group_name}_{signal_name}"] = signal_info

        # 风险调整
        risk_adjustment = 1.0
        for signal_name, signal_info in risk_signals.items():
            if signal_info['signal'] == 'CAUTION':
                risk_adjustment *= (1 - signal_info['strength'] * 0.3)

        # 暴涨加成
        surge_boost = 1.0
        if surge_signals:
            avg_surge_strength = np.mean([s['strength'] for s in surge_signals.values()])
            surge_boost = 1 + avg_surge_strength * 0.5

        # 最终得分
        final_score = total_score * risk_adjustment * surge_boost

        return {
            'score': final_score,
            'confidence': abs(final_score),
            'signal_details': signal_details,
            'surge_boost': surge_boost,
            'risk_adjustment': risk_adjustment
        }

    def _generate_final_signal(self, composite_signal: Dict, symbol: str) -> Dict:
        """生成最终信号"""
        score = composite_signal['score']
        confidence = composite_signal['confidence']

        # 信号阈值
        thresholds = self.config['thresholds']
        min_confidence = self.config['min_confidence']

        # 生成信号
        if score > thresholds['strong_buy'] and confidence > min_confidence:
            signal = 'BUY'
            action = 'STRONG_BUY'
        elif score > thresholds['buy'] and confidence > min_confidence:
            signal = 'BUY'
            action = 'BUY'
        elif score < thresholds['strong_sell'] and confidence > min_confidence:
            signal = 'SELL'
            action = 'STRONG_SELL'
        elif score < thresholds['sell'] and confidence > min_confidence:
            signal = 'SELL'
            action = 'SELL'
        else:
            signal = 'HOLD'
            action = 'HOLD'

        # 构建最终信号
        final_signal = {
            'timestamp': datetime.now(),
            'symbol': symbol,
            'signal': signal,
            'action': action,
            'score': score,
            'confidence': confidence,
            'details': composite_signal['signal_details'],
            'surge_boost': composite_signal.get('surge_boost', 1.0),
            'risk_adjustment': composite_signal.get('risk_adjustment', 1.0)
        }

        # 保存信号历史
        self.signals_history.append(final_signal)

        return final_signal

    def _empty_signal(self) -> Dict:
        """空信号"""
        return {
            'timestamp': datetime.now(),
            'symbol': '',
            'signal': 'HOLD',
            'action': 'HOLD',
            'score': 0,
            'confidence': 0,
            'details': {},
            'surge_boost': 1.0,
            'risk_adjustment': 1.0
        }

    def get_signal_summary(self, signals: List[Dict]) -> Dict:
        """获取信号摘要"""
        if not signals:
            return {'total': 0, 'buy': 0, 'sell': 0, 'hold': 0}

        summary = {
            'total': len(signals),
            'buy': len([s for s in signals if s['signal'] == 'BUY']),
            'sell': len([s for s in signals if s['signal'] == 'SELL']),
            'hold': len([s for s in signals if s['signal'] == 'HOLD']),
            'avg_confidence': np.mean([s['confidence'] for s in signals]),
            'strong_signals': len([s for s in signals if 'STRONG' in s['action']])
        }

        return summary
