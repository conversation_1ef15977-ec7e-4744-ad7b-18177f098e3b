# 多币种暴涨信号系统

一个基于机器学习和技术分析的加密货币交易信号生成系统，专门用于发现市场中即将暴涨的币种。系统支持多个币种的实时监控，通过综合分析技术指标、市场结构、机器学习预测和风险因素，生成高质量的交易信号。

## 🎯 系统特点

- 🤖 **多维度分析**：结合技术分析、机器学习和市场结构
- 🔍 **多币种监控**：同时监控多个主流加密货币
- ⚡ **实时信号**：实时检测暴涨信号并发出通知
- 🛡️ **风险管理**：内置风险评估和仓位管理
- 📊 **智能预测**：为每个币种训练专门的预测模型
- 🔔 **多种通知**：支持声音、Telegram、邮件通知

## 📋 目录

- [快速开始](#快速开始)
- [系统架构](#系统架构)
- [核心功能](#核心功能)
- [使用指南](#使用指南)
- [配置说明](#配置说明)
- [API文档](#api文档)

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
cd 05baozhang

# 安装依赖
pip install -r requirements.txt

# 安装TA-Lib (技术分析库)
# macOS
brew install ta-lib
# Ubuntu
sudo apt-get install libta-lib-dev
# Windows: 下载预编译包
```

### 2. 配置系统

编辑 `config.py` 文件，设置：
- 交易所API密钥（可选，用于实时数据）
- 监控币种列表
- 通知设置

### 3. 下载数据

```bash
# 下载所有配置币种的历史数据（30天）
python main.py --download --days 30

# 下载特定币种数据
python main.py --download --symbols BTCUSDT ETHUSDT --days 30
```

### 4. 训练模型

```bash
# 为所有币种训练模型
python main.py --train

# 为特定币种训练模型
python main.py --train --symbols BTCUSDT ETHUSDT
```

### 5. 分析币种

```bash
# 分析单个币种
python main.py --analyze BTCUSDT

# 显示详细分析结果
python main.py --analyze BTCUSDT --details
```

### 6. 启动监控

```bash
# 启动实时监控系统
python main.py --monitor

# 监控特定币种
python main.py --monitor --symbols BTCUSDT ETHUSDT SOLUSDT
```

## 🏗️ 系统架构

系统采用模块化设计，主要包含以下核心组件：

```
数据采集层 → 特征工程层 → 信号生成层 → 风险控制层 → 执行层
     ↓            ↓            ↓            ↓         ↓
  多交易所API   技术指标计算   ML模型预测   仓位管理   通知提醒
  实时行情数据  特征工程      信号聚合     止损止盈   信号记录
  历史数据存储  模式识别     策略优化     风控规则   性能监控
```

### 核心组件

1. **配置管理 (config.py)**：集中管理系统的所有配置参数
2. **数据采集器 (data_collector.py)**：负责从交易所获取数据
3. **技术指标计算器 (technical_indicators.py)**：计算各种技术分析指标
4. **机器学习预测器 (ml_predictor.py)**：基于LightGBM的价格预测模型
5. **信号生成器 (signal_generator.py)**：综合多种信号源生成交易信号
6. **风险管理器 (risk_manager.py)**：负责交易风险控制
7. **多币种监控器 (multi_coin_monitor.py)**：实时监控多个币种
8. **模型训练器 (train_models.py)**：为每个币种训练专门的预测模型
9. **主程序 (main.py)**：系统入口和命令行接口

### 技术栈

- **核心框架**：Python 3.9+, CCXT, TA-Lib, Scikit-learn, LightGBM
- **数据处理**：Pandas, NumPy
- **模型存储**：Joblib
- **通知系统**：声音提醒, Telegram Bot (可选), 邮件通知 (可选)

## 💡 核心功能

### 1. 多币种数据采集

- 支持Binance等主流交易所API
- 批量获取多个币种的历史K线数据
- 实时价格更新和监控
- 数据本地存储和缓存
- 数据质量检查和验证

### 2. 技术指标分析

- **趋势指标**：移动平均线(SMA/EMA)、MACD、ADX
- **震荡指标**：RSI、随机指标、威廉指标
- **成交量指标**：OBV、成交量比率、资金流量指标
- **波动率指标**：布林带、ATR、历史波动率
- **形态识别**：锤子线、吞没形态、十字星、启明星等

### 3. 机器学习预测

- 基于LightGBM的多分类模型
- 为每个币种训练专门的预测模型
- 特征工程和数据预处理
- 模型评估和特征重要性分析
- 预测未来价格走势和信号生成

### 4. 暴涨信号检测

系统识别的主要暴涨信号特征：

- **价格突破**：突破重要阻力位
- **成交量暴增**：成交量显著高于平均水平
- **技术指标共振**：多个技术指标同时发出买入信号
- **形态识别**：识别看涨形态
- **动量加速**：价格变化速率加快
- **波动率扩大**：市场波动性突然增加
- **支撑位反弹**：从关键支撑位强势反弹
- **均线多头排列**：短期均线上穿长期均线

### 5. 综合信号生成

- 多维度信号融合（技术分析 + 机器学习 + 市场结构）
- 信号强度评估和置信度计算
- 风险调整和暴涨加成
- 信号过滤和优化

### 6. 风险管理

- 仓位大小计算和优化
- 止损止盈价格设定
- 风险检查和验证
- 投资组合管理
- 交易记录和统计分析

### 7. 实时监控

- 多币种并行监控
- 实时信号检测和筛选
- 多种通知方式（声音、Telegram、邮件）
- 信号历史记录和报告生成
- 系统状态监控

## 📖 使用指南

### 基本工作流程

1. **环境准备**：安装依赖和配置系统
2. **数据下载**：获取历史数据用于训练和分析
3. **模型训练**：为每个币种训练预测模型
4. **单币种分析**：验证系统对特定币种的分析效果
5. **实时监控**：启动监控系统进行实时信号检测

### 命令行使用

```bash
# 查看帮助
python main.py --help

# 下载数据
python main.py --download --days 30
python main.py --download --symbols BTCUSDT ETHUSDT --days 7

# 训练模型
python main.py --train
python main.py --train --symbols BTCUSDT --retrain

# 分析币种
python main.py --analyze BTCUSDT
python main.py --analyze SUIUSDT --details

# 启动监控
python main.py --monitor
python main.py --monitor --symbols BTCUSDT ETHUSDT SOLUSDT
```

### 配置说明

主要配置项在 `config.py` 中：

```python
# 监控币种列表
SYMBOLS = [
    'BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'SUIUSDT', 'PENGUUSDT',
    'ADAUSDT', 'DOTUSDT', 'LINKUSDT', 'AVAXUSDT', 'MATICUSDT'
]

# 暴涨信号配置
SURGE_CONFIG = {
    'surge_threshold': 0.05,        # 暴涨阈值5%
    'volume_surge_ratio': 3.0,      # 成交量暴增比例
    'max_coins_per_signal': 3       # 每次最多推荐币种数
}

# 监控配置
MONITOR_CONFIG = {
    'scan_interval': 300,           # 扫描间隔(秒)
    'enable_sound': True,           # 启用声音报警
    'enable_telegram': False,       # 启用Telegram通知
}
```

## 🔧 高级功能

### 模型管理

```bash
# 训练特定币种模型
python train_models.py --symbols BTCUSDT ETHUSDT

# 评估已训练模型
python train_models.py --evaluate

# 更新表现不佳的模型
python train_models.py --update
```

### 监控系统

监控系统会：
1. 定期扫描所有配置的币种
2. 分析技术指标和生成ML预测
3. 检测暴涨信号并评估风险
4. 发送通知并记录信号历史
5. 生成详细的分析报告

### 信号解读

系统生成的信号包含以下信息：

- **信号类型**：BUY/SELL/HOLD
- **行动建议**：STRONG_BUY/BUY/STRONG_SELL/SELL/HOLD
- **置信度**：0-1之间的数值，越高越可信
- **综合得分**：考虑所有因素的综合评分
- **详细分析**：各个信号源的具体分析结果
- **风险评估**：仓位建议和风险检查结果

## 📊 输出示例

### 单币种分析输出

```
📊 BTCUSDT 分析结果:
信号: BUY (STRONG_BUY)
置信度: 0.8500
得分: 0.7200

🚀 暴涨信号:
  - price_breakout: 0.80
  - volume_surge: 0.70
  - momentum_surge: 0.75

📝 详细信号:
  - technical_RSI: BUY (强度: 0.80)
    原因: RSI超卖
  - ml_ML: BUY (强度: 0.75)
    原因: ML预测(0.75置信度)
  - market_VOLUME: BUY (强度: 0.60)
    原因: 成交量放大

💰 最新价格: 43250.500000

⚖️ 风险评估:
✅ 交易有效
建议仓位: 0.0347 BTCUSDT
止损价: 41087.975000
止盈价: 49737.575000
```

### 监控系统输出

```
🚀 启动多币种暴涨信号监控系统
📚 获取历史数据 (1000条记录)...
✅ BTCUSDT 数据加载完成 (1000 条记录)
✅ ETHUSDT 数据加载完成 (1000 条记录)
数据初始化完成，共加载 10 个币种

🔍 开始扫描 10 个币种...
🎯 发现 2 个强烈信号:
🚨 SUIUSDT: STRONG_BUY (置信度: 0.85, 得分: 0.72)
   暴涨信号: surge_price_breakout, surge_volume_surge
   ✅ 风险检查通过，建议仓位: 0.2150

🚨 PENGUUSDT: BUY (置信度: 0.68, 得分: 0.45)
   暴涨信号: surge_momentum_surge
   ✅ 风险检查通过，建议仓位: 0.1890

扫描完成，耗时 12.50秒，287.50秒后进行下次扫描
```

## 🎯 多币种适配

系统通过以下方式实现多币种适配：

1. **统一数据格式**：所有币种使用相同的数据结构
2. **币种特定配置**：为不同币种提供特定参数
3. **并行处理**：使用线程池并行处理多个币种
4. **独立模型**：为每个币种训练专门的预测模型
5. **共性分析**：识别各币种的共同暴涨特征

### 添加新币种

1. 在 `config.py` 的 `SYMBOLS` 列表中添加新币种
2. 运行 `python main.py --download --symbols NEWCOIN` 下载数据
3. 运行 `python main.py --train --symbols NEWCOIN` 训练模型
4. 新币种将自动包含在监控系统中

## ⚠️ 注意事项

1. **数据质量**：确保有足够的历史数据用于训练（建议至少30天）
2. **模型更新**：定期重新训练模型以适应市场变化
3. **风险控制**：系统提供的是信号参考，实际交易需要结合其他分析
4. **API限制**：注意交易所API的请求频率限制
5. **网络稳定**：确保网络连接稳定以获取实时数据

## 🔮 未来计划

- [ ] 添加更多交易所支持
- [ ] 集成更多技术指标
- [ ] 优化机器学习模型
- [ ] 添加回测功能
- [ ] 开发Web界面
- [ ] 支持更多通知方式
- [ ] 添加策略回测和优化

---

**免责声明**：本系统仅供学习和研究使用，不构成投资建议。加密货币交易存在高风险，请谨慎投资。
