# 多币种暴涨信号系统

## 项目概述

多币种暴涨信号系统是一个基于机器学习和技术分析的加密货币交易信号生成系统，旨在发现市场中即将暴涨的币种并提供交易信号。系统支持多个币种的实时监控，通过综合分析技术指标、市场结构、机器学习预测和风险因素，生成高质量的交易信号。

## 系统架构

系统采用模块化设计，主要包含以下核心组件：

1. **数据采集层**：负责从交易所API获取多币种的历史和实时数据
2. **特征工程层**：计算技术指标和构建预测特征
3. **信号生成层**：结合技术分析和机器学习生成交易信号
4. **风险控制层**：评估交易风险并提供仓位管理建议
5. **监控执行层**：实时监控多个币种并发出暴涨预警

### 数据流程

```
数据采集 → 特征计算 → 模型预测 → 信号生成 → 风险评估 → 发送通知
```

## 核心模块

### 1. 配置管理 (config.py)

集中管理系统的所有配置参数，包括：

- 交易所API配置
- 监控币种列表
- 技术指标参数
- 机器学习模型配置
- 信号生成阈值
- 风险管理参数
- 监控和通知设置

### 2. 数据采集器 (data_collector.py)

负责从交易所获取数据，支持：

- 多交易所API接入
- 批量获取多币种历史数据
- 实时价格更新
- 数据本地存储和加载
- 数据质量检查

### 3. 技术指标计算器 (technical_indicators.py)

计算各种技术分析指标：

- 移动平均线（SMA、EMA）
- 相对强弱指标（RSI）
- MACD指标
- 布林带
- 成交量指标
- 支撑阻力位
- 波动率指标
- 价格形态识别
- 暴涨信号检测

### 4. 机器学习预测器 (ml_predictor.py)

基于LightGBM的价格预测模型：

- 特征工程和数据预处理
- 模型训练和评估
- 预测未来价格走势
- 模型保存和加载
- 特征重要性分析

### 5. 信号生成器 (signal_generator.py)

综合多种信号源生成交易信号：

- 技术指标信号
- 机器学习预测信号
- 市场结构信号
- 风险信号
- 暴涨特殊信号
- 信号加权和综合评分

### 6. 风险管理器 (risk_manager.py)

负责交易风险控制：

- 仓位大小计算
- 止损止盈设置
- 风险检查和验证
- 投资组合管理
- 交易记录和统计

### 7. 多币种监控器 (multi_coin_monitor.py)

实时监控多个币种：

- 并行数据更新和分析
- 信号处理和筛选
- 通知发送（声音、Telegram、邮件）
- 信号历史记录
- 状态报告生成

### 8. 模型训练器 (train_models.py)

为每个币种训练专门的预测模型：

- 批量训练多个模型
- 模型评估和更新
- 训练报告生成
- 特征重要性分析

### 9. 主程序 (main.py)

系统入口和命令行接口：

- 环境设置
- 数据下载
- 单币种分析
- 监控启动
- 模型训练

## 数据结构

### 1. 原始K线数据

```
Timestamp,Open,High,Low,Close,Volume,CloseTime,QuoteVolume,TradeCount,TakerBuyBaseVolume,TakerBuyQuoteVolume
```

### 2. 信号结构

```json
{
  "timestamp": "2023-07-15T12:34:56",
  "symbol": "BTCUSDT",
  "signal": "BUY",
  "action": "STRONG_BUY",
  "score": 0.75,
  "confidence": 0.85,
  "details": {
    "technical_RSI": {"signal": "BUY", "strength": 0.8, "reason": "RSI超卖"},
    "ml_ML": {"signal": "BUY", "strength": 0.7, "reason": "ML预测(0.75置信度)"},
    "market_VOLUME": {"signal": "BUY", "strength": 0.6, "reason": "成交量放大"},
    "risk_VOLATILITY": {"signal": "CAUTION", "strength": 0.3, "reason": "波动率适中"},
    "surge_price_breakout": {"signal": "BUY", "strength": 0.8, "reason": "暴涨信号: price_breakout"}
  },
  "surge_boost": 1.3,
  "risk_adjustment": 0.9
}
```

### 3. 仓位信息

```json
{
  "symbol": "BTCUSDT",
  "signal": "BUY",
  "quantity": 0.05,
  "entry_price": 30000.0,
  "position_value": 1500.0,
  "stop_loss": 28500.0,
  "take_profit": 34500.0,
  "confidence": 0.85,
  "risk_checks": {
    "position_size": {"ratio": 0.15, "max_allowed": 0.2, "passed": true},
    "daily_loss": {"current_loss": 0, "potential_loss": 75.0, "total_ratio": 0.0075, "max_allowed": 0.02, "passed": true},
    "drawdown": {"current": 0.05, "max_allowed": 0.1, "passed": true},
    "volatility": {"current": 0.3, "threshold": 2.0, "passed": true},
    "liquidity": {"volume_strength": 0.6, "threshold": 2.0, "passed": true}
  },
  "timestamp": "2023-07-15T12:34:56"
}
```

## 工作流程

### 1. 数据准备

1. 使用`main.py --download`下载历史数据
2. 数据存储在`data/`目录下

### 2. 模型训练

1. 使用`main.py --train`为每个币种训练模型
2. 模型存储在`models/`目录下

### 3. 单币种分析

1. 使用`main.py --analyze SYMBOL`分析单个币种
2. 查看详细分析结果：`main.py --analyze SYMBOL --details`

### 4. 实时监控

1. 使用`main.py --monitor`启动监控系统
2. 系统自动扫描所有配置的币种
3. 发现暴涨信号时发出通知

## 多币种适配

系统设计支持多个币种的监控和分析，通过以下方式实现多币种适配：

1. **统一数据格式**：所有币种使用相同的数据结构和处理流程
2. **币种特定配置**：在`Config.get_symbol_config()`中为不同币种提供特定参数
3. **并行处理**：使用线程池并行处理多个币种的数据和分析
4. **独立模型**：为每个币种训练专门的预测模型
5. **共性分析**：通过`detect_surge_signals()`识别各币种的共同暴涨特征

## 暴涨信号特征

系统识别的主要暴涨信号特征包括：

1. **价格突破**：价格突破重要阻力位
2. **成交量暴增**：成交量显著高于平均水平
3. **技术指标共振**：多个技术指标同时发出买入信号
4. **形态识别**：识别看涨形态（如锤子线、启明星等）
5. **动量加速**：价格变化速率加快
6. **波动率扩大**：市场波动性突然增加
7. **支撑位反弹**：从关键支撑位强势反弹
8. **均线多头排列**：短期均线上穿长期均线

## 系统优势

1. **多维度分析**：结合技术分析、机器学习和市场结构
2. **风险管理**：内置风险评估和仓位管理
3. **实时监控**：支持多币种并行监控
4. **适应性强**：为每个币种训练专门模型
5. **可扩展性**：模块化设计便于添加新功能
6. **完整记录**：保存详细的信号历史和分析报告

## 使用建议

1. 先下载足够的历史数据（至少30天）
2. 为所有监控币种训练模型
3. 使用单币种分析功能验证系统效果
4. 启动监控系统进行实时监控
5. 定期更新数据和重新训练模型
6. 结合其他分析工具验证系统信号
