#!/bin/bash

# 多币种暴涨信号系统启动脚本

echo "🚀 多币种暴涨信号系统"
echo "========================"

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装，请先安装Python3"
    exit 1
fi

# 检查依赖
echo "📦 检查依赖..."
python3 -c "import pandas, numpy, sklearn, lightgbm, ccxt, talib" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "⚠️ 缺少依赖包，正在安装..."
    pip3 install -r requirements.txt
fi

# 创建必要目录
mkdir -p data models logs

echo "✅ 环境检查完成"
echo ""

# 显示菜单
while true; do
    echo "请选择操作："
    echo "1. 测试系统"
    echo "2. 下载数据"
    echo "3. 训练模型"
    echo "4. 分析币种"
    echo "5. 启动监控"
    echo "6. 退出"
    echo ""
    read -p "请输入选项 (1-6): " choice

    case $choice in
        1)
            echo "🧪 运行系统测试..."
            python3 test_system.py
            ;;
        2)
            echo "📥 下载历史数据..."
            read -p "输入天数 (默认30): " days
            days=${days:-30}
            python3 main.py --download --days $days
            ;;
        3)
            echo "🧠 训练模型..."
            python3 main.py --train
            ;;
        4)
            echo "📊 分析币种..."
            read -p "输入币种符号 (如 BTCUSDT): " symbol
            if [ -n "$symbol" ]; then
                python3 main.py --analyze $symbol --details
            else
                echo "❌ 请输入有效的币种符号"
            fi
            ;;
        5)
            echo "🔍 启动监控系统..."
            echo "按 Ctrl+C 停止监控"
            python3 main.py --monitor
            ;;
        6)
            echo "👋 再见！"
            exit 0
            ;;
        *)
            echo "❌ 无效选项，请重新选择"
            ;;
    esac
    
    echo ""
    echo "按回车键继续..."
    read
    echo ""
done
