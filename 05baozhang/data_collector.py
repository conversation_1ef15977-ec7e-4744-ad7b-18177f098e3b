#!/usr/bin/env python3
"""
多币种数据采集器
基于README.md中的CryptoDataCollector设计
"""

import ccxt
import pandas as pd
import numpy as np
import asyncio
import aiohttp
import requests
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from config import Config

class CryptoDataCollector:
    """加密货币数据采集器"""

    def __init__(self, exchange_configs: Dict = None):
        self.exchanges = {}
        self.config = Config()
        self.logger = logging.getLogger(__name__)

        # 使用默认配置或传入的配置
        if exchange_configs is None:
            exchange_configs = self.config.EXCHANGES

        self.initialize_exchanges(exchange_configs)

    def initialize_exchanges(self, configs: Dict):
        """初始化交易所连接"""
        for exchange_name, config in configs.items():
            try:
                exchange_class = getattr(ccxt, exchange_name)
                self.exchanges[exchange_name] = exchange_class({
                    'apiKey': config.get('api_key'),
                    'secret': config.get('secret'),
                    'sandbox': config.get('sandbox', True),
                    'enableRateLimit': True,
                    'options': {'defaultType': 'spot'}  # 现货交易
                })
                self.logger.info(f"Successfully initialized {exchange_name}")
            except Exception as e:
                self.logger.error(f"Failed to initialize {exchange_name}: {e}")

    async def fetch_ohlcv_data(self, symbol: str, timeframe: str = '1m',
                              limit: int = 1000, exchange: str = 'binance') -> pd.DataFrame:
        """获取OHLCV数据"""
        try:
            exchange_obj = self.exchanges[exchange]
            ohlcv = exchange_obj.fetch_ohlcv(symbol, timeframe, limit=limit)

            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)

            return df
        except Exception as e:
            self.logger.error(f"Error fetching data for {symbol}: {e}")
            return pd.DataFrame()

    def fetch_ohlcv_sync(self, symbol: str, timeframe: str = '1m',
                        limit: int = 1000, exchange: str = 'binance') -> pd.DataFrame:
        """同步获取OHLCV数据"""
        try:
            exchange_obj = self.exchanges[exchange]
            ohlcv = exchange_obj.fetch_ohlcv(symbol, timeframe, limit=limit)

            df = pd.DataFrame(ohlcv, columns=['Timestamp', 'Open', 'High', 'Low', 'Close', 'Volume'])
            df['Timestamp'] = pd.to_datetime(df['Timestamp'], unit='ms')

            # 添加额外的列以匹配现有数据格式
            df['CloseTime'] = df['Timestamp'] + pd.Timedelta(minutes=1)
            df['QuoteVolume'] = df['Volume'] * df['Close']  # 估算
            df['TradeCount'] = 0  # 占位符
            df['TakerBuyBaseVolume'] = df['Volume'] * 0.5  # 估算
            df['TakerBuyQuoteVolume'] = df['QuoteVolume'] * 0.5  # 估算

            return df
        except Exception as e:
            self.logger.error(f"Error fetching data for {symbol}: {e}")
            return pd.DataFrame()

    async def fetch_order_book(self, symbol: str, exchange: str = 'binance',
                              limit: int = 20) -> Dict:
        """获取订单簿数据"""
        try:
            exchange_obj = self.exchanges[exchange]
            order_book = exchange_obj.fetch_order_book(symbol, limit)
            return order_book
        except Exception as e:
            self.logger.error(f"Error fetching order book for {symbol}: {e}")
            return {}

    async def fetch_ticker(self, symbol: str, exchange: str = 'binance') -> Dict:
        """获取实时价格数据"""
        try:
            exchange_obj = self.exchanges[exchange]
            ticker = exchange_obj.fetch_ticker(symbol)
            return ticker
        except Exception as e:
            self.logger.error(f"Error fetching ticker for {symbol}: {e}")
            return {}

    def fetch_multiple_symbols(self, symbols: List[str], timeframe: str = '1m',
                              limit: int = 1000, exchange: str = 'binance') -> Dict[str, pd.DataFrame]:
        """批量获取多个币种的数据"""
        results = {}

        for symbol in symbols:
            try:
                self.logger.info(f"Fetching data for {symbol}...")
                df = self.fetch_ohlcv_sync(symbol, timeframe, limit, exchange)

                if not df.empty:
                    results[symbol] = df
                    self.logger.info(f"Successfully fetched {len(df)} records for {symbol}")
                else:
                    self.logger.warning(f"No data received for {symbol}")

                # 避免请求过快
                time.sleep(0.1)

            except Exception as e:
                self.logger.error(f"Failed to fetch data for {symbol}: {e}")
                continue

        return results

    def get_latest_prices(self, symbols: List[str], exchange: str = 'binance') -> Dict[str, float]:
        """获取多个币种的最新价格"""
        prices = {}

        try:
            exchange_obj = self.exchanges[exchange]
            tickers = exchange_obj.fetch_tickers(symbols)

            for symbol in symbols:
                if symbol in tickers:
                    prices[symbol] = tickers[symbol]['last']

        except Exception as e:
            self.logger.error(f"Error fetching latest prices: {e}")

            # 备用方案：逐个获取
            for symbol in symbols:
                try:
                    ticker = exchange_obj.fetch_ticker(symbol)
                    prices[symbol] = ticker['last']
                    time.sleep(0.1)
                except Exception as e2:
                    self.logger.error(f"Error fetching price for {symbol}: {e2}")
                    continue

        return prices

    def get_market_data(self, symbol: str, exchange: str = 'binance') -> Dict[str, Any]:
        """获取市场数据摘要"""
        try:
            exchange_obj = self.exchanges[exchange]
            ticker = exchange_obj.fetch_ticker(symbol)

            market_data = {
                'symbol': symbol,
                'price': ticker['last'],
                'change_24h': ticker['percentage'],
                'volume_24h': ticker['baseVolume'],
                'quote_volume_24h': ticker['quoteVolume'],
                'high_24h': ticker['high'],
                'low_24h': ticker['low'],
                'bid': ticker['bid'],
                'ask': ticker['ask'],
                'timestamp': ticker['timestamp']
            }

            return market_data

        except Exception as e:
            self.logger.error(f"Error fetching market data for {symbol}: {e}")
            return {}

    def save_data(self, data: pd.DataFrame, symbol: str, filepath: str = None) -> bool:
        """保存数据到文件"""
        try:
            if filepath is None:
                filepath = f"{self.config.DATA_DIR}/{symbol.lower()}_data.csv"

            data.to_csv(filepath, index=False)
            self.logger.info(f"Data saved to {filepath}")
            return True

        except Exception as e:
            self.logger.error(f"Error saving data: {e}")
            return False

    def load_data(self, symbol: str, filepath: str = None) -> pd.DataFrame:
        """从文件加载数据"""
        try:
            if filepath is None:
                filepath = f"{self.config.DATA_DIR}/{symbol.lower()}_data.csv"

            df = pd.read_csv(filepath)
            self.logger.info(f"Data loaded from {filepath}")
            return df

        except Exception as e:
            self.logger.error(f"Error loading data: {e}")
            return pd.DataFrame()

    def update_data(self, symbol: str, exchange: str = 'binance') -> pd.DataFrame:
        """更新单个币种的数据"""
        try:
            # 获取最新数据
            new_data = self.fetch_ohlcv_sync(symbol,
                                           self.config.DATA_CONFIG['interval'],
                                           self.config.DATA_CONFIG['limit'],
                                           exchange)

            if new_data.empty:
                return pd.DataFrame()

            # 尝试加载现有数据
            existing_data = self.load_data(symbol)

            if not existing_data.empty:
                # 合并数据，去重
                combined_data = pd.concat([existing_data, new_data])
                combined_data = combined_data.drop_duplicates(subset=['Timestamp'])
                combined_data = combined_data.sort_values('Timestamp')
            else:
                combined_data = new_data

            # 保存更新后的数据
            self.save_data(combined_data, symbol)

            return combined_data

        except Exception as e:
            self.logger.error(f"Error updating data for {symbol}: {e}")
            return pd.DataFrame()

    def get_data_quality_report(self, data: pd.DataFrame) -> Dict[str, Any]:
        """生成数据质量报告"""
        if data.empty:
            return {'status': 'empty', 'message': 'No data available'}

        report = {
            'total_records': len(data),
            'date_range': {
                'start': data['Timestamp'].min(),
                'end': data['Timestamp'].max()
            },
            'missing_values': data.isnull().sum().to_dict(),
            'duplicates': data.duplicated().sum(),
            'price_range': {
                'min': data['Low'].min(),
                'max': data['High'].max()
            },
            'volume_stats': {
                'mean': data['Volume'].mean(),
                'median': data['Volume'].median(),
                'std': data['Volume'].std()
            }
        }

        # 数据完整性检查
        expected_columns = ['Timestamp', 'Open', 'High', 'Low', 'Close', 'Volume']
        missing_columns = [col for col in expected_columns if col not in data.columns]

        if missing_columns:
            report['missing_columns'] = missing_columns
            report['status'] = 'incomplete'
        else:
            report['status'] = 'complete'

        return report
