#!/usr/bin/env python3
"""
风险管理模块
基于README.md中的RiskManager设计
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import logging

from config import Config

class RiskManager:
    """风险管理器"""
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or Config.RISK_CONFIG
        self.max_position_size = self.config['max_position_size']
        self.stop_loss_pct = self.config['stop_loss_pct']
        self.take_profit_pct = self.config['take_profit_pct']
        self.max_daily_loss = self.config['max_daily_loss']
        self.max_drawdown = self.config['max_drawdown']
        
        self.daily_pnl = 0
        self.total_pnl = 0
        self.peak_balance = 0
        self.current_positions = {}
        self.trade_history = []
        self.logger = logging.getLogger(__name__)
        
    def calculate_position_size(self, signal: Dict, account_balance: float, 
                              current_price: float) -> Dict:
        """计算仓位大小"""
        try:
            # 基础仓位大小
            base_size = account_balance * self.max_position_size
            
            # 根据信号强度调整
            confidence = signal.get('confidence', 0.5)
            adjusted_size = base_size * confidence
            
            # 根据信号类型调整
            if signal['action'] == 'STRONG_BUY':
                adjusted_size *= 1.5
            elif signal['action'] == 'STRONG_SELL':
                adjusted_size *= 1.5
            
            # 暴涨信号加成
            surge_boost = signal.get('surge_boost', 1.0)
            if surge_boost > 1.2:
                adjusted_size *= min(surge_boost, 1.5)
            
            # 风险调整
            risk_adjustment = signal.get('risk_adjustment', 1.0)
            adjusted_size *= risk_adjustment
            
            # 计算可购买数量
            quantity = adjusted_size / current_price
            
            # 计算止损止盈价格
            if signal['signal'] == 'BUY':
                stop_loss = current_price * (1 - self.stop_loss_pct)
                take_profit = current_price * (1 + self.take_profit_pct)
            elif signal['signal'] == 'SELL':
                stop_loss = current_price * (1 + self.stop_loss_pct)
                take_profit = current_price * (1 - self.take_profit_pct)
            else:
                stop_loss = None
                take_profit = None
            
            # 风险检查
            risk_checks = self._perform_risk_checks(adjusted_size, account_balance, signal)
            
            position_info = {
                'symbol': signal['symbol'],
                'signal': signal['signal'],
                'quantity': quantity,
                'entry_price': current_price,
                'position_value': adjusted_size,
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'confidence': confidence,
                'risk_checks': risk_checks,
                'timestamp': datetime.now()
            }
            
            return position_info
            
        except Exception as e:
            self.logger.error(f"计算仓位大小时出错: {e}")
            return {}
    
    def _perform_risk_checks(self, position_size: float, account_balance: float, signal: Dict) -> Dict:
        """执行风险检查"""
        checks = {}
        
        # 1. 仓位大小检查
        position_ratio = position_size / account_balance
        checks['position_size'] = {
            'ratio': position_ratio,
            'max_allowed': self.max_position_size,
            'passed': position_ratio <= self.max_position_size
        }
        
        # 2. 日损失检查
        potential_loss = position_size * self.stop_loss_pct
        daily_loss_ratio = (abs(self.daily_pnl) + potential_loss) / account_balance
        checks['daily_loss'] = {
            'current_loss': abs(self.daily_pnl),
            'potential_loss': potential_loss,
            'total_ratio': daily_loss_ratio,
            'max_allowed': self.max_daily_loss,
            'passed': daily_loss_ratio <= self.max_daily_loss
        }
        
        # 3. 回撤检查
        if self.peak_balance > 0:
            current_drawdown = (self.peak_balance - account_balance) / self.peak_balance
            checks['drawdown'] = {
                'current': current_drawdown,
                'max_allowed': self.max_drawdown,
                'passed': current_drawdown <= self.max_drawdown
            }
        else:
            checks['drawdown'] = {'passed': True}
        
        # 4. 波动率检查
        volatility_threshold = self.config.get('volatility_threshold', 2.0)
        signal_volatility = signal.get('details', {}).get('risk_VOLATILITY', {}).get('strength', 0)
        checks['volatility'] = {
            'current': signal_volatility,
            'threshold': volatility_threshold,
            'passed': signal_volatility < volatility_threshold
        }
        
        # 5. 流动性检查
        volume_threshold = self.config.get('volume_threshold', 2.0)
        volume_signal = signal.get('details', {}).get('market_VOLUME', {}).get('strength', 1.0)
        checks['liquidity'] = {
            'volume_strength': volume_signal,
            'threshold': volume_threshold,
            'passed': volume_signal >= 0.5  # 至少中等流动性
        }
        
        return checks
    
    def validate_trade(self, position_info: Dict) -> Tuple[bool, str]:
        """验证交易是否可以执行"""
        risk_checks = position_info.get('risk_checks', {})
        
        failed_checks = []
        
        for check_name, check_result in risk_checks.items():
            if not check_result.get('passed', False):
                failed_checks.append(check_name)
        
        if failed_checks:
            reason = f"风险检查失败: {', '.join(failed_checks)}"
            return False, reason
        
        return True, "所有风险检查通过"
    
    def update_position(self, symbol: str, position_info: Dict):
        """更新仓位信息"""
        self.current_positions[symbol] = position_info
        self.logger.info(f"更新仓位: {symbol} - {position_info['signal']}")
    
    def close_position(self, symbol: str, exit_price: float, reason: str = "手动平仓") -> Dict:
        """平仓"""
        if symbol not in self.current_positions:
            return {'success': False, 'message': f'没有找到 {symbol} 的仓位'}
        
        position = self.current_positions[symbol]
        entry_price = position['entry_price']
        quantity = position['quantity']
        
        # 计算盈亏
        if position['signal'] == 'BUY':
            pnl = (exit_price - entry_price) * quantity
        else:  # SELL
            pnl = (entry_price - exit_price) * quantity
        
        # 更新统计
        self.total_pnl += pnl
        self.daily_pnl += pnl
        
        # 记录交易历史
        trade_record = {
            'symbol': symbol,
            'entry_time': position['timestamp'],
            'exit_time': datetime.now(),
            'signal': position['signal'],
            'entry_price': entry_price,
            'exit_price': exit_price,
            'quantity': quantity,
            'pnl': pnl,
            'reason': reason
        }
        
        self.trade_history.append(trade_record)
        
        # 移除仓位
        del self.current_positions[symbol]
        
        self.logger.info(f"平仓 {symbol}: PnL = {pnl:.2f}, 原因: {reason}")
        
        return {
            'success': True,
            'pnl': pnl,
            'trade_record': trade_record
        }
    
    def check_stop_loss_take_profit(self, symbol: str, current_price: float) -> Optional[str]:
        """检查止损止盈"""
        if symbol not in self.current_positions:
            return None
        
        position = self.current_positions[symbol]
        stop_loss = position.get('stop_loss')
        take_profit = position.get('take_profit')
        
        if position['signal'] == 'BUY':
            if stop_loss and current_price <= stop_loss:
                return 'stop_loss'
            elif take_profit and current_price >= take_profit:
                return 'take_profit'
        else:  # SELL
            if stop_loss and current_price >= stop_loss:
                return 'stop_loss'
            elif take_profit and current_price <= take_profit:
                return 'take_profit'
        
        return None
    
    def get_portfolio_summary(self, account_balance: float) -> Dict:
        """获取投资组合摘要"""
        total_position_value = sum(pos['position_value'] for pos in self.current_positions.values())
        
        # 更新峰值余额
        if account_balance > self.peak_balance:
            self.peak_balance = account_balance
        
        # 计算回撤
        current_drawdown = 0
        if self.peak_balance > 0:
            current_drawdown = (self.peak_balance - account_balance) / self.peak_balance
        
        summary = {
            'account_balance': account_balance,
            'total_position_value': total_position_value,
            'available_balance': account_balance - total_position_value,
            'position_ratio': total_position_value / account_balance if account_balance > 0 else 0,
            'active_positions': len(self.current_positions),
            'daily_pnl': self.daily_pnl,
            'total_pnl': self.total_pnl,
            'peak_balance': self.peak_balance,
            'current_drawdown': current_drawdown,
            'total_trades': len(self.trade_history)
        }
        
        return summary
    
    def get_risk_metrics(self) -> Dict:
        """获取风险指标"""
        if not self.trade_history:
            return {'message': '暂无交易历史'}
        
        pnls = [trade['pnl'] for trade in self.trade_history]
        
        metrics = {
            'total_trades': len(self.trade_history),
            'winning_trades': len([pnl for pnl in pnls if pnl > 0]),
            'losing_trades': len([pnl for pnl in pnls if pnl < 0]),
            'win_rate': len([pnl for pnl in pnls if pnl > 0]) / len(pnls),
            'avg_win': np.mean([pnl for pnl in pnls if pnl > 0]) if any(pnl > 0 for pnl in pnls) else 0,
            'avg_loss': np.mean([pnl for pnl in pnls if pnl < 0]) if any(pnl < 0 for pnl in pnls) else 0,
            'max_win': max(pnls) if pnls else 0,
            'max_loss': min(pnls) if pnls else 0,
            'total_pnl': sum(pnls),
            'sharpe_ratio': np.mean(pnls) / np.std(pnls) if np.std(pnls) > 0 else 0
        }
        
        # 计算最大回撤
        cumulative_pnl = np.cumsum(pnls)
        running_max = np.maximum.accumulate(cumulative_pnl)
        drawdowns = (running_max - cumulative_pnl) / running_max
        metrics['max_drawdown'] = np.max(drawdowns) if len(drawdowns) > 0 else 0
        
        return metrics
    
    def reset_daily_pnl(self):
        """重置日盈亏"""
        self.daily_pnl = 0
        self.logger.info("日盈亏已重置")
    
    def get_position_info(self, symbol: str) -> Optional[Dict]:
        """获取特定仓位信息"""
        return self.current_positions.get(symbol)
    
    def emergency_close_all(self, current_prices: Dict[str, float]) -> List[Dict]:
        """紧急平仓所有仓位"""
        results = []
        
        for symbol in list(self.current_positions.keys()):
            if symbol in current_prices:
                result = self.close_position(symbol, current_prices[symbol], "紧急平仓")
                results.append(result)
        
        self.logger.warning(f"紧急平仓完成，共平仓 {len(results)} 个仓位")
        return results
