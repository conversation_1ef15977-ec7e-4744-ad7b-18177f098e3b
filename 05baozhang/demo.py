#!/usr/bin/env python3
"""
多币种暴涨信号系统演示脚本
展示系统的主要功能
"""

import os
import sys
import time
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config
from data_collector import CryptoDataCollector
from technical_indicators import TechnicalIndicators
from signal_generator import SignalGenerator
from risk_manager import RiskManager

def print_header(title):
    """打印标题"""
    print("\n" + "="*60)
    print(f"🎯 {title}")
    print("="*60)

def demo_data_collection():
    """演示数据采集功能"""
    print_header("数据采集演示")
    
    collector = CryptoDataCollector()
    
    # 演示获取单个币种数据
    print("📊 获取BTCUSDT最新数据...")
    try:
        data = collector.fetch_ohlcv_sync('BTCUSDT', '1m', 100)
        if not data.empty:
            print(f"✅ 成功获取 {len(data)} 条BTCUSDT数据")
            print(f"📈 最新价格: {data['Close'].iloc[-1]:.2f}")
            print(f"📊 24h成交量: {data['Volume'].sum():.0f}")
        else:
            print("❌ 数据获取失败")
    except Exception as e:
        print(f"❌ 数据获取出错: {e}")

def demo_technical_analysis():
    """演示技术分析功能"""
    print_header("技术分析演示")
    
    # 使用示例数据
    import pandas as pd
    import numpy as np
    
    # 创建示例数据
    dates = pd.date_range(start='2023-01-01', periods=200, freq='1min')
    np.random.seed(42)
    
    price = 100
    data = []
    for i in range(200):
        change = np.random.normal(0, 0.01)
        price *= (1 + change)
        
        open_price = price * (1 + np.random.normal(0, 0.005))
        high_price = max(open_price, price) * (1 + np.random.uniform(0, 0.01))
        low_price = min(open_price, price) * (1 - np.random.uniform(0, 0.01))
        volume = np.random.lognormal(10, 1)
        
        data.append({
            'Timestamp': dates[i],
            'Open': open_price,
            'High': high_price,
            'Low': low_price,
            'Close': price,
            'Volume': volume
        })
    
    df = pd.DataFrame(data)
    
    # 计算技术指标
    print("🔧 计算技术指标...")
    indicators = TechnicalIndicators()
    df_with_indicators = indicators.calculate_all_indicators(df)
    
    print(f"✅ 计算完成，共 {len(df_with_indicators.columns)} 个特征")
    
    # 显示最新指标值
    latest = df_with_indicators.iloc[-1]
    print("\n📊 最新技术指标:")
    
    if 'RSI' in latest.index and pd.notna(latest['RSI']):
        print(f"   RSI: {latest['RSI']:.2f}")
    
    if 'MACD' in latest.index and pd.notna(latest['MACD']):
        print(f"   MACD: {latest['MACD']:.4f}")
    
    if 'BB_position' in latest.index and pd.notna(latest['BB_position']):
        print(f"   布林带位置: {latest['BB_position']:.2f}")
    
    if 'volume_ratio' in latest.index and pd.notna(latest['volume_ratio']):
        print(f"   成交量比率: {latest['volume_ratio']:.2f}")
    
    # 检测暴涨信号
    print("\n🚀 暴涨信号检测:")
    surge_signals = indicators.detect_surge_signals(df_with_indicators)
    
    if surge_signals:
        for signal_name, strength in surge_signals.items():
            print(f"   ✨ {signal_name}: {strength:.2f}")
    else:
        print("   📊 未检测到明显的暴涨信号")
    
    return df_with_indicators

def demo_signal_generation(data):
    """演示信号生成功能"""
    print_header("信号生成演示")
    
    signal_gen = SignalGenerator()
    
    print("🎯 生成综合交易信号...")
    signal = signal_gen.generate_composite_signal(data, 'DEMO')
    
    print(f"\n📊 信号结果:")
    print(f"   信号类型: {signal['signal']}")
    print(f"   行动建议: {signal['action']}")
    print(f"   置信度: {signal['confidence']:.4f}")
    print(f"   综合得分: {signal['score']:.4f}")
    
    # 显示详细信号分析
    if 'details' in signal and signal['details']:
        print(f"\n📝 详细信号分析 (共{len(signal['details'])}个):")
        
        # 只显示前5个最重要的信号
        sorted_signals = sorted(signal['details'].items(), 
                              key=lambda x: x[1].get('strength', 0), 
                              reverse=True)
        
        for i, (name, detail) in enumerate(sorted_signals[:5], 1):
            print(f"   {i}. {name}: {detail['signal']} (强度: {detail['strength']:.2f})")
            print(f"      原因: {detail['reason']}")
    
    return signal

def demo_risk_management(signal):
    """演示风险管理功能"""
    print_header("风险管理演示")
    
    risk_mgr = RiskManager()
    
    # 模拟账户参数
    account_balance = 10000  # 10000美元账户
    current_price = 100      # 假设当前价格100美元
    
    print(f"💰 账户余额: ${account_balance:,.2f}")
    print(f"💱 当前价格: ${current_price:.2f}")
    
    # 计算仓位
    print("\n⚖️ 计算建议仓位...")
    position_info = risk_mgr.calculate_position_size(signal, account_balance, current_price)
    
    if position_info:
        print(f"✅ 仓位计算完成:")
        print(f"   建议数量: {position_info.get('quantity', 0):.4f}")
        print(f"   仓位价值: ${position_info.get('position_value', 0):.2f}")
        print(f"   仓位比例: {position_info.get('position_value', 0)/account_balance*100:.1f}%")
        
        stop_loss = position_info.get('stop_loss')
        take_profit = position_info.get('take_profit')
        
        if stop_loss:
            print(f"   止损价格: ${stop_loss:.2f}")
        if take_profit:
            print(f"   止盈价格: ${take_profit:.2f}")
        
        # 风险检查
        is_valid, reason = risk_mgr.validate_trade(position_info)
        print(f"\n🔍 风险检查: {'✅ 通过' if is_valid else '❌ 失败'}")
        if not is_valid:
            print(f"   失败原因: {reason}")
    else:
        print("❌ 无法计算仓位信息")

def demo_multi_coin_analysis():
    """演示多币种分析功能"""
    print_header("多币种分析演示")
    
    config = Config()
    symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT']  # 演示用的币种
    
    print(f"🔍 分析 {len(symbols)} 个币种...")
    
    collector = CryptoDataCollector()
    signal_gen = SignalGenerator()
    
    results = []
    
    for i, symbol in enumerate(symbols, 1):
        print(f"\n[{i}/{len(symbols)}] 分析 {symbol}...")
        
        try:
            # 获取数据
            data = collector.load_data(symbol)
            
            if data.empty:
                print(f"   ⚠️ {symbol} 无本地数据，跳过分析")
                continue
            
            # 生成信号
            signal = signal_gen.generate_composite_signal(data, symbol)
            
            results.append({
                'symbol': symbol,
                'signal': signal['signal'],
                'action': signal['action'],
                'confidence': signal['confidence'],
                'score': signal['score']
            })
            
            print(f"   📊 {signal['signal']} ({signal['action']}) - 置信度: {signal['confidence']:.3f}")
            
        except Exception as e:
            print(f"   ❌ {symbol} 分析失败: {e}")
    
    # 汇总结果
    if results:
        print(f"\n📋 分析汇总:")
        print(f"   总币种数: {len(results)}")
        
        buy_signals = [r for r in results if r['signal'] == 'BUY']
        sell_signals = [r for r in results if r['signal'] == 'SELL']
        
        print(f"   买入信号: {len(buy_signals)}")
        print(f"   卖出信号: {len(sell_signals)}")
        print(f"   持有信号: {len(results) - len(buy_signals) - len(sell_signals)}")
        
        if buy_signals:
            print(f"\n🚀 买入推荐:")
            for result in sorted(buy_signals, key=lambda x: x['confidence'], reverse=True):
                print(f"   • {result['symbol']}: {result['action']} (置信度: {result['confidence']:.3f})")

def main():
    """主演示函数"""
    print("🚀 多币种暴涨信号系统演示")
    print("本演示将展示系统的主要功能模块")
    
    try:
        # 1. 数据采集演示
        demo_data_collection()
        time.sleep(2)
        
        # 2. 技术分析演示
        data = demo_technical_analysis()
        time.sleep(2)
        
        # 3. 信号生成演示
        signal = demo_signal_generation(data)
        time.sleep(2)
        
        # 4. 风险管理演示
        demo_risk_management(signal)
        time.sleep(2)
        
        # 5. 多币种分析演示
        demo_multi_coin_analysis()
        
        print_header("演示完成")
        print("🎉 系统演示完成！")
        print("💡 您可以使用以下命令开始使用系统:")
        print("   python main.py --download --days 7    # 下载数据")
        print("   python main.py --train                 # 训练模型")
        print("   python main.py --analyze BTCUSDT       # 分析币种")
        print("   python main.py --monitor               # 启动监控")
        print("   ./run.sh                               # 使用交互式菜单")
        
    except KeyboardInterrupt:
        print("\n\n⏹️ 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
