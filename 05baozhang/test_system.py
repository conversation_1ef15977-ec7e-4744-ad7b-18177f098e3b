#!/usr/bin/env python3
"""
系统测试脚本
验证多币种暴涨信号系统的各个组件是否正常工作
"""

import pandas as pd
import numpy as np
import os
import sys
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config
from data_collector import CryptoDataCollector
from technical_indicators import TechnicalIndicators
from ml_predictor import MLPredictor
from signal_generator import SignalGenerator
from risk_manager import RiskManager

def test_config():
    """测试配置模块"""
    print("🔧 测试配置模块...")

    config = Config()

    # 验证配置
    assert config.validate_config(), "配置验证失败"

    # 测试币种配置
    btc_config = config.get_symbol_config('BTCUSDT')
    assert btc_config['symbol'] == 'BTCUSDT', "币种配置错误"

    print("✅ 配置模块测试通过")

def test_data_collector():
    """测试数据采集器"""
    print("📊 测试数据采集器...")

    collector = CryptoDataCollector()

    # 测试使用现有数据
    if os.path.exists('05baozhang/sui.csv'):
        # 加载SUI数据进行测试
        data = pd.read_csv('05baozhang/sui.csv')
        print(f"✅ 成功加载测试数据: {len(data)} 条记录")

        # 数据质量检查
        quality_report = collector.get_data_quality_report(data)
        print(f"📋 数据质量: {quality_report['status']}")

        return data
    else:
        print("⚠️ 未找到测试数据文件，跳过数据采集器测试")
        return None

def test_technical_indicators(data):
    """测试技术指标计算器"""
    if data is None:
        print("⚠️ 无数据，跳过技术指标测试")
        return None

    print("📈 测试技术指标计算器...")

    indicators = TechnicalIndicators()

    # 计算技术指标
    data_with_indicators = indicators.calculate_all_indicators(data)

    # 检查指标是否计算成功
    required_indicators = ['RSI', 'MACD', 'BB_upper', 'BB_lower', 'volume_ratio']
    for indicator in required_indicators:
        assert indicator in data_with_indicators.columns, f"缺少指标: {indicator}"

    print(f"✅ 技术指标计算成功，共 {len(data_with_indicators.columns)} 个特征")

    # 测试暴涨信号检测
    surge_signals = indicators.detect_surge_signals(data_with_indicators)
    print(f"🚀 检测到 {len(surge_signals)} 个暴涨信号")

    return data_with_indicators

def test_ml_predictor(data):
    """测试机器学习预测器"""
    if data is None or len(data) < 100:
        print("⚠️ 数据不足，跳过ML预测器测试")
        return None

    print("🧠 测试机器学习预测器...")

    predictor = MLPredictor()

    # 准备特征
    features = predictor.prepare_features(data)
    print(f"📊 特征准备完成: {len(features.columns)} 个特征")

    # 创建标签
    labels = predictor.create_labels(data)
    print(f"🏷️ 标签创建完成: {len(labels)} 个样本")

    # 简单训练测试（使用较少数据）
    if len(data) > 200:
        try:
            accuracy = predictor.train_model(data.tail(200))  # 只用最后200条数据训练
            print(f"✅ 模型训练完成，准确率: {accuracy:.4f}")

            # 测试预测
            prediction = predictor.predict(data)
            print(f"🔮 预测结果: {prediction['signal']} (置信度: {prediction['confidence']:.4f})")

            return predictor
        except Exception as e:
            print(f"⚠️ ML训练失败: {e}")
            return None
    else:
        print("⚠️ 数据不足，跳过模型训练")
        return None

def test_signal_generator(data, predictor):
    """测试信号生成器"""
    if data is None:
        print("⚠️ 无数据，跳过信号生成器测试")
        return None

    print("🎯 测试信号生成器...")

    signal_gen = SignalGenerator()

    # 如果有训练好的预测器，使用它
    if predictor:
        signal_gen.ml_predictor = predictor

    # 生成信号
    signal = signal_gen.generate_composite_signal(data, 'TESTUSDT')

    print(f"📊 信号生成完成:")
    print(f"   信号: {signal['signal']} ({signal['action']})")
    print(f"   置信度: {signal['confidence']:.4f}")
    print(f"   得分: {signal['score']:.4f}")

    if 'details' in signal:
        print(f"   详细信号数量: {len(signal['details'])}")

    print("✅ 信号生成器测试通过")
    return signal

def test_risk_manager(signal):
    """测试风险管理器"""
    if signal is None:
        print("⚠️ 无信号，跳过风险管理器测试")
        return

    print("⚖️ 测试风险管理器...")

    risk_mgr = RiskManager()

    # 测试仓位计算
    account_balance = 10000  # 假设10000美元账户
    current_price = 100  # 假设价格100美元

    # 确保信号有必要的字段
    if signal is None or 'signal' not in signal:
        signal = {
            'symbol': 'TESTUSDT',
            'signal': 'BUY',
            'action': 'BUY',
            'confidence': 0.7,
            'score': 0.5,
            'details': {}
        }

    position_info = risk_mgr.calculate_position_size(signal, account_balance, current_price)

    print(f"💰 仓位计算结果:")

    if position_info and isinstance(position_info, dict):
        print(f"   建议数量: {position_info.get('quantity', 0):.4f}")
        print(f"   仓位价值: {position_info.get('position_value', 0):.2f}")

        stop_loss = position_info.get('stop_loss')
        take_profit = position_info.get('take_profit')

        if stop_loss is not None:
            print(f"   止损价: {stop_loss:.2f}")
        else:
            print("   止损价: N/A")

        if take_profit is not None:
            print(f"   止盈价: {take_profit:.2f}")
        else:
            print("   止盈价: N/A")

        # 验证交易
        is_valid, reason = risk_mgr.validate_trade(position_info)
    else:
        print("   无法计算仓位信息")
        is_valid, reason = False, "无仓位信息"
    print(f"   风险检查: {'✅ 通过' if is_valid else '❌ 失败'} - {reason}")

    print("✅ 风险管理器测试通过")

def create_sample_data():
    """创建示例数据用于测试"""
    print("📝 创建示例数据...")

    # 生成示例K线数据
    dates = pd.date_range(start='2023-01-01', periods=1000, freq='1min')

    # 模拟价格走势
    np.random.seed(42)
    price = 100
    prices = []
    volumes = []

    for i in range(1000):
        # 随机价格变动
        change = np.random.normal(0, 0.01)
        price *= (1 + change)
        prices.append(price)

        # 随机成交量
        volume = np.random.lognormal(10, 1)
        volumes.append(volume)

    # 创建OHLC数据
    data = []
    for i in range(len(prices)):
        if i == 0:
            open_price = prices[i]
        else:
            open_price = prices[i-1]

        close_price = prices[i]
        high_price = max(open_price, close_price) * (1 + np.random.uniform(0, 0.005))
        low_price = min(open_price, close_price) * (1 - np.random.uniform(0, 0.005))

        data.append({
            'Timestamp': dates[i],
            'Open': open_price,
            'High': high_price,
            'Low': low_price,
            'Close': close_price,
            'Volume': volumes[i],
            'CloseTime': dates[i] + pd.Timedelta(minutes=1),
            'QuoteVolume': volumes[i] * close_price,
            'TradeCount': np.random.randint(100, 1000),
            'TakerBuyBaseVolume': volumes[i] * 0.5,
            'TakerBuyQuoteVolume': volumes[i] * close_price * 0.5
        })

    df = pd.DataFrame(data)
    print(f"✅ 示例数据创建完成: {len(df)} 条记录")

    return df

def main():
    """主测试函数"""
    print("🚀 开始系统测试")
    print("=" * 50)

    try:
        # 1. 测试配置
        test_config()
        print()

        # 2. 测试数据采集器
        data = test_data_collector()
        print()

        # 如果没有真实数据，创建示例数据
        if data is None:
            data = create_sample_data()
            print()

        # 3. 测试技术指标
        data_with_indicators = test_technical_indicators(data)
        print()

        # 4. 测试ML预测器
        predictor = test_ml_predictor(data_with_indicators)
        print()

        # 5. 测试信号生成器
        signal = test_signal_generator(data_with_indicators, predictor)
        print()

        # 6. 测试风险管理器
        test_risk_manager(signal)
        print()

        print("=" * 50)
        print("🎉 所有测试完成！系统运行正常")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
