#!/usr/bin/env python3
"""
技术指标计算模块
基于README.md中的TechnicalIndicators设计
"""

import talib
import pandas as pd
import numpy as np
from typing import Tuple, Dict, List, Optional
from config import Config

class TechnicalIndicators:
    """技术指标计算器"""

    def __init__(self, config: Optional[Dict] = None):
        self.config = config or Config.TECHNICAL_CONFIG

    @staticmethod
    def calculate_moving_averages(df: pd.DataFrame, periods: List[int] = None) -> pd.DataFrame:
        """计算移动平均线"""
        if periods is None:
            periods = [7, 14, 30, 50, 200]

        for period in periods:
            df[f'SMA_{period}'] = talib.SMA(df['Close'], timeperiod=period)
            df[f'EMA_{period}'] = talib.EMA(df['Close'], timeperiod=period)
        return df

    @staticmethod
    def calculate_rsi(df: pd.DataFrame, period: int = 14) -> pd.DataFrame:
        """计算RSI指标"""
        df['RSI'] = talib.RSI(df['Close'], timeperiod=period)
        df['RSI_oversold'] = df['RSI'] < 30
        df['RSI_overbought'] = df['RSI'] > 70
        return df

    @staticmethod
    def calculate_macd(df: pd.DataFrame, fast: int = 12, slow: int = 26, signal: int = 9) -> pd.DataFrame:
        """计算MACD指标"""
        df['MACD'], df['MACD_signal'], df['MACD_hist'] = talib.MACD(
            df['Close'], fastperiod=fast, slowperiod=slow, signalperiod=signal
        )
        df['MACD_bullish'] = (df['MACD'] > df['MACD_signal']) & (df['MACD'].shift(1) <= df['MACD_signal'].shift(1))
        df['MACD_bearish'] = (df['MACD'] < df['MACD_signal']) & (df['MACD'].shift(1) >= df['MACD_signal'].shift(1))
        return df

    @staticmethod
    def calculate_bollinger_bands(df: pd.DataFrame, period: int = 20, std_dev: int = 2) -> pd.DataFrame:
        """计算布林带指标"""
        df['BB_upper'], df['BB_middle'], df['BB_lower'] = talib.BBANDS(
            df['Close'], timeperiod=period, nbdevup=std_dev, nbdevdn=std_dev
        )
        df['BB_width'] = (df['BB_upper'] - df['BB_lower']) / df['BB_middle']
        df['BB_position'] = (df['Close'] - df['BB_lower']) / (df['BB_upper'] - df['BB_lower'])
        return df

    @staticmethod
    def calculate_volume_indicators(df: pd.DataFrame) -> pd.DataFrame:
        """计算成交量指标"""
        df['volume_sma'] = talib.SMA(df['Volume'], timeperiod=20)
        df['volume_ratio'] = df['Volume'] / df['volume_sma']
        df['OBV'] = talib.OBV(df['Close'], df['Volume'])
        return df

    @staticmethod
    def calculate_support_resistance(df: pd.DataFrame, window: int = 20) -> pd.DataFrame:
        """计算支撑阻力位"""
        df['pivot'] = (df['High'] + df['Low'] + df['Close']) / 3
        df['support'] = df['Low'].rolling(window=window).min()
        df['resistance'] = df['High'].rolling(window=window).max()
        return df

    @staticmethod
    def calculate_atr(df: pd.DataFrame, period: int = 14) -> pd.DataFrame:
        """计算ATR指标"""
        df['ATR'] = talib.ATR(df['High'], df['Low'], df['Close'], timeperiod=period)
        return df

    @staticmethod
    def calculate_stochastic(df: pd.DataFrame, k_period: int = 14, d_period: int = 3) -> pd.DataFrame:
        """计算随机指标"""
        df['STOCH_K'], df['STOCH_D'] = talib.STOCH(
            df['High'], df['Low'], df['Close'],
            fastk_period=k_period, slowk_period=3, slowk_matype=0,
            slowd_period=d_period, slowd_matype=0
        )
        return df

    @staticmethod
    def calculate_adx(df: pd.DataFrame, period: int = 14) -> pd.DataFrame:
        """计算ADX指标"""
        df['ADX'] = talib.ADX(df['High'], df['Low'], df['Close'], timeperiod=period)
        df['PLUS_DI'] = talib.PLUS_DI(df['High'], df['Low'], df['Close'], timeperiod=period)
        df['MINUS_DI'] = talib.MINUS_DI(df['High'], df['Low'], df['Close'], timeperiod=period)
        return df

    @staticmethod
    def calculate_ichimoku(df: pd.DataFrame) -> pd.DataFrame:
        """计算一目均衡表"""
        # 转换线 (Conversion Line)
        high_9 = df['High'].rolling(window=9).max()
        low_9 = df['Low'].rolling(window=9).min()
        df['ichimoku_conversion'] = (high_9 + low_9) / 2

        # 基准线 (Base Line)
        high_26 = df['High'].rolling(window=26).max()
        low_26 = df['Low'].rolling(window=26).min()
        df['ichimoku_base'] = (high_26 + low_26) / 2

        # 先行带1 (Leading Span A)
        df['ichimoku_span_a'] = ((df['ichimoku_conversion'] + df['ichimoku_base']) / 2).shift(26)

        # 先行带2 (Leading Span B)
        high_52 = df['High'].rolling(window=52).max()
        low_52 = df['Low'].rolling(window=52).min()
        df['ichimoku_span_b'] = ((high_52 + low_52) / 2).shift(26)

        # 延迟线 (Lagging Span)
        df['ichimoku_lagging'] = df['Close'].shift(-26)

        return df

    @staticmethod
    def calculate_momentum_indicators(df: pd.DataFrame) -> pd.DataFrame:
        """计算动量指标"""
        # ROC (Rate of Change)
        df['ROC'] = talib.ROC(df['Close'], timeperiod=10)

        # CCI (Commodity Channel Index)
        df['CCI'] = talib.CCI(df['High'], df['Low'], df['Close'], timeperiod=14)

        # Williams %R
        df['WILLR'] = talib.WILLR(df['High'], df['Low'], df['Close'], timeperiod=14)

        # MFI (Money Flow Index)
        df['MFI'] = talib.MFI(df['High'], df['Low'], df['Close'], df['Volume'], timeperiod=14)

        return df

    @staticmethod
    def calculate_volatility_indicators(df: pd.DataFrame) -> pd.DataFrame:
        """计算波动率指标"""
        # 历史波动率
        df['volatility'] = df['Close'].pct_change().rolling(window=20).std() * np.sqrt(252)

        # 真实波动率
        df['true_range'] = talib.TRANGE(df['High'], df['Low'], df['Close'])
        df['ATR'] = talib.ATR(df['High'], df['Low'], df['Close'], timeperiod=14)

        # 波动率比率
        df['volatility_ratio'] = df['volatility'] / df['volatility'].rolling(window=50).mean()

        return df

    @staticmethod
    def calculate_price_patterns(df: pd.DataFrame) -> pd.DataFrame:
        """计算价格形态"""
        # 锤子线
        df['hammer'] = talib.CDLHAMMER(df['Open'], df['High'], df['Low'], df['Close'])

        # 吞没形态
        df['engulfing'] = talib.CDLENGULFING(df['Open'], df['High'], df['Low'], df['Close'])

        # 十字星
        df['doji'] = talib.CDLDOJI(df['Open'], df['High'], df['Low'], df['Close'])

        # 启明星
        df['morning_star'] = talib.CDLMORNINGSTAR(df['Open'], df['High'], df['Low'], df['Close'])

        # 黄昏星
        df['evening_star'] = talib.CDLEVENINGSTAR(df['Open'], df['High'], df['Low'], df['Close'])

        return df

    def calculate_all_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算所有技术指标"""
        # 确保列名大写
        df = df.rename(columns={
            'open': 'Open', 'high': 'High', 'low': 'Low',
            'close': 'Close', 'volume': 'Volume'
        })

        # 计算基础指标
        df = self.calculate_moving_averages(df, self.config['ma_periods'])
        df = self.calculate_rsi(df, self.config['rsi_period'])
        df = self.calculate_macd(df, self.config['macd_fast'], self.config['macd_slow'], self.config['macd_signal'])
        df = self.calculate_bollinger_bands(df, self.config['bb_period'], self.config['bb_std'])
        df = self.calculate_volume_indicators(df)
        df = self.calculate_support_resistance(df, self.config['support_resistance_window'])

        # 计算高级指标
        df = self.calculate_atr(df, self.config['atr_period'])
        df = self.calculate_stochastic(df)
        df = self.calculate_adx(df)
        df = self.calculate_momentum_indicators(df)
        df = self.calculate_volatility_indicators(df)

        # 计算形态指标
        df = self.calculate_price_patterns(df)

        # 删除NaN值
        df = df.replace([np.inf, -np.inf], np.nan)

        return df

    def detect_surge_signals(self, df: pd.DataFrame, config: Dict = None) -> Dict[str, float]:
        """检测暴涨信号"""
        if config is None:
            config = Config.SURGE_CONFIG

        signals = {}

        try:
            # 确保数据已计算指标
            if 'RSI' not in df.columns:
                df = self.calculate_all_indicators(df)

            latest = df.iloc[-1]

            # 1. 价格突破信号
            if latest['Close'] > latest['resistance'] * 0.98:
                signals['price_breakout'] = 0.8

            # 2. 成交量暴增信号
            if latest['volume_ratio'] > config['volume_surge_ratio']:
                signals['volume_surge'] = 0.7

            # 3. RSI超买信号
            if latest['RSI'] > 70:
                signals['rsi_overbought'] = 0.6

            # 4. MACD金叉信号
            if 'MACD_bullish' in latest.index and pd.notna(latest['MACD_bullish']) and latest['MACD_bullish']:
                signals['macd_bullish'] = 0.7

            # 5. 布林带突破信号
            if 'BB_position' in latest.index and pd.notna(latest['BB_position']) and latest['BB_position'] > 0.95:
                signals['bollinger_breakout'] = 0.8

            # 6. 动量信号
            if 'ROC' in latest.index and pd.notna(latest['ROC']) and latest['ROC'] > config['momentum_threshold']:
                signals['momentum_surge'] = 0.7

            # 7. 价格形态信号
            hammer_signal = 'hammer' in latest.index and pd.notna(latest['hammer']) and latest['hammer'] > 0
            morning_star_signal = 'morning_star' in latest.index and pd.notna(latest['morning_star']) and latest['morning_star'] > 0
            if hammer_signal or morning_star_signal:
                signals['bullish_pattern'] = 0.6

            # 8. 短期均线多头排列
            if ('SMA_7' in latest.index and 'SMA_14' in latest.index and 'SMA_30' in latest.index and
                pd.notna(latest['SMA_7']) and pd.notna(latest['SMA_14']) and pd.notna(latest['SMA_30']) and
                latest['Close'] > latest['SMA_7'] > latest['SMA_14'] > latest['SMA_30']):
                signals['ma_bullish'] = 0.7

            # 9. 支撑位反弹信号
            if (len(df) > 2 and 'support' in df.columns and
                pd.notna(df['support'].iloc[-2]) and pd.notna(df['Low'].iloc[-2]) and
                df['Low'].iloc[-2] <= df['support'].iloc[-2] * 1.02 and
                latest['Close'] > df['Close'].iloc[-2] * 1.02):
                signals['support_bounce'] = 0.6

            # 10. 波动率扩大信号
            if 'volatility_ratio' in latest.index and pd.notna(latest['volatility_ratio']) and latest['volatility_ratio'] > 1.5:
                signals['volatility_expansion'] = 0.5

        except Exception as e:
            print(f"检测暴涨信号时出错: {e}")

        return signals
