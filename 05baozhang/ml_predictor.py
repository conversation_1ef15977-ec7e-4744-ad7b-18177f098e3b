#!/usr/bin/env python3
"""
机器学习预测模块
基于README.md中的MLPredictor设计
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import lightgbm as lgb
import joblib
import os
import warnings
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime

from config import Config
from technical_indicators import TechnicalIndicators

warnings.filterwarnings('ignore')

class MLPredictor:
    """机器学习预测器"""

    def __init__(self, model_type: str = 'lightgbm', config: Optional[Dict] = None):
        self.model_type = model_type
        self.model = None
        self.scaler = StandardScaler()
        self.feature_columns = []
        self.config = config or Config.ML_CONFIG
        self.indicators = TechnicalIndicators()

    def prepare_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """准备特征数据"""
        # 基础特征
        feature_df = df[['Close', 'Volume', 'High', 'Low', 'Open']].copy()

        # 价格变化特征
        feature_df['price_change'] = df['Close'].pct_change()
        feature_df['price_change_5'] = df['Close'].pct_change(5)
        feature_df['price_change_10'] = df['Close'].pct_change(10)

        # 波动率特征
        feature_df['volatility'] = df['Close'].rolling(window=20).std()
        feature_df['volatility_ratio'] = feature_df['volatility'] / feature_df['volatility'].rolling(window=50).mean()

        # 技术指标特征
        if 'RSI' in df.columns:
            feature_df['RSI'] = df['RSI']
            feature_df['RSI_change'] = df['RSI'].diff()

        if 'MACD' in df.columns:
            feature_df['MACD'] = df['MACD']
            feature_df['MACD_signal'] = df['MACD_signal']
            feature_df['MACD_hist'] = df['MACD_hist']

        if 'BB_position' in df.columns:
            feature_df['BB_position'] = df['BB_position']
            feature_df['BB_width'] = df['BB_width']

        # 成交量特征
        if 'volume_ratio' in df.columns:
            feature_df['volume_ratio'] = df['volume_ratio']

        # 移动平均线特征
        for period in [7, 14, 30]:
            if f'SMA_{period}' in df.columns:
                feature_df[f'price_to_sma_{period}'] = df['Close'] / df[f'SMA_{period}']

        # 添加ATR特征
        if 'ATR' in df.columns:
            feature_df['ATR'] = df['ATR']
            feature_df['ATR_ratio'] = df['ATR'] / df['Close']

        # 添加支撑阻力特征
        if 'support' in df.columns and 'resistance' in df.columns:
            feature_df['support_distance'] = (df['Close'] - df['support']) / df['Close']
            feature_df['resistance_distance'] = (df['resistance'] - df['Close']) / df['Close']

        # 添加动量指标
        for col in ['ROC', 'CCI', 'WILLR', 'MFI']:
            if col in df.columns:
                feature_df[col] = df[col]

        # 添加形态指标
        for col in ['hammer', 'engulfing', 'doji', 'morning_star', 'evening_star']:
            if col in df.columns:
                feature_df[col] = df[col]

        # 删除无穷大和空值
        feature_df = feature_df.replace([np.inf, -np.inf], np.nan)
        feature_df = feature_df.ffill().fillna(0)

        return feature_df

    def create_labels(self, df: pd.DataFrame, prediction_horizon: int = 6,
                     threshold: float = 0.02) -> pd.Series:
        """创建预测标签"""
        labels = []
        for i in range(len(df) - prediction_horizon):
            current_price = df['Close'].iloc[i]
            future_prices = df['Close'].iloc[i+1:i+prediction_horizon+1]
            max_future_price = future_prices.max()

            # 计算未来收益率
            future_return = (max_future_price - current_price) / current_price

            # 创建标签：0=持有, 1=买入, 2=卖出
            if future_return > threshold:
                labels.append(1)  # 买入信号
            elif future_return < -threshold:
                labels.append(2)  # 卖出信号
            else:
                labels.append(0)  # 持有信号

        # 补齐剩余的标签
        labels.extend([0] * prediction_horizon)

        return pd.Series(labels, index=df.index)

    def train_model(self, df: pd.DataFrame, prediction_horizon: int = None, threshold: float = None):
        """训练模型"""
        # 使用配置或默认值
        prediction_horizon = prediction_horizon or self.config['prediction_horizon']
        threshold = threshold or self.config['threshold']

        # 计算所有技术指标
        df = self.indicators.calculate_all_indicators(df)

        # 准备特征和标签
        features = self.prepare_features(df)
        labels = self.create_labels(df, prediction_horizon, threshold)

        # 删除空值行
        valid_indices = ~(features.isnull().any(axis=1) | labels.isnull())
        features = features[valid_indices]
        labels = labels[valid_indices]

        self.feature_columns = features.columns.tolist()

        # 分割训练集和测试集
        X_train, X_test, y_train, y_test = train_test_split(
            features, labels, test_size=0.2, random_state=42, stratify=labels
        )

        # 特征缩放
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)

        # 训练模型
        if self.model_type == 'lightgbm':
            self.model = lgb.LGBMClassifier(
                objective='multiclass',
                num_class=3,
                learning_rate=0.1,
                n_estimators=200,
                max_depth=6,
                random_state=42
            )
        else:
            self.model = RandomForestClassifier(
                n_estimators=200,
                max_depth=10,
                random_state=42
            )

        self.model.fit(X_train_scaled, y_train)

        # 预测和评估
        y_pred = self.model.predict(X_test_scaled)
        accuracy = accuracy_score(y_test, y_pred)

        print(f"模型准确率: {accuracy:.4f}")
        print(f"分类报告:\n{classification_report(y_test, y_pred)}")
        print(f"混淆矩阵:\n{confusion_matrix(y_test, y_pred)}")

        # 特征重要性
        if hasattr(self.model, 'feature_importances_'):
            importances = self.model.feature_importances_
            feature_importance = pd.DataFrame({
                'feature': self.feature_columns,
                'importance': importances
            }).sort_values('importance', ascending=False)

            print("\n特征重要性 (Top 10):")
            print(feature_importance.head(10))

        return accuracy

    def predict(self, df: pd.DataFrame) -> Dict:
        """进行预测"""
        try:
            # 如果模型未训练，返回默认预测
            if self.model is None:
                return {
                    'prediction': 0,
                    'prediction_proba': [0.7, 0.2, 0.1],
                    'signal': 'HOLD',
                    'confidence': 0.7,
                    'feature_importance': {}
                }

            # 计算技术指标
            df = self.indicators.calculate_all_indicators(df)

            # 准备特征
            features = self.prepare_features(df)

            # 检查特征列是否存在
            if not self.feature_columns:
                self.feature_columns = features.columns.tolist()

            # 只保留训练时的特征列，如果不存在则使用所有特征
            common_features = [col for col in self.feature_columns if col in features.columns]
            if not common_features:
                common_features = features.columns.tolist()

            features = features[common_features]

            # 特征缩放
            if len(features) > 0:
                try:
                    features_scaled = self.scaler.transform(features.fillna(0))
                except:
                    # 如果scaler未训练，使用StandardScaler进行拟合
                    from sklearn.preprocessing import StandardScaler
                    self.scaler = StandardScaler()
                    features_scaled = self.scaler.fit_transform(features.fillna(0))

                # 预测
                if len(features_scaled) > 0:
                    prediction = self.model.predict(features_scaled[-1:])
                    prediction_proba = self.model.predict_proba(features_scaled[-1:])

                    # 获取特征重要性
                    if hasattr(self.model, 'feature_importances_'):
                        feature_importance = dict(zip(common_features, self.model.feature_importances_[:len(common_features)]))
                        # 排序特征重要性
                        feature_importance = dict(sorted(feature_importance.items(), key=lambda x: x[1], reverse=True))
                    else:
                        feature_importance = {}

                    return {
                        'prediction': prediction[0],
                        'prediction_proba': prediction_proba[0],
                        'signal': self._interpret_signal(prediction[0]),
                        'confidence': max(prediction_proba[0]),
                        'feature_importance': feature_importance
                    }

            # 如果无法预测，返回默认值
            return {
                'prediction': 0,
                'prediction_proba': [0.7, 0.2, 0.1],
                'signal': 'HOLD',
                'confidence': 0.7,
                'feature_importance': {}
            }

        except Exception as e:
            print(f"预测时出错: {e}")
            return {
                'prediction': 0,
                'prediction_proba': [0.7, 0.2, 0.1],
                'signal': 'HOLD',
                'confidence': 0.7,
                'error': str(e)
            }

    def _interpret_signal(self, prediction: int) -> str:
        """解释预测信号"""
        signal_map = {0: 'HOLD', 1: 'BUY', 2: 'SELL'}
        return signal_map.get(prediction, 'UNKNOWN')

    def save_model(self, filepath: str = None, symbol: str = 'generic'):
        """保存模型"""
        if filepath is None:
            os.makedirs(Config.MODELS_DIR, exist_ok=True)
            filepath = f"{Config.MODELS_DIR}/{symbol}_{self.model_type}_model.joblib"

        joblib.dump({
            'model': self.model,
            'scaler': self.scaler,
            'feature_columns': self.feature_columns,
            'model_type': self.model_type,
            'timestamp': datetime.now().isoformat()
        }, filepath)

        print(f"模型已保存到 {filepath}")

    def load_model(self, filepath: str):
        """加载模型"""
        data = joblib.load(filepath)
        self.model = data['model']
        self.scaler = data['scaler']
        self.feature_columns = data['feature_columns']
        self.model_type = data['model_type']

        print(f"模型已从 {filepath} 加载")

    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        if self.model is None:
            return {'status': 'not_loaded'}

        info = {
            'model_type': self.model_type,
            'feature_count': len(self.feature_columns),
            'top_features': self.feature_columns[:10] if len(self.feature_columns) > 10 else self.feature_columns
        }

        if hasattr(self.model, 'feature_importances_'):
            top_features = sorted(zip(self.feature_columns, self.model.feature_importances_),
                                key=lambda x: x[1], reverse=True)[:5]
            info['top_important_features'] = [{'name': name, 'importance': float(imp)} for name, imp in top_features]

        return info
