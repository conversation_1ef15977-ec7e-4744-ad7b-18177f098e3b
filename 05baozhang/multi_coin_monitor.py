#!/usr/bin/env python3
"""
多币种监控系统
实时监控多个币种并生成暴涨信号
"""

import asyncio
import pandas as pd
import numpy as np
import time
import logging
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from concurrent.futures import ThreadPoolExecutor
import threading

from config import Config
from data_collector import CryptoDataCollector
from signal_generator import SignalGenerator
from risk_manager import RiskManager
from ml_predictor import MLPredictor

class MultiCoinMonitor:
    """多币种监控器"""
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = Config()
        self.symbols = self.config.SYMBOLS
        self.monitor_config = self.config.MONITOR_CONFIG
        
        # 初始化组件
        self.data_collector = CryptoDataCollector()
        self.signal_generator = SignalGenerator()
        self.risk_manager = RiskManager()
        
        # 监控状态
        self.is_running = False
        self.last_scan_time = None
        self.alert_history = {}
        self.signal_history = []
        
        # 数据缓存
        self.data_cache = {}
        self.price_cache = {}
        
        # 日志设置
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # 线程池
        self.executor = ThreadPoolExecutor(max_workers=10)
        
    def setup_logging(self):
        """设置日志"""
        log_config = self.config.LOG_CONFIG
        
        logging.basicConfig(
            level=getattr(logging, log_config['level']),
            format=log_config['format'],
            handlers=[
                logging.FileHandler(f"{self.config.LOGS_DIR}/monitor.log"),
                logging.StreamHandler() if log_config['console_output'] else logging.NullHandler()
            ]
        )
    
    def start_monitoring(self):
        """开始监控"""
        self.logger.info("🚀 启动多币种暴涨信号监控系统")
        self.is_running = True
        
        try:
            # 初始化数据
            self._initialize_data()
            
            # 开始监控循环
            while self.is_running:
                start_time = time.time()
                
                # 执行扫描
                self._scan_all_symbols()
                
                # 计算下次扫描时间
                elapsed_time = time.time() - start_time
                sleep_time = max(0, self.monitor_config['scan_interval'] - elapsed_time)
                
                self.logger.info(f"扫描完成，耗时 {elapsed_time:.2f}秒，{sleep_time:.2f}秒后进行下次扫描")
                
                if sleep_time > 0:
                    time.sleep(sleep_time)
                    
        except KeyboardInterrupt:
            self.logger.info("收到停止信号，正在关闭监控系统...")
        except Exception as e:
            self.logger.error(f"监控系统出错: {e}")
        finally:
            self.stop_monitoring()
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_running = False
        self.executor.shutdown(wait=True)
        self.logger.info("监控系统已停止")
    
    def _initialize_data(self):
        """初始化数据"""
        self.logger.info("正在初始化数据...")
        
        # 并行获取所有币种的历史数据
        futures = []
        for symbol in self.symbols:
            future = self.executor.submit(self._load_symbol_data, symbol)
            futures.append((symbol, future))
        
        # 等待所有数据加载完成
        for symbol, future in futures:
            try:
                data = future.result(timeout=60)
                if not data.empty:
                    self.data_cache[symbol] = data
                    self.logger.info(f"✅ {symbol} 数据加载完成 ({len(data)} 条记录)")
                else:
                    self.logger.warning(f"⚠️ {symbol} 数据为空")
            except Exception as e:
                self.logger.error(f"❌ {symbol} 数据加载失败: {e}")
        
        self.logger.info(f"数据初始化完成，共加载 {len(self.data_cache)} 个币种")
    
    def _load_symbol_data(self, symbol: str) -> pd.DataFrame:
        """加载单个币种的数据"""
        try:
            # 尝试从文件加载
            data = self.data_collector.load_data(symbol)
            
            if data.empty:
                # 从API获取
                data = self.data_collector.fetch_ohlcv_sync(
                    symbol, 
                    self.config.DATA_CONFIG['interval'],
                    self.config.DATA_CONFIG['limit']
                )
                
                if not data.empty:
                    self.data_collector.save_data(data, symbol)
            
            return data
            
        except Exception as e:
            self.logger.error(f"加载 {symbol} 数据失败: {e}")
            return pd.DataFrame()
    
    def _scan_all_symbols(self):
        """扫描所有币种"""
        self.last_scan_time = datetime.now()
        self.logger.info(f"🔍 开始扫描 {len(self.symbols)} 个币种...")
        
        # 更新价格数据
        self._update_prices()
        
        # 并行分析所有币种
        futures = []
        for symbol in self.symbols:
            if symbol in self.data_cache:
                future = self.executor.submit(self._analyze_symbol, symbol)
                futures.append((symbol, future))
        
        # 收集分析结果
        signals = []
        for symbol, future in futures:
            try:
                signal = future.result(timeout=30)
                if signal and signal['signal'] != 'HOLD':
                    signals.append(signal)
            except Exception as e:
                self.logger.error(f"分析 {symbol} 失败: {e}")
        
        # 处理信号
        if signals:
            self._process_signals(signals)
        else:
            self.logger.info("本次扫描未发现强烈信号")
    
    def _update_prices(self):
        """更新价格数据"""
        try:
            prices = self.data_collector.get_latest_prices(self.symbols)
            self.price_cache.update(prices)
            self.logger.debug(f"价格更新完成，共 {len(prices)} 个币种")
        except Exception as e:
            self.logger.error(f"价格更新失败: {e}")
    
    def _analyze_symbol(self, symbol: str) -> Optional[Dict]:
        """分析单个币种"""
        try:
            # 获取最新数据
            data = self._get_updated_data(symbol)
            if data.empty:
                return None
            
            # 生成信号
            signal = self.signal_generator.generate_composite_signal(data, symbol)
            
            # 检查信号强度
            if signal['confidence'] < self.config.SIGNAL_CONFIG['min_confidence']:
                return None
            
            # 检查冷却时间
            if self._is_in_cooldown(symbol):
                return None
            
            return signal
            
        except Exception as e:
            self.logger.error(f"分析 {symbol} 时出错: {e}")
            return None
    
    def _get_updated_data(self, symbol: str) -> pd.DataFrame:
        """获取更新后的数据"""
        try:
            # 获取缓存数据
            cached_data = self.data_cache.get(symbol, pd.DataFrame())
            
            # 获取最新数据点
            latest_data = self.data_collector.fetch_ohlcv_sync(symbol, '1m', 1)
            
            if not latest_data.empty and not cached_data.empty:
                # 合并数据
                combined_data = pd.concat([cached_data, latest_data])
                combined_data = combined_data.drop_duplicates(subset=['Timestamp'])
                combined_data = combined_data.sort_values('Timestamp')
                
                # 保持数据量在合理范围
                if len(combined_data) > 1500:
                    combined_data = combined_data.tail(1000)
                
                # 更新缓存
                self.data_cache[symbol] = combined_data
                
                return combined_data
            
            return cached_data
            
        except Exception as e:
            self.logger.error(f"更新 {symbol} 数据失败: {e}")
            return self.data_cache.get(symbol, pd.DataFrame())
    
    def _is_in_cooldown(self, symbol: str) -> bool:
        """检查是否在冷却期"""
        cooldown_time = self.monitor_config['alert_cooldown']
        
        if symbol in self.alert_history:
            last_alert = self.alert_history[symbol]
            time_diff = (datetime.now() - last_alert).total_seconds()
            return time_diff < cooldown_time
        
        return False
    
    def _process_signals(self, signals: List[Dict]):
        """处理信号"""
        # 按置信度排序
        signals.sort(key=lambda x: x['confidence'], reverse=True)
        
        # 限制信号数量
        max_signals = self.config.SURGE_CONFIG['max_coins_per_signal']
        top_signals = signals[:max_signals]
        
        self.logger.info(f"🎯 发现 {len(top_signals)} 个强烈信号:")
        
        for signal in top_signals:
            self._handle_signal(signal)
        
        # 保存信号历史
        self.signal_history.extend(top_signals)
        
        # 生成报告
        self._generate_signal_report(top_signals)
    
    def _handle_signal(self, signal: Dict):
        """处理单个信号"""
        symbol = signal['symbol']
        action = signal['action']
        confidence = signal['confidence']
        score = signal['score']
        
        # 记录报警时间
        self.alert_history[symbol] = datetime.now()
        
        # 日志记录
        self.logger.info(f"🚨 {symbol}: {action} (置信度: {confidence:.2f}, 得分: {score:.2f})")
        
        # 详细信号分析
        details = signal.get('details', {})
        surge_signals = [k for k, v in details.items() if 'surge' in k.lower()]
        if surge_signals:
            self.logger.info(f"   暴涨信号: {', '.join(surge_signals)}")
        
        # 风险评估
        current_price = self.price_cache.get(symbol, 0)
        if current_price > 0:
            position_info = self.risk_manager.calculate_position_size(
                signal, 10000, current_price  # 假设10000美元账户
            )
            
            is_valid, reason = self.risk_manager.validate_trade(position_info)
            if is_valid:
                self.logger.info(f"   ✅ 风险检查通过，建议仓位: {position_info['quantity']:.4f}")
            else:
                self.logger.warning(f"   ⚠️ 风险检查失败: {reason}")
        
        # 发送通知
        self._send_notification(signal)
    
    def _send_notification(self, signal: Dict):
        """发送通知"""
        try:
            # 声音提醒
            if self.monitor_config['enable_sound']:
                self._play_alert_sound()
            
            # Telegram通知
            if self.monitor_config['enable_telegram']:
                self._send_telegram_notification(signal)
            
            # 邮件通知
            if self.monitor_config['enable_email']:
                self._send_email_notification(signal)
                
        except Exception as e:
            self.logger.error(f"发送通知失败: {e}")
    
    def _play_alert_sound(self):
        """播放提醒声音"""
        try:
            import platform
            system = platform.system()
            
            if system == "Darwin":  # macOS
                os.system("afplay /System/Library/Sounds/Glass.aiff")
            elif system == "Windows":
                import winsound
                winsound.Beep(1000, 500)
            elif system == "Linux":
                os.system("paplay /usr/share/sounds/alsa/Front_Left.wav")
                
        except Exception as e:
            self.logger.error(f"播放声音失败: {e}")
    
    def _send_telegram_notification(self, signal: Dict):
        """发送Telegram通知"""
        # TODO: 实现Telegram通知
        pass
    
    def _send_email_notification(self, signal: Dict):
        """发送邮件通知"""
        # TODO: 实现邮件通知
        pass
    
    def _generate_signal_report(self, signals: List[Dict]):
        """生成信号报告"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'scan_time': self.last_scan_time.isoformat(),
            'total_symbols': len(self.symbols),
            'signals_found': len(signals),
            'signals': signals
        }
        
        # 保存报告
        report_file = f"{self.config.LOGS_DIR}/signal_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        
        self.logger.info(f"信号报告已保存: {report_file}")
    
    def get_status(self) -> Dict:
        """获取监控状态"""
        return {
            'is_running': self.is_running,
            'last_scan_time': self.last_scan_time,
            'monitored_symbols': len(self.symbols),
            'cached_data': len(self.data_cache),
            'price_cache': len(self.price_cache),
            'signal_history_count': len(self.signal_history),
            'alert_history_count': len(self.alert_history)
        }
    
    def get_recent_signals(self, hours: int = 24) -> List[Dict]:
        """获取最近的信号"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        recent_signals = [
            signal for signal in self.signal_history
            if signal['timestamp'] > cutoff_time
        ]
        
        return recent_signals
    
    def manual_scan(self, symbol: str = None) -> Dict:
        """手动扫描"""
        if symbol:
            # 扫描单个币种
            if symbol in self.symbols:
                signal = self._analyze_symbol(symbol)
                return {'symbol': symbol, 'signal': signal}
            else:
                return {'error': f'币种 {symbol} 不在监控列表中'}
        else:
            # 扫描所有币种
            self._scan_all_symbols()
            return {'message': '手动扫描完成'}

def main():
    """主函数"""
    monitor = MultiCoinMonitor()
    
    try:
        monitor.start_monitoring()
    except KeyboardInterrupt:
        print("\n正在停止监控...")
        monitor.stop_monitoring()

if __name__ == "__main__":
    main()
