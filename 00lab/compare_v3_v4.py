#!/usr/bin/env python3
"""
对比backtestv3和backtestv4的区别
展示两个版本的不同行为模式
"""

import subprocess
import sys
import os
import time

def print_comparison():
    """打印V3和V4的对比说明"""
    print("="*80)
    print("回测模拟器版本对比：V3 vs V4")
    print("="*80)
    
    print("\n📊 backtestv3.py 特点：")
    print("✅ 支持东8区时间显示")
    print("✅ 可以同时进行多个猜测")
    print("✅ 每分钟都会尝试进行新的预测")
    print("✅ 多个猜测可以并行等待验证")
    print("⚠️  可能导致过度交易")
    
    print("\n🔒 backtestv4.py 特点：")
    print("✅ 支持东8区时间显示")
    print("✅ 一次只能进行一次猜测")
    print("✅ 必须等待当前猜测验证完成后才能进行下一次")
    print("✅ 更符合实际交易限制")
    print("✅ 避免过度交易")
    print("✅ 更保守的交易策略")
    
    print("\n🎯 主要区别：")
    print("┌─────────────────┬─────────────────┬─────────────────┐")
    print("│      特性       │      V3版本     │      V4版本     │")
    print("├─────────────────┼─────────────────┼─────────────────┤")
    print("│ 并发猜测        │       支持      │     不支持      │")
    print("│ 交易频率        │       较高      │     较低        │")
    print("│ 风险控制        │       一般      │     更好        │")
    print("│ 实际交易相似度  │       一般      │     更高        │")
    print("│ 资金利用率      │       较高      │     较低        │")
    print("└─────────────────┴─────────────────┴─────────────────┘")
    
    print("\n💡 使用建议：")
    print("• V3适合：快速验证模型效果，测试不同参数")
    print("• V4适合：模拟真实交易环境，评估实际可行性")
    print("• 建议先用V3快速测试，再用V4验证实际效果")

def run_demo():
    """运行演示"""
    print("\n" + "="*60)
    print("演示模式选择")
    print("="*60)
    
    # 检查必要文件
    required_files = [
        'btcusd_1-min_data.csv',
        'btc_prediction_model.joblib', 
        'model_config.json',
        'backtestv3.py',
        'backtestv4.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        print("\n请确保所有文件都存在后再运行演示。")
        return
    
    print("✅ 所有必要文件检查通过！\n")
    
    print("请选择要演示的版本：")
    print("1. backtestv3.py (可并发猜测)")
    print("2. backtestv4.py (一次只能一个猜测)")
    print("3. 不运行演示，只查看对比")
    
    choice = input("\n请输入选择 (1/2/3): ").strip()
    
    if choice == '1':
        print("\n🚀 启动 backtestv3.py 演示...")
        print("特点：可以同时进行多个猜测")
        print("按 Ctrl+C 可以随时停止")
        print("-" * 50)
        
        try:
            cmd = [
                sys.executable, 'backtestv3.py', 
                'btcusd_1-min_data.csv',
                '--start-row', '5000',
                '--speed', '0.2'
            ]
            subprocess.run(cmd, check=True)
        except subprocess.CalledProcessError as e:
            print(f"运行出错: {e}")
        except KeyboardInterrupt:
            print("\n演示被用户中断")
            
    elif choice == '2':
        print("\n🔒 启动 backtestv4.py 演示...")
        print("特点：一次只能进行一次猜测，等验证完成后才能进行下一次")
        print("按 Ctrl+C 可以随时停止")
        print("-" * 50)
        
        try:
            cmd = [
                sys.executable, 'backtestv4.py', 
                'btcusd_1-min_data.csv',
                '--start-row', '5000',
                '--speed', '0.2'
            ]
            subprocess.run(cmd, check=True)
        except subprocess.CalledProcessError as e:
            print(f"运行出错: {e}")
        except KeyboardInterrupt:
            print("\n演示被用户中断")
            
    elif choice == '3':
        print("\n📋 跳过演示，仅查看对比信息")
        
    else:
        print("\n❌ 无效选择")

def show_usage_examples():
    """显示使用示例"""
    print("\n" + "="*60)
    print("使用示例")
    print("="*60)
    
    print("\n📝 backtestv3.py 使用示例：")
    print("# 从第5000行开始，快速模式")
    print("python backtestv3.py btcusd_1-min_data.csv --start-row 5000 --speed 0.1")
    print("\n# 从指定北京时间开始")
    print("python backtestv3.py btcusd_1-min_data.csv --start-time '2025-07-08 14:30:00' --speed 0.2")
    
    print("\n📝 backtestv4.py 使用示例：")
    print("# 从第5000行开始，限制模式")
    print("python backtestv4.py btcusd_1-min_data.csv --start-row 5000 --speed 0.1")
    print("\n# 从指定北京时间开始，限制模式")
    print("python backtestv4.py btcusd_1-min_data.csv --start-time '2025-07-08 14:30:00' --speed 0.2")
    
    print("\n💡 参数说明：")
    print("--start-row: 从数据的第N行开始模拟")
    print("--start-time: 从指定的北京时间开始模拟")
    print("--speed: 模拟速度，每行数据间隔的秒数")

def main():
    """主函数"""
    print_comparison()
    show_usage_examples()
    
    choice = input("\n是否要运行演示？(y/n): ").lower().strip()
    if choice in ['y', 'yes']:
        run_demo()
    else:
        print("\n👋 感谢使用对比工具！")
    
    print("\n" + "="*60)
    print("总结")
    print("="*60)
    print("• V3版本适合快速测试和参数调优")
    print("• V4版本更接近真实交易环境")
    print("• 建议在实际使用前用V4版本进行最终验证")
    print("="*60)

if __name__ == '__main__':
    main()
