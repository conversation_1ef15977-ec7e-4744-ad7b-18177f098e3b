#!/usr/bin/env python3
"""
实时预测脚本
基于LightGBM_percentage_v2模型进行实时BTC价格预测
支持多个并发猜测信号，4小时等待时间
"""

import pandas as pd
import joblib
import json
import numpy as np
import time
import requests
from datetime import datetime, timezone
import os
import signal
import sys
import logging
from typing import Dict, List, Optional, Tuple
import platform

class RealTimePredictor:
    def __init__(self,
                 model_file='btc_percentage_model.joblib',
                 config_file='model_config_percentage.json',
                 log_file='prediction_log.txt',
                 max_wait_hours=4,
                 update_interval=60,
                 enable_sound=True):
        """
        初始化实时预测器

        Args:
            model_file: 模型文件路径
            config_file: 配置文件路径
            log_file: 日志文件路径
            max_wait_hours: 最大等待时间（小时）
            update_interval: 更新间隔（秒）
            enable_sound: 是否启用报警声
        """
        self.model_file = model_file
        self.config_file = config_file
        self.log_file = log_file
        self.max_wait_hours = max_wait_hours
        self.max_wait_minutes = int(max_wait_hours * 60)
        self.update_interval = update_interval
        self.enable_sound = enable_sound
        self.running = True

        # 存储活跃的预测
        self.active_predictions: Dict[str, dict] = {}

        # 统计信息
        self.total_predictions = 0
        self.successful_predictions = 0
        self.failed_predictions = 0
        self.timeout_predictions = 0

        # 设置信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

        # 设置日志
        self.setup_logging()

        print(f"🚀 实时预测器启动")
        print(f"🤖 模型文件: {self.model_file}")
        print(f"📊 配置文件: {self.config_file}")
        print(f"📝 日志文件: {self.log_file}")
        print(f"⏰ 最大等待时间: {self.max_wait_hours}小时")
        print(f"🔄 更新间隔: {self.update_interval}秒")
        print(f"🔔 报警声: {'启用' if self.enable_sound else '禁用'}")
        print("按 Ctrl+C 停止预测\n")

    def signal_handler(self, signum, frame):
        """信号处理器，优雅退出"""
        print(f"\n📋 接收到退出信号 {signum}，正在停止...")
        self.running = False
        self.log_message("系统停止", "用户中断")

    def setup_logging(self):
        """设置日志系统"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def play_alert_sound(self):
        """播放报警声"""
        try:
            system = platform.system().lower()

            if system == "windows":
                # Windows系统使用winsound
                try:
                    import winsound
                    # 播放系统默认声音
                    winsound.MessageBeep(winsound.MB_ICONEXCLAMATION)
                    # 或者播放自定义频率的声音
                    winsound.Beep(1000, 500)  # 1000Hz, 500ms
                except ImportError:
                    # 如果winsound不可用，使用print bell
                    print('\a' * 3)

            elif system == "darwin":  # macOS
                # macOS使用say命令或afplay
                try:
                    os.system('say "预测信号"')
                    # os.system('afplay /System/Library/Sounds/Glass.aiff')
                except:
                    # 备用方案：使用say命令
                    os.system('say "预测信号"')

            elif system == "linux":
                # Linux使用paplay或aplay
                try:
                    # 尝试使用系统声音
                    os.system('paplay /usr/share/sounds/alsa/Front_Left.wav 2>/dev/null')
                except:
                    try:
                        # 备用方案：使用beep命令
                        os.system('beep -f 1000 -l 500 2>/dev/null')
                    except:
                        # 最后备用方案：终端bell
                        print('\a' * 3)
            else:
                # 其他系统使用终端bell
                print('\a' * 3)

        except Exception as e:
            # 如果所有方法都失败，至少输出bell字符
            print('\a' * 3)
            self.log_message("警告", f"播放报警声失败: {e}")

    def log_message(self, event_type: str, message: str, data: dict = None):
        """记录日志消息"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = f"[{timestamp}] {event_type}: {message}"

        if data:
            log_entry += f" | 数据: {data}"

        # 只写入专用日志文件，避免重复
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry + '\n')

    def load_model_and_config(self) -> Tuple[object, dict]:
        """加载模型和配置"""
        try:
            model = joblib.load(self.model_file)
            with open(self.config_file, 'r') as f:
                config = json.load(f)

            self.log_message("模型加载", f"成功加载模型和配置")
            print(f"✅ 模型加载成功")
            print(f"📊 模型类型: {config.get('model_type', 'Unknown')}")
            print(f"🎯 最优阈值: {config.get('best_threshold', 0.5):.3f}")
            print(f"📈 特征数量: {len(config.get('feature_list', []))}")

            return model, config

        except Exception as e:
            self.log_message("错误", f"加载模型失败: {e}")
            raise

    def get_initial_data(self, limit=1000) -> pd.DataFrame:
        """获取初始历史数据"""
        try:
            print(f"📚 获取初始历史数据 ({limit}条记录)...")

            # 使用与get_btc_history.py相同的逻辑
            url = "https://api.binance.com/api/v3/klines"
            params = {
                'symbol': 'BTCUSDT',
                'interval': '1m',
                'limit': min(limit, 1000)  # API限制
            }

            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()

            klines = response.json()

            data_list = []
            for kline in klines:
                data = {
                    'Timestamp': int(kline[0]) // 1000,
                    'Open': float(kline[1]),
                    'High': float(kline[2]),
                    'Low': float(kline[3]),
                    'Close': float(kline[4]),
                    'Volume': float(kline[5]),
                    'CloseTime': int(kline[6]) // 1000,
                    'QuoteVolume': float(kline[7]),
                    'TradeCount': int(kline[8]),
                    'TakerBuyBaseVolume': float(kline[9]),
                    'TakerBuyQuoteVolume': float(kline[10])
                }
                data_list.append(data)

            df = pd.DataFrame(data_list)
            df['Timestamp_dt'] = pd.to_datetime(df['Timestamp'], unit='s', utc=True).dt.tz_convert('Asia/Shanghai')
            df.set_index('Timestamp_dt', inplace=True)

            print(f"✅ 获取到 {len(df)} 条历史数据")
            self.log_message("数据获取", f"初始数据获取成功，{len(df)}条记录")

            return df

        except Exception as e:
            self.log_message("错误", f"获取初始数据失败: {e}")
            raise

    def get_latest_data(self) -> Optional[dict]:
        """获取最新的K线数据"""
        try:
            url = "https://api.binance.com/api/v3/klines"
            params = {
                'symbol': 'BTCUSDT',
                'interval': '1m',
                'limit': 1
            }

            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()

            klines = response.json()
            if not klines:
                return None

            kline = klines[0]
            data = {
                'Timestamp': int(kline[0]) // 1000,
                'Open': float(kline[1]),
                'High': float(kline[2]),
                'Low': float(kline[3]),
                'Close': float(kline[4]),
                'Volume': float(kline[5]),
                'CloseTime': int(kline[6]) // 1000,
                'QuoteVolume': float(kline[7]),
                'TradeCount': int(kline[8]),
                'TakerBuyBaseVolume': float(kline[9]),
                'TakerBuyQuoteVolume': float(kline[10])
            }

            return data

        except Exception as e:
            self.log_message("错误", f"获取最新数据失败: {e}")
            return None

    def calculate_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算特征（与LightGBM_percentage_v2.py保持一致）"""
        try:
            df_work = df.copy()
            epsilon = 1e-9

            # 1. 时间特征
            df_work['hour'] = df_work.index.hour
            df_work['day_of_week'] = df_work.index.dayofweek

            # 2. ATR特征
            df_work = self.get_standardized_kline_features(df_work, '_1m', epsilon)

            # 3. 价格/动量特征
            df_work['return_60min'] = df_work['Close'].pct_change(60)
            df_work['return_30min'] = df_work['Close'].pct_change(30)
            df_work['return_10min'] = df_work['Close'].pct_change(10)
            df_work['return_5min'] = df_work['Close'].pct_change(5)
            df_work['return_3min'] = df_work['Close'].pct_change(3)

            # 4. 波动率特征
            for n in [10, 30, 60]:
                df_work[f'volatility_{n}'] = df_work['Close'].rolling(window=n).std()
                df_work[f'volatility_ratio_{n}'] = df_work[f'volatility_{n}'] / (df_work['Close'] + epsilon)

            # 5. 趋势特征
            for n in [10, 30, 60]:
                df_work[f'sma_{n}'] = df_work['Close'].rolling(window=n).mean()
                df_work[f'price_div_sma_{n}'] = df_work['Close'] / (df_work[f'sma_{n}'] + epsilon)
            df_work['sma_10_div_sma_30'] = df_work['sma_10'] / (df_work['sma_30'] + epsilon)

            # 6. VWAP特征
            df_work['price_x_volume'] = df_work['Close'] * df_work['Volume']
            vwap_numerator = df_work['price_x_volume'].rolling(window=30).sum()
            vwap_denominator = df_work['Volume'].rolling(window=30).sum()
            df_work['vwap_30'] = vwap_numerator / (vwap_denominator + epsilon)
            df_work['price_div_vwap_30'] = df_work['Close'] / (df_work['vwap_30'] + epsilon)
            df_work.drop('price_x_volume', axis=1, inplace=True)

            # 7. 成交量特征
            df_work['vma_60'] = df_work['Volume'].rolling(window=60).mean()
            df_work['volume_div_vma_60'] = df_work['Volume'] / (df_work['vma_60'] + epsilon)

            # 8. 滚动K线特征
            for window in [3, 5]:
                if f'range_norm_by_atr_1m' in df_work.columns:
                    df_work[f'range_norm_by_atr_mean_{window}m'] = df_work['range_norm_by_atr_1m'].rolling(window=window).mean()

            if f'body_percent_of_range_1m' in df_work.columns:
                df_work[f'body_percent_mean_5m'] = df_work['body_percent_of_range_1m'].rolling(window=5).mean()

            return df_work

        except Exception as e:
            self.log_message("错误", f"特征计算失败: {e}")
            raise

    def get_standardized_kline_features(self, df: pd.DataFrame, timeframe_suffix: str, epsilon: float) -> pd.DataFrame:
        """计算标准化的K线特征"""
        try:
            high_low = df['High'] - df['Low']
            high_prev_close = abs(df['High'] - df['Close'].shift(1))
            low_prev_close = abs(df['Low'] - df['Close'].shift(1))
            tr = pd.concat([high_low, high_prev_close, low_prev_close], axis=1).max(axis=1)
            atr_col = f'atr_14{timeframe_suffix}'
            df[atr_col] = tr.rolling(window=14).mean()

            body_size = abs(df['Close'] - df['Open'])
            price_range = df['High'] - df['Low']
            upper_shadow = df['High'] - df[['Open', 'Close']].max(axis=1)
            lower_shadow = df[['Open', 'Close']].min(axis=1) - df['Low']

            df[f'body_percent_of_range{timeframe_suffix}'] = body_size / (price_range + epsilon)
            df[f'upper_shadow_percent_of_range{timeframe_suffix}'] = upper_shadow / (price_range + epsilon)
            df[f'lower_shadow_percent_of_range{timeframe_suffix}'] = lower_shadow / (price_range + epsilon)
            df[f'range_norm_by_atr{timeframe_suffix}'] = price_range / (df[atr_col] + epsilon)

            return df

        except Exception as e:
            self.log_message("错误", f"K线特征计算失败: {e}")
            return df

    def make_prediction(self, model, config: dict, df: pd.DataFrame) -> Tuple[Optional[int], float, float]:
        """进行预测"""
        try:
            if len(df) < 300:  # 确保有足够的历史数据
                return None, 0.0, 0.0

            # 计算特征
            features_df = self.calculate_features(df)
            features_df_clean = features_df.dropna()

            if features_df_clean.empty:
                return None, 0.0, 0.0

            # 获取最新特征
            feature_list = config['feature_list']
            latest_features = features_df_clean[feature_list].iloc[-1:]
            current_price = features_df_clean.iloc[-1]['Close']

            # 预测
            probability = model.predict_proba(latest_features)[0, 1]
            best_threshold = config['best_threshold']

            guess = None
            if probability > best_threshold:
                guess = 1  # 预测先涨1%
            elif probability < (1 - best_threshold):
                guess = 0  # 预测先跌1%

            return guess, probability, current_price

        except Exception as e:
            self.log_message("错误", f"预测失败: {e}")
            return None, 0.0, 0.0

    def add_prediction(self, guess: int, probability: float, price: float, timestamp: datetime):
        """添加新的预测"""
        prediction_id = f"pred_{timestamp.strftime('%Y%m%d_%H%M%S')}"
        expire_time = timestamp + pd.Timedelta(hours=self.max_wait_hours)

        prediction = {
            'id': prediction_id,
            'guess': guess,
            'probability': probability,
            'price': price,
            'timestamp': timestamp,
            'expire_time': expire_time,
            'up_target': price * 1.01,
            'down_target': price * 0.99,
            'status': 'active'
        }

        self.active_predictions[prediction_id] = prediction
        self.total_predictions += 1

        direction_str = "先涨1%" if guess == 1 else "先跌1%"

        # 播放报警声
        if self.enable_sound:
            print("🔔 播放报警声...")
            self.play_alert_sound()
        else:
            print("🔔 报警声已禁用")

        print(f"🎯 新预测: {prediction_id}")
        print(f"   方向: {direction_str} (信心: {probability:.3f})")
        print(f"   价格: ${price:.2f}")
        print(f"   目标: 涨至${prediction['up_target']:.2f} 或 跌至${prediction['down_target']:.2f}")
        print(f"   过期: {expire_time.strftime('%Y-%m-%d %H:%M:%S')}")

        self.log_message("新预测", f"{prediction_id} - {direction_str}", {
            'probability': probability,
            'price': price,
            'up_target': prediction['up_target'],
            'down_target': prediction['down_target']
        })

    def check_predictions(self, current_price: float, current_time: datetime):
        """检查活跃预测的状态"""
        completed_predictions = []

        for pred_id, prediction in self.active_predictions.items():
            if prediction['status'] != 'active':
                continue

            # 检查是否达到目标
            if current_price >= prediction['up_target']:
                # 达到上涨目标
                result = 1 if prediction['guess'] == 1 else 0
                self.complete_prediction(pred_id, result, current_price, current_time, "达到上涨目标")
                completed_predictions.append(pred_id)

            elif current_price <= prediction['down_target']:
                # 达到下跌目标
                result = 1 if prediction['guess'] == 0 else 0
                self.complete_prediction(pred_id, result, current_price, current_time, "达到下跌目标")
                completed_predictions.append(pred_id)

            elif current_time >= prediction['expire_time']:
                # 超时
                self.complete_prediction(pred_id, -1, current_price, current_time, "超时")
                completed_predictions.append(pred_id)

        # 移除已完成的预测
        for pred_id in completed_predictions:
            del self.active_predictions[pred_id]

    def complete_prediction(self, pred_id: str, result: int, final_price: float, end_time: datetime, reason: str):
        """完成预测"""
        prediction = self.active_predictions[pred_id]
        duration = end_time - prediction['timestamp']
        duration_minutes = int(duration.total_seconds() / 60)

        if result == 1:
            # 预测成功
            self.successful_predictions += 1
            status = "成功✅"
            print(f"🎉 预测成功: {pred_id}")
        elif result == 0:
            # 预测失败
            self.failed_predictions += 1
            status = "失败❌"
            print(f"💔 预测失败: {pred_id}")
        else:
            # 超时
            self.timeout_predictions += 1
            status = "超时⏰"
            print(f"⏰ 预测超时: {pred_id}")

        direction_str = "先涨1%" if prediction['guess'] == 1 else "先跌1%"

        print(f"   预测: {direction_str} (信心: {prediction['probability']:.3f})")
        print(f"   起始价格: ${prediction['price']:.2f}")
        print(f"   结束价格: ${final_price:.2f}")
        print(f"   持续时间: {duration_minutes}分钟")
        print(f"   结果: {status} - {reason}")

        self.log_message("预测完成", f"{pred_id} - {status}", {
            'direction': direction_str,
            'probability': prediction['probability'],
            'start_price': prediction['price'],
            'end_price': final_price,
            'duration_minutes': duration_minutes,
            'reason': reason
        })

    def print_status(self):
        """打印当前状态"""
        active_count = len(self.active_predictions)
        success_rate = (self.successful_predictions / max(1, self.total_predictions - self.timeout_predictions)) * 100

        print(f"\n📊 当前状态:")
        print(f"   活跃预测: {active_count}")
        print(f"   总预测数: {self.total_predictions}")
        print(f"   成功: {self.successful_predictions}")
        print(f"   失败: {self.failed_predictions}")
        print(f"   超时: {self.timeout_predictions}")
        print(f"   成功率: {success_rate:.1f}%")

    def run(self):
        """运行实时预测"""
        try:
            # 加载模型和配置
            model, config = self.load_model_and_config()

            # 获取初始数据
            df = self.get_initial_data(1000)

            print(f"\n🔄 开始实时预测...")
            print("-" * 60)

            last_timestamp = None

            while self.running:
                try:
                    # 获取最新数据
                    latest_data = self.get_latest_data()
                    if latest_data is None:
                        time.sleep(5)
                        continue

                    current_time = datetime.fromtimestamp(latest_data['Timestamp'], tz=timezone.utc).astimezone()
                    current_price = latest_data['Close']

                    # 检查是否是新数据
                    if last_timestamp is None or latest_data['Timestamp'] != last_timestamp:
                        # 更新数据框
                        new_row = pd.DataFrame([latest_data])
                        new_row['Timestamp_dt'] = pd.to_datetime(new_row['Timestamp'], unit='s', utc=True).dt.tz_convert('Asia/Shanghai')
                        new_row.set_index('Timestamp_dt', inplace=True)

                        df = pd.concat([df, new_row])
                        df = df.tail(1000)  # 保持最近1000条数据

                        print(f"📈 {current_time.strftime('%Y-%m-%d %H:%M:%S')} | 价格: ${current_price:.2f}")

                        # 检查现有预测
                        self.check_predictions(current_price, current_time)

                        # 进行新预测
                        guess, probability, pred_price = self.make_prediction(model, config, df)

                        if guess is not None:
                            self.add_prediction(guess, probability, pred_price, current_time)
                        else:
                            print("📊 模型分析: 信心不足，放弃本次预测")
                            # 记录放弃预测的日志
                            self.log_message("分析结果", "信心不足，放弃预测", {
                                'probability': probability,
                                'price': pred_price,
                                'threshold': config.get('best_threshold', 0.5),
                                'reason': '模型信心度未达到阈值要求'
                            })

                        last_timestamp = latest_data['Timestamp']

                        # 每10次更新显示一次状态
                        if self.total_predictions % 10 == 0 and self.total_predictions > 0:
                            self.print_status()

                    # 等待下次更新
                    time.sleep(self.update_interval)

                except KeyboardInterrupt:
                    break
                except Exception as e:
                    self.log_message("错误", f"运行时错误: {e}")
                    time.sleep(5)

        except Exception as e:
            self.log_message("错误", f"启动失败: {e}")
            raise
        finally:
            self.print_final_summary()

    def print_final_summary(self):
        """打印最终总结"""
        print(f"\n" + "="*50)
        print("实时预测总结")
        print("="*50)

        total_completed = self.successful_predictions + self.failed_predictions + self.timeout_predictions
        success_rate = (self.successful_predictions / max(1, total_completed - self.timeout_predictions)) * 100

        print(f"总预测数: {self.total_predictions}")
        print(f"已完成: {total_completed}")
        print(f"活跃中: {len(self.active_predictions)}")
        print(f"成功: {self.successful_predictions}")
        print(f"失败: {self.failed_predictions}")
        print(f"超时: {self.timeout_predictions}")
        print(f"成功率: {success_rate:.1f}% (基于非超时预测)")
        print(f"日志文件: {self.log_file}")
        print("="*50)

        self.log_message("系统停止", "预测总结", {
            'total_predictions': self.total_predictions,
            'successful': self.successful_predictions,
            'failed': self.failed_predictions,
            'timeout': self.timeout_predictions,
            'success_rate': success_rate
        })

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="实时BTC价格预测器")
    parser.add_argument("--model-file", default="btc_percentage_model.joblib", help="模型文件路径")
    parser.add_argument("--config-file", default="model_config_percentage.json", help="配置文件路径")
    parser.add_argument("--log-file", default="prediction_log.txt", help="日志文件路径")
    parser.add_argument("--max-wait-hours", type=float, default=4.0, help="最大等待时间（小时）")
    parser.add_argument("--update-interval", type=int, default=60, help="更新间隔（秒）")
    parser.add_argument("--no-sound", action="store_true", help="禁用报警声")

    args = parser.parse_args()

    # 检查模型文件是否存在
    if not os.path.exists(args.model_file):
        print(f"❌ 模型文件不存在: {args.model_file}")
        print("请先运行 LightGBM_percentage_v2.py 训练模型")
        return

    if not os.path.exists(args.config_file):
        print(f"❌ 配置文件不存在: {args.config_file}")
        print("请先运行 LightGBM_percentage_v2.py 训练模型")
        return

    # 创建预测器并运行
    predictor = RealTimePredictor(
        model_file=args.model_file,
        config_file=args.config_file,
        log_file=args.log_file,
        max_wait_hours=args.max_wait_hours,
        update_interval=args.update_interval,
        enable_sound=not args.no_sound  # 如果指定了--no-sound则禁用声音
    )

    predictor.run()

if __name__ == "__main__":
    main()
