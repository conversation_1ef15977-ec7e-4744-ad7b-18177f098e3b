import pandas as pd
import time
import os
import csv
import argparse

def simulate_live_data(source_file, start_row=0, start_time=None, speed=1.0):
    """
    从历史CSV文件中逐行读取数据，并模拟实时数据流。
    优先使用 start_time，如果未提供，则使用 start_row。

    :param source_file: 源历史数据CSV文件路径。
    :param start_row: 模拟的起始行号（当start_time为None时生效）。
    :param start_time: 模拟的起始时间字符串 (例如 "2022-01-15 10:00:00")。
    :param speed: 每条新数据之间等待的秒数。
    """
    destination_file = 'live_btc_data.csv'

    # --- 1. 加载和准备数据 ---
    print(f"正在从 {source_file} 加载历史数据...")
    try:
        df_source = pd.read_csv(source_file)
    except FileNotFoundError:
        print(f"错误：找不到源文件 '{source_file}'！请确保文件名正确且文件存在。")
        return

    # 将Unix时间戳转换为datetime对象，并设为索引，这是时间定位的关键
    df_source['Timestamp_dt'] = pd.to_datetime(df_source['Timestamp'], unit='s')
    df_source.set_index('Timestamp_dt', inplace=True)
    df_source.sort_index(inplace=True) # 必须排序以保证时间定位的正确性
    
    actual_start_index = 0

    # --- 2. 确定模拟的起始点 ---
    if start_time:
        try:
            # 将用户输入的时间字符串转换为datetime对象
            user_start_time = pd.to_datetime(start_time)
            print(f"正在定位时间点: {user_start_time}...")
            
            # 使用 searchsorted 高效地找到第一个大于等于指定时间的索引位置
            # side='left' 保证找到的是第一个匹配项
            actual_start_index = df_source.index.searchsorted(user_start_time, side='left')
            
            if actual_start_index >= len(df_source):
                print(f"错误：指定的开始时间 {start_time} 在数据文件的最后一个时间点之后。")
                return
            
            start_timestamp = df_source.index[actual_start_index]
            print(f"找到最接近的起始点 -> 时间: {start_timestamp}, 位于第 {actual_start_index} 行。")

        except Exception as e:
            print(f"错误：无法解析时间格式 '{start_time}'。请使用 'YYYY-MM-DD HH:MM:SS' 格式。")
            print(f"具体错误: {e}")
            return
    else:
        # 如果没有提供时间，则使用行号
        actual_start_index = start_row
        if actual_start_index >= len(df_source):
            print(f"错误：起始行 {start_row} 超出文件总行数 {len(df_source)}。")
            return
        start_timestamp = df_source.index[actual_start_index]
        print(f"将从指定的第 {actual_start_index} 行开始 -> 时间: {start_timestamp}")

    # --- 3. 重置目标文件 ---
    if os.path.exists(destination_file):
        os.remove(destination_file)
    
    header = df_source.columns.tolist()
    # 注意：我们原始的列名没有'Timestamp_dt'，所以要用原始的header
    original_header = pd.read_csv(source_file, nrows=0).columns.tolist()
    with open(destination_file, 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(original_header)
    
    print(f"模拟准备就绪。每 {speed} 秒产生一条新数据。按 Ctrl+C 停止。")
    print("-" * 50)
    
    # --- 4. 开始模拟循环 ---
    try:
        # 使用 .iloc 从我们计算出的确切行号开始迭代
        for index_pos, (timestamp, row) in enumerate(df_source.iloc[actual_start_index:].iterrows()):
            with open(destination_file, 'a', newline='') as f:
                writer = csv.writer(f)
                # 写入原始数据行，不包含我们添加的 'Timestamp_dt'
                writer.writerow(row.values)
            
            print(f"[{actual_start_index + index_pos}] 数据已生成 -> 时间: {timestamp}, 收盘价: {row['Close']}")
            time.sleep(speed)
            
    except KeyboardInterrupt:
        print("\n" + "-" * 50)
        print("模拟被用户手动停止。")
    except Exception as e:
        print(f"\n发生未知错误: {e}")

if __name__ == '__main__':
    parser = argparse.ArgumentParser(
        description="通过时间或行号模拟实时BTC数据流。",
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument("source_file", help="源历史数据CSV文件路径。")
    parser.add_argument(
        "--start-time", 
        type=str, 
        default=None, 
        help="模拟的起始时间。\n格式: 'YYYY-MM-DD HH:MM:SS' (需要用引号包围)。\n如果提供此项，将忽略 --start-row。"
    )
    parser.add_argument(
        "--start-row", 
        type=int, 
        default=0, 
        help="模拟的起始行号 (当 --start-time 未提供时生效)。"
    )
    parser.add_argument(
        "--speed", 
        type=float, 
        default=1.0, 
        help="每条新数据之间的间隔秒数。"
    )
    
    args = parser.parse_args()
    
    simulate_live_data(args.source_file, args.start_row, args.start_time, args.speed)