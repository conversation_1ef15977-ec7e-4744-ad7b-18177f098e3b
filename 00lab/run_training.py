#!/usr/bin/env python3
"""
模型训练启动器 - 选择不同的预测目标
"""

import subprocess
import sys
import os

def show_target_comparison():
    """显示不同目标的对比"""
    print("="*70)
    print("LightGBM模型训练 - 预测目标选择")
    print("="*70)
    
    print("\n📊 原目标：预测10分钟后价格是否上涨")
    print("✅ 优点：计算简单，时间固定，适合快速验证")
    print("❌ 缺点：忽略波动幅度，不考虑交易成本")
    print("📁 文件：LightGBM.py / LightGBM_fixed.py")
    
    print("\n🎯 新目标：预测先涨1%还是先跌1%")
    print("✅ 优点：符合实际交易，明确止盈止损，风险可控")
    print("❌ 缺点：计算复杂，可能长时间等待")
    print("📁 文件：LightGBM_percentage.py")
    
    print("\n💡 评分规则对比：")
    print("原目标：猜对+0.8分，猜错-1.0分（不对称）")
    print("新目标：成功+1分，失败-1分（对称）")

def check_requirements():
    """检查必要文件"""
    required_files = ['btcusd_1-min_data.csv']
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        print("\n请确保数据文件存在后再运行。")
        return False
    
    print("✅ 数据文件检查通过！")
    return True

def run_training(script_name, target_description):
    """运行训练脚本"""
    print(f"\n🚀 开始训练：{target_description}")
    print(f"📝 执行脚本：{script_name}")
    print("-" * 50)
    
    try:
        subprocess.run([sys.executable, script_name], check=True)
        print(f"\n✅ 训练完成！")
        
        # 显示生成的文件
        if "percentage" in script_name:
            model_file = "btc_percentage_model.joblib"
            config_file = "model_config_percentage.json"
            importance_file = "feature_importance_percentage.csv"
        else:
            model_file = "btc_prediction_model_fixed.joblib" if "fixed" in script_name else "btc_prediction_model.joblib"
            config_file = "model_config_fixed.json" if "fixed" in script_name else "model_config.json"
            importance_file = "feature_importance.csv"
        
        print(f"\n📁 生成的文件：")
        if os.path.exists(model_file):
            print(f"   ✅ {model_file}")
        if os.path.exists(config_file):
            print(f"   ✅ {config_file}")
        if os.path.exists(importance_file):
            print(f"   ✅ {importance_file}")
            
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 训练失败：{e}")
    except KeyboardInterrupt:
        print(f"\n⏹️  训练被用户中断")
    except FileNotFoundError:
        print(f"\n❌ 找不到脚本文件：{script_name}")

def main():
    """主函数"""
    show_target_comparison()
    
    # 检查必要文件
    if not check_requirements():
        return
    
    print("\n" + "="*50)
    print("请选择要训练的模型：")
    print("="*50)
    
    print("1. 原目标模型（固定时间）")
    print("2. 原目标模型（修复数据泄露版本）")
    print("3. 新目标模型（百分比目标）- 推荐")
    print("4. 查看对比分析")
    print("5. 退出")
    
    while True:
        choice = input("\n请输入选择 (1-5): ").strip()
        
        if choice == '1':
            run_training("LightGBM.py", "预测10分钟后价格是否上涨（原版本）")
            break
        elif choice == '2':
            run_training("LightGBM_fixed.py", "预测10分钟后价格是否上涨（修复版本）")
            break
        elif choice == '3':
            run_training("LightGBM_percentage.py", "预测先涨1%还是先跌1%（推荐）")
            break
        elif choice == '4':
            show_detailed_comparison()
        elif choice == '5':
            print("👋 退出训练")
            return
        else:
            print("❌ 请输入 1-5 之间的数字")

def show_detailed_comparison():
    """显示详细对比"""
    print("\n" + "="*60)
    print("详细对比分析")
    print("="*60)
    
    print("\n🔍 技术差异：")
    print("┌─────────────────┬─────────────────┬─────────────────┐")
    print("│      特性       │    原目标模型   │   新目标模型    │")
    print("├─────────────────┼─────────────────┼─────────────────┤")
    print("│ 预测时间        │     固定10分钟  │     动态时间    │")
    print("│ 目标幅度        │     任意幅度    │     固定1%      │")
    print("│ 止盈止损        │     无明确点    │     明确设定    │")
    print("│ 风险控制        │     较弱        │     较强        │")
    print("│ 实际应用        │     理论研究    │     实际交易    │")
    print("│ 计算复杂度      │     简单        │     中等        │")
    print("└─────────────────┴─────────────────┴─────────────────┘")
    
    print("\n💰 收益计算：")
    print("原目标：收益 = (10分钟后价格 - 当前价格) / 当前价格")
    print("新目标：收益 = ±1%（达到目标立即平仓）")
    
    print("\n⚠️  风险评估：")
    print("原目标：可能承受大幅亏损，无明确止损")
    print("新目标：最大亏损1%，风险可控")
    
    print("\n🎯 推荐使用场景：")
    print("原目标：学术研究、概念验证、快速测试")
    print("新目标：实际交易、策略开发、风险管理")
    
    input("\n按回车键返回主菜单...")

if __name__ == '__main__':
    main()
