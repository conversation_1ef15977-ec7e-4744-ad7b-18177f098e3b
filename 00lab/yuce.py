# ===================================================================
#      这是一个完整的、可运行的实时预测脚本
# ===================================================================
import pandas as pd
import joblib
import json
import numpy as np # LightGBM有时需要numpy

# -------------------------------------------------------------------
#      第1部分: 特征工程函数 (你的“菜谱”)
# -------------------------------------------------------------------
def calculate_all_features(df):
    """
    接收一个原始的OHLCV DataFrame，返回一个计算好所有特征的DataFrame。
    这个函数里的代码必须和你的训练代码100%一致！
    """
    print("开始计算特征...")
    
    # 准备工作
    epsilon = 1e-9

    # --- 函数1: 计算标准化的K线特征 (可复用) ---
    def get_standardized_kline_features(kdf, timeframe_suffix=''):
        kdf = kdf.copy() # 避免SettingWithCopyWarning
        # 1. 计算ATR
        high_low = kdf['high'] - kdf['low']
        high_prev_close = abs(kdf['high'] - kdf['close'].shift(1))
        low_prev_close = abs(kdf['low'] - kdf['close'].shift(1))
        tr = pd.concat([high_low, high_prev_close, low_prev_close], axis=1).max(axis=1)
        atr_col = f'atr_14{timeframe_suffix}'
        kdf[atr_col] = tr.rolling(window=14).mean()

        # 2. 计算形态的绝对值
        body_size = abs(kdf['close'] - kdf['open'])
        price_range = kdf['high'] - kdf['low']
        upper_shadow = kdf['high'] - kdf[['open', 'close']].max(axis=1)
        lower_shadow = kdf[['open', 'close']].min(axis=1) - kdf['low']
        
        # 3. 标准化
        kdf[f'body_percent_of_range{timeframe_suffix}'] = body_size / (price_range + epsilon)
        kdf[f'upper_shadow_percent_of_range{timeframe_suffix}'] = upper_shadow / (price_range + epsilon)
        kdf[f'lower_shadow_percent_of_range{timeframe_suffix}'] = lower_shadow / (price_range + epsilon)
        kdf[f'range_norm_by_atr{timeframe_suffix}'] = price_range / (kdf[atr_col] + epsilon)
        
        return kdf

    # --- 函数2: 计算正在形成的K线特征 ---
    def add_live_kline_features(main_df, timeframe):
        tf_str = f'{timeframe}T'
        suffix = f'_live_{timeframe}m'
        main_df['start_of_bar'] = main_df.index.floor(tf_str)
        grouped = main_df.groupby('start_of_bar')
        live_open = grouped['open'].transform('first')
        live_high = grouped['high'].transform('cummax')
        live_low = grouped['low'].transform('cummin')
        live_close = main_df['close']
        live_body_size = abs(live_close - live_open)
        live_price_range = live_high - live_low
        live_upper_shadow = live_high - pd.concat([live_open, live_close], axis=1).max(axis=1)
        live_lower_shadow = pd.concat([live_open, live_close], axis=1).min(axis=1) - live_low
        main_df[f'body_percent{suffix}'] = live_body_size / (live_price_range + epsilon)
        main_df[f'upper_shadow_percent{suffix}'] = live_upper_shadow / (live_price_range + epsilon)
        main_df[f'lower_shadow_percent{suffix}'] = live_lower_shadow / (live_price_range + epsilon)
        main_df[f'range_norm_by_atr{suffix}'] = live_price_range / (main_df['atr_14_1m'] + epsilon)
        main_df.drop('start_of_bar', axis=1, inplace=True)
        return main_df

    # --- 执行特征工程 ---
    # 价格/动量/趋势特征
    for n in [1, 3, 5, 10, 30, 60, 120, 240]:
        df[f'return_{n}min'] = df['close'].pct_change(n)
    for n in [10, 30, 60, 120, 240]:
        df[f'sma_{n}'] = df['close'].rolling(window=n).mean()
        df[f'price_div_sma_{n}'] = df['close'] / (df[f'sma_{n}'] + epsilon)
    df['sma_10_div_sma_30'] = df['sma_10'] / (df['sma_30'] + epsilon)

    # 成交量特征
    for n in [10, 30, 60, 120, 240]:
        df[f'vma_{n}'] = df['volume'].rolling(window=n).mean()
        df[f'volume_div_vma_{n}'] = df['volume'] / (df[f'vma_{n}'] + epsilon)
    
    # VWAP
    df['price_x_volume'] = df['close'] * df['volume']
    vwap_numerator = df['price_x_volume'].rolling(window=30).sum()
    vwap_denominator = df['volume'].rolling(window=30).sum()
    df['vwap_30'] = vwap_numerator / (vwap_denominator + epsilon)
    df['price_div_vwap_30'] = df['close'] / (df['vwap_30'] + epsilon)
    df.drop('price_x_volume', axis=1, inplace=True)
    
    # 时间特征
    df['hour'] = df.index.hour
    df['day_of_week'] = df.index.dayofweek

    # K线形态特征 (多层次)
    df = get_standardized_kline_features(df, timeframe_suffix='_1m')
    for window in [3, 5]:
        df[f'body_percent_mean_{window}m'] = df['body_percent_of_range_1m'].rolling(window=window).mean()
        df[f'range_norm_by_atr_mean_{window}m'] = df['range_norm_by_atr_1m'].rolling(window=window).mean()
    for tf in [5, 15]:
        df_tf = df.resample(f'{tf}T').agg({'open':'first', 'high':'max', 'low':'min', 'close':'last'})
        df_tf = get_standardized_kline_features(df_tf, timeframe_suffix=f'_{tf}m_completed')
        features_to_merge = [col for col in df_tf.columns if f'_{tf}m_completed' in col]
        df = pd.merge_asof(df, df_tf[features_to_merge], left_index=True, right_index=True, direction='backward')
    for tf in [5, 15]:
        df = add_live_kline_features(df, tf)

    # 最终清理
    df.dropna(inplace=True)
    
    print("特征计算完成。")
    return df

# -------------------------------------------------------------------
#      第2部分: 数据获取函数 (你的“采购员”)
# -------------------------------------------------------------------
def get_latest_data(minutes_needed=300):
    """
    这个函数负责获取最新的K线数据。
    你需要根据你的实际情况修改这部分代码。
    """
    print("正在获取最新K线数据...")
    
    # ###############################################################
    # ##                                                             ##
    # ##  你需要修改这里！将 'live_btc_data.csv' 换成你的数据源  ##
    # ##                                                             ##
    # ###############################################################
    # 假设你的实时数据会不断写入到'live_btc_data.csv'这个文件
    # 如果你是从API获取，就在这里写API请求代码
    
    try:
        # 这是一个示例，你需要替换它
        live_data_df = pd.read_csv('live_btc_data.csv') 
    except FileNotFoundError:
        print("错误: 找不到数据文件 'live_btc_data.csv'。")
        print("请确保你有一个实时更新的数据文件，或者修改 get_latest_data 函数来从API获取数据。")
        return None

    # --- 数据预处理 (和训练时保持一致) ---
    live_data_df['Timestamp'] = pd.to_datetime(live_data_df['Timestamp'], unit='s')
    live_data_df.set_index('Timestamp', inplace=True)
    live_data_df.rename(columns={
        'Open':'open', 'High':'high', 'Low':'low', 'Close':'close', 'Volume':'volume'
    }, inplace=True)
    
    # 确保数据足够
    if len(live_data_df) < minutes_needed:
        print(f"警告: 数据不足，需要至少 {minutes_needed} 分钟, 当前只有 {len(live_data_df)} 分钟。")
        return None
        
    return live_data_df.tail(minutes_needed) # 只返回需要的部分

# -------------------------------------------------------------------
#      第3部分: 主程序 (你的“厨房总管”)
# -------------------------------------------------------------------
def make_prediction():
    """
    执行一次完整的实时预测流程。
    """
    # 1. 加载模型和配置
    print("加载模型和配置...")
    try:
        model = joblib.load('btc_prediction_model.joblib')
        with open('model_config.json', 'r') as f:
            config = json.load(f)
    except FileNotFoundError as e:
        print(f"错误：找不到模型或配置文件！ {e}")
        print("请确保 btc_prediction_model.joblib 和 model_config.json 文件与本脚本在同一个文件夹下。")
        return

    best_threshold = config['best_threshold']
    feature_list = config['feature_list']
    
    # 2. 获取最新数据
    live_data_df = get_latest_data()
    if live_data_df is None:
        return # 如果获取数据失败，则退出

    # 3. 计算特征
    features_df = calculate_all_features(live_data_df.copy()) # 传入副本以防修改原始数据
    
    # 检查是否有可用的数据行进行预测
    if features_df.empty:
        print("错误：计算特征后没有剩下任何数据。可能是因为初始数据太少，dropna()后变空。")
        return

    # 4. 准备模型输入
    latest_features = features_df[feature_list].iloc[-1:]
    
    # 5. 进行预测
    print("模型正在预测...")
    probability = model.predict_proba(latest_features)[0, 1]
    
    # 6. 做出决策
    print("\n" + "="*20 + " 最终决策 " + "="*20)
    print(f"当前时间: {latest_features.index[0]}")
    print(f"预测10分钟后上涨的概率是: {probability:.4f}")
    
    if probability > best_threshold:
        print(f"决策: 猜测【涨】！ (信心 {probability:.2f} > {best_threshold:.2f})")
    elif probability < (1 - best_threshold):
        print(f"决策: 猜测【跌】！ (信心 {probability:.2f} < {1-best_threshold:.2f})")
    else:
        print(f"决策: 【放弃】本次猜测。 (信心不足)")
    print("="*54 + "\n")

if __name__ == '__main__':
    make_prediction()