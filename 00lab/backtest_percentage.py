import pandas as pd
import joblib
import json
import numpy as np
import time
import os
import csv
import argparse
import pytz

# ===================================================================
#      回测模拟器 - 预测先涨1%还是先跌1%
# ===================================================================

def calculate_features(df, epsilon=1e-9):
    """计算技术指标特征（基于重要性分析优化，与训练时保持一致）"""
    df_work = df.copy()

    # 1. 时间特征（最重要）
    df_work['hour'] = df_work.index.hour  # 重要性: 290
    df_work['day_of_week'] = df_work.index.dayofweek  # 重要性: 191

    # 2. ATR特征（第3重要）
    df_work = get_standardized_kline_features(df_work, timeframe_suffix='_1m', epsilon=epsilon)

    # 3. 价格/动量特征（保留重要的）
    df_work['return_60min'] = df_work['close'].pct_change(60)  # 重要性: 134
    df_work['return_30min'] = df_work['close'].pct_change(30)  # 重要性: 41
    df_work['return_10min'] = df_work['close'].pct_change(10)  # 重要性: 13
    df_work['return_5min'] = df_work['close'].pct_change(5)   # 重要性: 11
    df_work['return_3min'] = df_work['close'].pct_change(3)   # 重要性: 3

    # 4. 波动率特征（保留重要的）
    for n in [10, 30, 60]:
        df_work[f'volatility_{n}'] = df_work['close'].rolling(window=n).std()
        df_work[f'volatility_ratio_{n}'] = df_work[f'volatility_{n}'] / (df_work['close'] + epsilon)

    # 5. 趋势特征（保留重要的）
    for n in [10, 30, 60]:
        df_work[f'sma_{n}'] = df_work['close'].rolling(window=n).mean()
        df_work[f'price_div_sma_{n}'] = df_work['close'] / (df_work[f'sma_{n}'] + epsilon)
    df_work['sma_10_div_sma_30'] = df_work['sma_10'] / (df_work['sma_30'] + epsilon)  # 重要性: 14

    # 6. VWAP特征（保留重要的）
    df_work['price_x_volume'] = df_work['close'] * df_work['volume']
    vwap_numerator = df_work['price_x_volume'].rolling(window=30).sum()
    vwap_denominator = df_work['volume'].rolling(window=30).sum()
    df_work['vwap_30'] = vwap_numerator / (vwap_denominator + epsilon)
    df_work['price_div_vwap_30'] = df_work['close'] / (df_work['vwap_30'] + epsilon)  # 重要性: 46
    df_work.drop('price_x_volume', axis=1, inplace=True)

    # 7. 成交量特征（只保留最重要的）
    df_work['vma_60'] = df_work['volume'].rolling(window=60).mean()
    df_work['volume_div_vma_60'] = df_work['volume'] / (df_work['vma_60'] + epsilon)  # 重要性: 5

    # 8. 滚动K线特征（保留重要的）
    for window in [3, 5]:
        if f'range_norm_by_atr_1m' in df_work.columns:
            df_work[f'range_norm_by_atr_mean_{window}m'] = df_work['range_norm_by_atr_1m'].rolling(window=window).mean()

    # 只保留body_percent_mean_5m（重要性: 3）
    if f'body_percent_of_range_1m' in df_work.columns:
        df_work[f'body_percent_mean_5m'] = df_work['body_percent_of_range_1m'].rolling(window=5).mean()

    return df_work

def get_standardized_kline_features(df, timeframe_suffix='', epsilon=1e-9):
    """计算标准化的K线特征"""
    try:
        high_low = df['high'] - df['low']
        high_prev_close = abs(df['high'] - df['close'].shift(1))
        low_prev_close = abs(df['low'] - df['close'].shift(1))
        tr = pd.concat([high_low, high_prev_close, low_prev_close], axis=1).max(axis=1)
        atr_col = f'atr_14{timeframe_suffix}'
        df[atr_col] = tr.rolling(window=14).mean()

        body_size = abs(df['close'] - df['open'])
        price_range = df['high'] - df['low']
        upper_shadow = df['high'] - df[['open', 'close']].max(axis=1)
        lower_shadow = df[['open', 'close']].min(axis=1) - df['low']

        df[f'body_percent_of_range{timeframe_suffix}'] = body_size / (price_range + epsilon)
        df[f'upper_shadow_percent_of_range{timeframe_suffix}'] = upper_shadow / (price_range + epsilon)
        df[f'lower_shadow_percent_of_range{timeframe_suffix}'] = lower_shadow / (price_range + epsilon)
        df[f'range_norm_by_atr{timeframe_suffix}'] = price_range / (df[atr_col] + epsilon)

    except Exception as e:
        print(f"K线特征计算出错: {e}")

    return df

def make_prediction_and_print(model, config, features_df):
    """执行预测"""
    try:
        best_threshold = config['best_threshold']
        feature_list = config['feature_list']

        latest_features = features_df[feature_list].iloc[-1:]
        current_price = features_df.iloc[-1]['close']

        probability = model.predict_proba(latest_features)[0, 1]

        guess = None
        if probability > best_threshold:
            guess = 1  # 预测先涨1%
        elif probability < (1 - best_threshold):
            guess = 0  # 预测先跌1%

        return guess, probability, current_price

    except Exception as e:
        print(f"预测时发生错误: {e}")
        return None, None, None

def check_percentage_result(start_price, price_history, up_threshold=0.01, down_threshold=0.01, max_minutes=120):
    """
    检查在给定时间内是否先达到涨跌幅目标
    返回: (result, actual_minutes)
    result: 1=先涨1%, 0=先跌1%, None=都没达到
    """
    up_target = start_price * (1 + up_threshold)
    down_target = start_price * (1 - down_threshold)

    for i, price in enumerate(price_history[:max_minutes]):
        if price >= up_target:
            return 1, i + 1  # 先涨1%
        elif price <= down_target:
            return 0, i + 1  # 先跌1%

    return None, max_minutes  # 都没达到

def run_backtest_simulation(source_file, start_row, start_time, speed, initial_buffer):
    """运行回测模拟"""
    destination_file = 'live_btc_data.csv'

    # 加载模型和配置
    try:
        model = joblib.load('btc_percentage_model.joblib')
        with open('model_config_percentage.json', 'r') as f:
            config = json.load(f)
    except FileNotFoundError as e:
        print(f"错误：找不到模型文件！ {e}")
        print("请先运行 LightGBM_percentage.py 训练模型。")
        return

    # 加载源数据
    df_source = pd.read_csv(source_file)
    df_source['Timestamp_dt'] = pd.to_datetime(df_source['Timestamp'], unit='s', utc=True).dt.tz_convert('Asia/Shanghai')
    df_source.set_index('Timestamp_dt', inplace=True, drop=False)
    df_source.sort_index(inplace=True)

    # 初始化状态
    current_guess = None
    total_score = 0
    wins = 0
    losses = 0
    timeouts = 0

    print("🎯 百分比目标模式：预测先涨1%还是先跌1%")
    print("📊 评分规则：成功+1分，失败-1分")

    # 确定起始点
    actual_start_index = start_row
    if start_time:
        try:
            user_start_time = pd.to_datetime(start_time)
            if user_start_time.tz is None:
                user_start_time = user_start_time.tz_localize('Asia/Shanghai')
            else:
                user_start_time = user_start_time.tz_convert('Asia/Shanghai')
            actual_start_index = df_source.index.searchsorted(user_start_time, side='left')
        except Exception as e:
            print(f"时间格式错误: {start_time}, 错误信息: {e}")
            return

    if actual_start_index < initial_buffer:
        actual_start_index = initial_buffer

    # 准备初始数据
    buffer_start_index = actual_start_index - initial_buffer
    initial_data = df_source.iloc[buffer_start_index:actual_start_index]
    original_header = pd.read_csv(source_file, nrows=0).columns.tolist()
    initial_data[original_header].to_csv(destination_file, index=False)

    print("-" * 60 + f"\n模拟开始于 {df_source.index[actual_start_index]}！按 Ctrl+C 停止。\n" + "-" * 60)

    # 主循环
    try:
        for index_timestamp, row in df_source.iloc[actual_start_index:].iterrows():
            # 更新数据文件
            with open(destination_file, 'a', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(row[original_header].values)

            current_time = index_timestamp
            current_price = row['Close']
            print(f"数据更新 -> 时间: {current_time}, 价格: {current_price:.2f}")

            # 检查是否有猜测需要验证
            if current_guess is not None:
                # 获取从猜测开始到现在的价格历史
                guess_start_idx = df_source.index.get_loc(current_guess['guess_time'])
                current_idx = df_source.index.get_loc(current_time)

                if current_idx > guess_start_idx:
                    price_history = df_source.iloc[guess_start_idx+1:current_idx+1]['Close'].values

                    if len(price_history) > 0:
                        result, actual_minutes = check_percentage_result(
                            current_guess['guess_price'],
                            price_history,
                            up_threshold=0.01,
                            down_threshold=0.01,
                            max_minutes=120
                        )

                        # 如果有结果或者超过最大时间，进行验证
                        if result is not None or actual_minutes >= 120:
                            print("="*20 + " 🚀 结果验证 " + "="*20)
                            print(f"验证在 {current_guess['guess_time']} 做出的猜测:")

                            if result is not None and current_guess['guess_direction'] == result:
                                total_score += 1
                                wins += 1
                                direction_str = "先涨1%" if result == 1 else "先跌1%"
                                print(f"✅ 猜对了! 实际{direction_str} (用时{actual_minutes}分钟)")
                                print(f"\033[92m得分: +1, 当前总分: {total_score:+d}\033[0m")
                            elif result is not None:
                                # 猜错了，扣分
                                total_score -= 1
                                losses += 1
                                actual_str = "先涨1%" if result == 1 else "先跌1%"
                                guess_str = "先涨1%" if current_guess['guess_direction'] == 1 else "先跌1%"
                                print(f"❌ 猜错了! 猜测{guess_str}，实际{actual_str}")
                                print(f"\033[91m得分: -1, 当前总分: {total_score:+d}\033[0m")
                            else:
                                # 超时，不扣分，只记录为无效猜测
                                timeouts += 1
                                print(f"⏰ 超时! 120分钟内都没有达到1%的涨跌幅")
                                print(f"\033[93m得分: 0 (超时不计分), 当前总分: {total_score:+d}\033[0m")

                            print("="*54)
                            current_guess = None

            # 只有在没有待验证猜测时才能进行新的预测
            if current_guess is None:
                try:
                    live_data_df = pd.read_csv(destination_file)
                    live_data_df['Timestamp'] = pd.to_datetime(live_data_df['Timestamp'], unit='s', utc=True).dt.tz_convert('Asia/Shanghai')
                    live_data_df.set_index('Timestamp', inplace=True)
                    live_data_df.rename(columns={'Open':'open', 'High':'high', 'Low':'low', 'Close':'close', 'Volume':'volume'}, inplace=True)

                    if len(live_data_df) >= 300:
                        features_df = calculate_features(live_data_df.copy())

                        if not features_df.empty:
                            features_df_clean = features_df.dropna()
                            if not features_df_clean.empty:
                                guess, probability, price_at_guess = make_prediction_and_print(model, config, features_df_clean)

                                if guess is not None:
                                    current_guess = {
                                        'guess_price': price_at_guess,
                                        'guess_direction': guess,
                                        'guess_time': current_time
                                    }
                                    direction_str = "先涨1%" if guess == 1 else "先跌1%"
                                    up_target = price_at_guess * 1.01
                                    down_target = price_at_guess * 0.99
                                    print(f"🎯 模型决策: 预测【{direction_str}】 (信心: {probability:.3f})")
                                    print(f"📈 上涨目标: ${up_target:.2f}, 📉 下跌目标: ${down_target:.2f}")
                                    print(f"⏰ 最大等待时间: 120分钟\n")
                                else:
                                    print("📊 模型分析: 信心不足，放弃本次猜测")

                except Exception as e:
                    print(f"特征计算或预测出错: {e}")

            time.sleep(speed)

    except KeyboardInterrupt:
        print("\n" + "-" * 50 + "\n模拟被用户停止。")
    finally:
        print("\n" + "="*25 + " 模拟总结 " + "="*25)
        total_trades = wins + losses
        total_guesses = wins + losses + timeouts
        win_rate = (wins / total_trades * 100) if total_trades > 0 else 0
        valid_rate = (total_trades / total_guesses * 100) if total_guesses > 0 else 0

        print(f"总得分: {total_score:+d}")
        print(f"总猜测次数: {total_guesses}")
        print(f"有效猜测: {total_trades} (成功: {wins}, 失败: {losses})")
        print(f"超时猜测: {timeouts} (不计分)")
        print(f"成功率: {win_rate:.2f}% (基于有效猜测)")
        print(f"有效率: {valid_rate:.2f}% (有效猜测/总猜测)")
        print("="*64)

        if os.path.exists(destination_file):
            os.remove(destination_file)

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="运行百分比目标回测模拟器 - 预测先涨1%还是先跌1%")
    parser.add_argument("source_file", help="源历史数据CSV文件。")
    parser.add_argument("--start-time", type=str, default=None, help="模拟的起始时间（北京时间）。格式: 'YYYY-MM-DD HH:MM:SS'")
    parser.add_argument("--start-row", type=int, default=5000, help="模拟的起始行号")
    parser.add_argument("--speed", type=float, default=0.1, help="模拟速度，秒/行")

    args = parser.parse_args()
    INITIAL_BUFFER_SIZE = 2000
    run_backtest_simulation(args.source_file, args.start_row, args.start_time, args.speed, INITIAL_BUFFER_SIZE)
