import pandas as pd
import numpy as np

def calculate_all_features(df):
    """
    接收一个原始的OHLCV DataFrame，返回一个计算好所有特征的DataFrame。
    这是项目中关于特征计算的唯一真实来源。
    """
    # 准备工作
    epsilon = 1e-9

    # --- 函数1: 计算标准化的K线特征 (可复用) ---
    def get_standardized_kline_features(kdf, timeframe_suffix=''):
        kdf = kdf.copy()
        high_low = kdf['high'] - kdf['low']
        high_prev_close = abs(kdf['high'] - kdf['close'].shift(1))
        low_prev_close = abs(kdf['low'] - kdf['close'].shift(1))
        tr = pd.concat([high_low, high_prev_close, low_prev_close], axis=1).max(axis=1)
        atr_col = f'atr_14{timeframe_suffix}'
        kdf[atr_col] = tr.rolling(window=14).mean()
        body_size = abs(kdf['close'] - kdf['open'])
        price_range = kdf['high'] - kdf['low']
        upper_shadow = kdf['high'] - kdf[['open', 'close']].max(axis=1)
        lower_shadow = kdf[['open', 'close']].min(axis=1) - kdf['low']
        kdf[f'body_percent_of_range{timeframe_suffix}'] = body_size / (price_range + epsilon)
        kdf[f'upper_shadow_percent_of_range{timeframe_suffix}'] = upper_shadow / (price_range + epsilon)
        kdf[f'lower_shadow_percent_of_range{timeframe_suffix}'] = lower_shadow / (price_range + epsilon)
        kdf[f'range_norm_by_atr{timeframe_suffix}'] = price_range / (kdf[atr_col] + epsilon)
        return kdf

    # --- 函数2: 计算正在形成的K线特征 ---
    def add_live_kline_features(main_df, timeframe):
        tf_str = f'{timeframe}min'
        suffix = f'_live_{timeframe}m'
        main_df['start_of_bar'] = main_df.index.floor(tf_str)
        grouped = main_df.groupby('start_of_bar')
        live_open = grouped['open'].transform('first')
        live_high = grouped['high'].transform('cummax')
        live_low = grouped['low'].transform('cummin')
        live_close = main_df['close']
        live_body_size = abs(live_close - live_open)
        live_price_range = live_high - live_low
        live_upper_shadow = live_high - pd.concat([live_open, live_close], axis=1).max(axis=1)
        live_lower_shadow = pd.concat([live_open, live_close], axis=1).min(axis=1) - live_low
        main_df[f'body_percent{suffix}'] = live_body_size / (live_price_range + epsilon)
        main_df[f'upper_shadow_percent{suffix}'] = live_upper_shadow / (live_price_range + epsilon)
        main_df[f'lower_shadow_percent{suffix}'] = live_lower_shadow / (live_price_range + epsilon)
        main_df[f'range_norm_by_atr{suffix}'] = live_price_range / (df['atr_14_1m'] + epsilon)
        main_df.drop('start_of_bar', axis=1, inplace=True)
        return main_df

    # --- 执行特征工程 ---
    for n in [1, 3, 5, 10, 30, 60, 120, 240]:
        df[f'return_{n}min'] = df['close'].pct_change(n)
    for n in [10, 30, 60, 120, 240]:
        df[f'sma_{n}'] = df['close'].rolling(window=n).mean()
        df[f'price_div_sma_{n}'] = df['close'] / (df[f'sma_{n}'] + epsilon)
    df['sma_10_div_sma_30'] = df['sma_10'] / (df['sma_30'] + epsilon)
    for n in [10, 30, 60, 120, 240]:
        df[f'vma_{n}'] = df['volume'].rolling(window=n).mean()
        df[f'volume_div_vma_{n}'] = df['volume'] / (df[f'vma_{n}'] + epsilon)
    df['price_x_volume'] = df['close'] * df['volume']
    vwap_numerator = df['price_x_volume'].rolling(window=30).sum()
    vwap_denominator = df['volume'].rolling(window=30).sum()
    df['vwap_30'] = vwap_numerator / (vwap_denominator + epsilon)
    df['price_div_vwap_30'] = df['close'] / (df['vwap_30'] + epsilon)
    df.drop('price_x_volume', axis=1, inplace=True)
    df['hour'] = df.index.hour
    df['day_of_week'] = df.index.dayofweek
    df = get_standardized_kline_features(df, timeframe_suffix='_1m')
    for window in [3, 5]:
        df[f'body_percent_mean_{window}m'] = df['body_percent_of_range_1m'].rolling(window=window).mean()
        df[f'range_norm_by_atr_mean_{window}m'] = df['range_norm_by_atr_1m'].rolling(window=window).mean()
    for tf in [5, 15]:
        df_tf = df.resample(f'{tf}min',label='right', closed='right').agg({'open':'first', 'high':'max', 'low':'min', 'close':'last'})
        df_tf = get_standardized_kline_features(df_tf, timeframe_suffix=f'_{tf}m_completed')
        features_to_merge = [col for col in df_tf.columns if f'_{tf}m_completed' in col]
        df = pd.merge_asof(df, df_tf[features_to_merge], left_index=True, right_index=True, direction='backward')
    for tf in [5, 15]:
        df = add_live_kline_features(df, tf)

    df.dropna(inplace=True)
    return df