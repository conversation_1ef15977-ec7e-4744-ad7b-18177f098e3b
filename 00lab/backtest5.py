# backtest_final_correct.py
import pandas as pd
import joblib
import json
import time
import argparse
import warnings

warnings.simplefilter(action='ignore', category=FutureWarning)

from feature import calculate_all_features
import config

def run_correct_simulation(start_time_str, speed):
    """
    最终正确的回测模拟器：先一次性计算所有特征，然后在模拟中只进行查找。
    """
    # --- 1. 加载所有工具和原始数据 ---
    print("--> 步骤 1/4: 正在加载所有资源...")
    model = joblib.load(config.MODEL_OUTPUT_FILE)
    model_config = json.load(open(config.CONFIG_OUTPUT_FILE, 'r'))
    raw_df = pd.read_csv(config.SOURCE_DATA_FILE)
    
    # --- 2. 一次性预处理完整数据 ---
    print("--> 步骤 2/4: 正在预处理完整历史数据...")
    raw_df['Timestamp'] = pd.to_datetime(raw_df['Timestamp'], unit='s')
    raw_df.set_index('Timestamp', inplace=True)
    raw_df.rename(columns={'Open':'open', 'High':'high', 'Low':'low', 'Close':'close', 'Volume':'volume'}, inplace=True)
    if config.START_DATE:
        raw_df = raw_df.loc[config.START_DATE:]
    raw_df.sort_index(inplace=True)
    
    # --- 3. 【核心】一次性预计算所有特征 ---
    print("--> 步骤 3/4: 正在对完整历史数据预计算所有特征... (这可能需要几分钟)")
    df_with_features_golden = calculate_all_features(raw_df.copy())
    print("    所有特征已预计算完毕！")

    # --- 4. 准备并开始模拟 ---
    feature_list = model_config['feature_list']
    best_threshold = model_config['best_threshold']
    
    # 确定模拟的起点
    start_time = pd.to_datetime(start_time_str)
    simulation_df = df_with_features_golden.loc[start_time:]
    
    if simulation_df.empty:
        print(f"错误：起始时间 {start_time_str} 在可计算特征的数据范围之后。")
        return
        
    # 初始化状态
    pending_guesses = {}
    total_score, wins, losses = 0, 0, 0
    
    print("-" * 50 + f"\n✅ 模拟开始于 {simulation_df.index[0]}！\n" + "-" * 50)
    
    # --- 主循环：现在只进行查找和预测 ---
    try:
        for current_time, row in simulation_df.iterrows():
            print(f"\n数据更新 -> 时间: {current_time}, 价格: {row['close']:.2f}")

            # a. 【新】直接从预计算的表格中查找特征向量
            features_row = row[feature_list]
            
            # b. 执行预测
            probability = model.predict_proba(features_row.to_frame().T)[0, 1]
            
            # c. 验证到期的猜测 (代码与v3版相同)
            # ...

            # d. 记录新的猜测
            guess = None
            if probability > best_threshold:
                guess = 1
            elif probability < (1 - best_threshold):
                guess = 0
            
            if guess is not None:
                # (记录和打印猜测的代码与v3版相同)
                direction_str = "涨" if guess == 1 else "跌"
                print(f"--- 模型决策: 猜测【{direction_str}】 (信心: {probability:.2f}) ---")

            time.sleep(speed)
            
    except KeyboardInterrupt:
        print("\n模拟被用户停止。")
    finally:
        # 打印总结
        pass

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="最终正确的回测模拟器。")
    parser.add_argument("start_time", help="模拟的起始时间。格式: 'YYYY-MM-DD HH:MM:SS'。")
    parser.add_argument("--speed", type=float, default=0.1, help="模拟速度，秒/行。")
    
    args = parser.parse_args()
    run_correct_simulation(args.start_time, args.speed)