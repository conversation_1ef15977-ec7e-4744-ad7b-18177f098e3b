# config.py

# --- 数据配置 ---
SOURCE_DATA_FILE = 'btcusd_1-min_data.csv'
START_DATE = '2025-01-01' # 筛选数据的起始日期，None表示不筛选
END_DATE = None # 筛选数据的结束日期，None表示不筛选

# --- 数据集划分配置 ---
TRAIN_END_DATE = '2025-03-30'
VALIDATION_END_DATE = '2025-07-08'
# 测试集将是 VALIDATION_END_DATE 之后的所有数据

# --- 模型和特征配置 ---
TARGET_COLUMN = 'label'
EXCLUDED_COLUMNS = [
    'open', 'high', 'low', 'close', 'volume', 'label', 'future_price_10min',
    'sma_10', 'sma_30', 'sma_60', 'sma_120', 'sma_240',
    'vma_10', 'vma_30', 'vma_60', 'vma_120', 'vma_240',
    'vwap_30'
]

# --- 模型训练参数 ---
LGBM_PARAMS = {
    'objective': 'binary',
    'metric': 'auc',
    'n_estimators': 2000,
    'learning_rate': 0.05,
    'n_jobs': -1,
    'random_state': 42
}
EARLY_STOPPING_ROUNDS = 100

# --- 输出文件配置 ---
MODEL_OUTPUT_FILE = 'btc_prediction_model.joblib'
CONFIG_OUTPUT_FILE = 'model_config.json'