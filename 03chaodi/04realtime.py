#!/usr/bin/env python3
"""
SUI/USDT 5分钟线实时预测脚本
基于已训练的LightGBM模型进行实时价格预测，支持多个并发预测信号。
特征计算逻辑从共享的 model_utils.py 模块导入。
日志系统经过优化，确保稳定写入文件并简化API错误记录。
"""

import pandas as pd
import joblib
import json
import numpy as np
import time
import requests
from datetime import datetime, timezone
import os
import signal
import sys
import logging
from typing import Dict, Optional, Tuple
import platform
import argparse

# 从共享模块导入特征计算函数，确保逻辑一致性
from model_utils import calculate_features

class SuiRealTimePredictor:
    def __init__(self,
                 model_file: str,
                 config_file: str,
                 log_file='sui_prediction_log.txt',
                 update_interval=30,
                 enable_sound=True):
        """
        初始化SUI实时预测器
        """
        self.model_file = model_file
        self.config_file = config_file
        self.log_file = log_file
        self.update_interval = update_interval
        self.enable_sound = enable_sound
        self.running = True
        
        self.model = None
        self.config = None
        self.timeframe_minutes = 5

        self.active_predictions: Dict[str, dict] = {}
        self.total_predictions = 0
        self.successful_predictions = 0
        self.failed_predictions = 0
        self.timeout_predictions = 0

        # 设置日志系统 (优化后)
        self.logger = self.setup_logging()

        # 设置信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        self.logger.info("="*20 + " 预测器启动 " + "="*20)
        self.logger.info(f"模型文件: {self.model_file}")
        self.logger.info(f"配置文件: {self.config_file}")
        self.logger.info(f"特征工程: 从 model_utils.py 引用")
        self.logger.info(f"日志文件: {self.log_file}")
        self.logger.info(f"更新间隔: {self.update_interval}秒")
        self.logger.info(f"报警声: {'启用' if self.enable_sound else '禁用'}")
        self.logger.info("按 Ctrl+C 停止预测\n")

    def signal_handler(self, signum, frame):
        """信号处理器，优雅退出"""
        self.logger.info(f"接收到退出信号 {signum}，正在停止...")
        self.running = False

    def setup_logging(self) -> logging.Logger:
        """
        设置专用的日志系统，确保日志稳定写入文件和控制台。
        """
        # 创建一个专用的logger，而不是配置root logger
        logger = logging.getLogger('SuiPredictor')
        logger.setLevel(logging.INFO)
        
        # 防止日志消息向上传递到root logger
        logger.propagate = False

        # 如果logger已经有handlers，先移除它们，避免重复添加
        if logger.hasHandlers():
            logger.handlers.clear()

        # 创建格式化器
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

        # 文件处理器
        file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

        # 控制台处理器
        stream_handler = logging.StreamHandler(sys.stdout)
        stream_handler.setLevel(logging.INFO)
        stream_handler.setFormatter(formatter)
        logger.addHandler(stream_handler)

        return logger

    def play_alert_sound(self):
        """播放报警声（跨平台）"""
        try:
            system = platform.system().lower()
            if system == "windows":
                import winsound
                winsound.MessageBeep(winsound.MB_ICONEXCLAMATION)
            elif system == "darwin":
                os.system('say "发现交易信号"')
            elif system == "linux":
                os.system('paplay /usr/share/sounds/freedesktop/stereo/complete.oga 2>/dev/null || echo -e "\\a"')
            else:
                print('\a' * 3)
        except Exception as e:
            print('\a' * 3)
            self.logger.warning(f"播放报警声失败: {e}")

    def log_message(self, event_type: str, message: str, data: dict = None, level: str = 'info'):
        """
        使用配置好的logger记录日志消息
        """
        log_entry = f"[{event_type}] {message}"
        if data:
            log_entry += f" | 数据: {json.dumps({k: (f'{v:.4f}' if isinstance(v, float) else v) for k, v in data.items()})}"
        
        log_method = getattr(self.logger, level, self.logger.info)
        log_method(log_entry)

    def load_model_and_config(self) -> bool:
        """加载模型和配置"""
        try:
            self.model = joblib.load(self.model_file)
            with open(self.config_file, 'r') as f:
                self.config = json.load(f)

            required_keys = ['feature_list', 'best_threshold', 'up_threshold', 'down_threshold', 'max_lookforward_minutes']
            if not all(key in self.config for key in required_keys):
                raise ValueError(f"配置文件缺少关键项，需要: {required_keys}")
            
            self.timeframe_minutes = self.config.get('timeframe_minutes', 5)

            self.log_message("系统", "模型和配置加载成功")
            self.logger.info(f"   - 模型类型: {self.config.get('model_type', 'Unknown')}")
            self.logger.info(f"   - 目标涨幅: {self.config['up_threshold']*100:.2f}%")
            self.logger.info(f"   - 目标跌幅: {self.config['down_threshold']*100:.2f}%")
            self.logger.info(f"   - 最优阈值: {self.config['best_threshold']:.3f}")
            self.logger.info(f"   - K线周期: {self.timeframe_minutes}分钟")
            self.logger.info(f"   - 最大等待: {self.config['max_lookforward_minutes']}分钟")
            self.logger.info(f"   - 特征数量: {len(self.config['feature_list'])}")
            return True

        except Exception as e:
            self.logger.critical(f"加载模型或配置失败: {e}", exc_info=True)
            return False

    def _fetch_binance_klines(self, limit=1000) -> Optional[pd.DataFrame]:
        """从Binance API获取K线数据并进行预处理"""
        try:
            url = "https://api.binance.com/api/v3/klines"
            params = {'symbol': 'SUIUSDT', 'interval': f'{self.timeframe_minutes}m', 'limit': limit}
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            klines = response.json()

            if not klines: return None

            df = pd.DataFrame(klines, columns=['Timestamp', 'Open', 'High', 'Low', 'Close', 'Volume', 'CloseTime', 'QuoteVolume', 'TradeCount', 'TakerBuyBaseVolume', 'TakerBuyQuoteVolume', 'Ignore'])
            df = df.astype({'Open': float, 'High': float, 'Low': float, 'Close': float, 'Volume': float})
            df['Timestamp'] = pd.to_datetime(df['Timestamp'], unit='ms')
            df.set_index('Timestamp', inplace=True)
            df.rename(columns={'Open': 'open', 'High': 'high', 'Low': 'low', 'Close': 'close', 'Volume': 'volume'}, inplace=True)
            
            return df[['open', 'high', 'low', 'close', 'volume']]
        except requests.exceptions.RequestException as e:
            # 简化API错误日志
            self.logger.warning(f"获取K线数据失败 (API请求错误): {e.__class__.__name__}")
            return None

    def get_initial_data(self) -> Optional[pd.DataFrame]:
        """获取初始历史数据"""
        self.logger.info("正在获取初始历史数据...")
        df = self._fetch_binance_klines(limit=1000)
        if df is not None:
            self.log_message("数据获取", f"初始数据获取成功，{len(df)}条记录")
        else:
            self.logger.error("获取初始数据失败，无法启动。")
        return df

    def get_latest_data(self) -> Optional[pd.Series]:
        """获取最新的已收盘K线数据"""
        df = self._fetch_binance_klines(limit=2)
        if df is not None and len(df) >= 2:
            return df.iloc[-2] # 返回倒数第二根，确保是已收盘的最新K线
        return None

    def make_prediction(self, df: pd.DataFrame) -> Tuple[Optional[int], float, float]:
        """使用最新数据进行预测"""
        try:
            longest_window = 720 // self.timeframe_minutes 
            if len(df) < longest_window + 20:
                self.logger.warning(f"数据不足 ({len(df)}条)，跳过此次预测。")
                return None, 0.0, 0.0

            features_df = calculate_features(df, timeframe=self.timeframe_minutes)
            features_df_clean = features_df.dropna()

            if features_df_clean.empty:
                self.logger.warning("特征计算后数据为空，无法预测。")
                return None, 0.0, 0.0

            feature_list = self.config['feature_list']
            latest_features_series = features_df_clean.iloc[-1]
            
            missing_features = [f for f in feature_list if f not in latest_features_series.index]
            if missing_features:
                 self.logger.error(f"预测时缺少特征: {missing_features}")
                 return None, 0.0, 0.0
            
            latest_features_df = latest_features_series[feature_list].to_frame().T
            current_price = latest_features_series['close']

            probability = self.model.predict_proba(latest_features_df)[0, 1]
            best_threshold = self.config['best_threshold']

            guess = None
            if probability > best_threshold:
                guess = 1
            elif probability < (1 - best_threshold):
                guess = 0

            return guess, probability, current_price

        except Exception as e:
            self.logger.error(f"预测过程中发生严重错误: {e}", exc_info=True)
            return None, 0.0, 0.0

    def add_prediction(self, guess: int, probability: float, price: float, timestamp: datetime):
        """添加新的预测到活跃列表"""
        prediction_id = f"pred_{timestamp.strftime('%Y%m%d_%H%M%S')}"
        max_wait = pd.Timedelta(minutes=self.config['max_lookforward_minutes'])
        expire_time = timestamp + max_wait

        prediction = {
            'id': prediction_id, 'guess': guess, 'probability': probability, 'price': price,
            'timestamp': timestamp, 'expire_time': expire_time,
            'up_target': price * (1 + self.config['up_threshold']),
            'down_target': price * (1 - self.config['down_threshold']), 'status': 'active'
        }
        self.active_predictions[prediction_id] = prediction
        self.total_predictions += 1
        direction_str = f"先涨{self.config['up_threshold']*100:.1f}%" if guess == 1 else f"先跌{self.config['down_threshold']*100:.1f}%"
        
        self.log_message("新预测", f"ID: {prediction_id}, 方向: {direction_str}", {
            'confidence': probability, 'price': price, 'up_target': prediction['up_target'], 'down_target': prediction['down_target']
        })
        if self.enable_sound: self.play_alert_sound()

    def check_predictions(self, current_price: float, current_time: datetime):
        """检查所有活跃预测的状态"""
        completed_ids = []
        for pred_id, pred in self.active_predictions.items():
            if pred['status'] != 'active': continue

            if current_price >= pred['up_target']:
                result, reason = (1, "达到上涨目标") if pred['guess'] == 1 else (0, "达到上涨目标 (预测错误)")
                self.complete_prediction(pred_id, result, current_price, current_time, reason)
                completed_ids.append(pred_id)
            elif current_price <= pred['down_target']:
                result, reason = (1, "达到下跌目标") if pred['guess'] == 0 else (0, "达到下跌目标 (预测错误)")
                self.complete_prediction(pred_id, result, current_price, current_time, reason)
                completed_ids.append(pred_id)
            elif current_time >= pred['expire_time']:
                self.complete_prediction(pred_id, -1, current_price, current_time, "超时")
                completed_ids.append(pred_id)
        
        for pred_id in completed_ids:
            if pred_id in self.active_predictions:
                del self.active_predictions[pred_id]

    def complete_prediction(self, pred_id: str, result: int, final_price: float, end_time: datetime, reason: str):
        """标记一个预测为完成并记录结果"""
        pred = self.active_predictions[pred_id]
        pred['status'] = 'completed'
        duration_minutes = (end_time - pred['timestamp']).total_seconds() / 60
        
        status_map = {1: "成功✅", 0: "失败❌", -1: "超时⏰"}
        status_str = status_map.get(result, "未知")

        if result == 1: self.successful_predictions += 1
        elif result == 0: self.failed_predictions += 1
        else: self.timeout_predictions += 1
        
        direction_str = f"先涨{self.config['up_threshold']*100:.1f}%" if pred['guess'] == 1 else f"先跌{self.config['down_threshold']*100:.1f}%"
        
        self.log_message("预测完成", f"ID: {pred_id}, 结果: {status_str}", {
            'direction': direction_str, 'confidence': pred['probability'], 'start_price': pred['price'],
            'end_price': final_price, 'duration_min': duration_minutes, 'reason': reason
        })

    def print_status(self):
        """定期打印当前状态摘要到日志"""
        active_count = len([p for p in self.active_predictions.values() if p['status'] == 'active'])
        valid_trades = self.successful_predictions + self.failed_predictions
        success_rate = (self.successful_predictions / max(1, valid_trades)) * 100

        self.logger.info("\n--- 状态摘要 ---\n"
                        f"  活跃预测: {active_count}\n"
                        f"  总预测数: {self.total_predictions}\n"
                        f"     成功: {self.successful_predictions}\n"
                        f"     失败: {self.failed_predictions}\n"
                        f"     超时: {self.timeout_predictions}\n"
                        f"  胜率 (非超时): {success_rate:.2f}%\n"
                        "------------------")
        
    def run(self):
        """运行实时预测主循环"""
        if not self.load_model_and_config():
            return

        df = self.get_initial_data()
        if df is None:
            return

        self.log_message("系统", f"开始实时预测 (每 {self.timeframe_minutes} 分钟一个新K线)")
        last_kline_timestamp = None

        while self.running:
            try:
                # 获取最新的实时价格用于检查止盈止损
                try:
                    price_response = requests.get("https://api.binance.com/api/v3/ticker/price?symbol=SUIUSDT", timeout=5)
                    if price_response.status_code == 200:
                        current_price = float(price_response.json()['price'])
                        self.check_predictions(current_price, datetime.now().astimezone())
                except requests.exceptions.RequestException:
                    pass # 对于价格检查的API失败，静默处理，等待下一次循环

                latest_kline = self.get_latest_data()
                if latest_kline is None:
                    time.sleep(10)
                    continue
                
                if last_kline_timestamp is None or latest_kline.name > last_kline_timestamp:
                    current_time = latest_kline.name.to_pydatetime().replace(tzinfo=timezone.utc).astimezone()
                    price_at_kline_close = latest_kline['close']
                    
                    self.log_message("K线更新", f"新的 {self.timeframe_minutes}m K线", {'timestamp': current_time.strftime('%Y-%m-%d %H:%M:%S'), 'price': price_at_kline_close})
                    
                    if latest_kline.name not in df.index:
                        df = pd.concat([df.iloc[1:], latest_kline.to_frame().T])
                    
                    guess, probability, pred_price = self.make_prediction(df)

                    if guess is not None:
                        self.add_prediction(guess, probability, pred_price, current_time)
                    else:
                        self.log_message("分析结果", "信心不足，放弃本次预测", {'confidence': probability})
                    
                    last_kline_timestamp = latest_kline.name

                    if self.total_predictions > 0 and self.total_predictions % 5 == 0:
                        self.print_status()

                time.sleep(self.update_interval)

            except KeyboardInterrupt:
                self.running = False
            except Exception as e:
                self.logger.error(f"主循环运行时错误: {e}", exc_info=True)
                time.sleep(15)
        
        self.print_final_summary()

    def print_final_summary(self):
        """打印并记录最终总结"""
        self.logger.info("\n" + "="*20 + " 实时预测总结 " + "="*20)
        self.print_status()
        self.log_message("系统", "预测器已停止", level='info')

def main():
    """主函数，处理命令行参数"""
    parser = argparse.ArgumentParser(description="SUI/USDT 5分钟线实时预测器")
    parser.add_argument("--model-file", default="sui_percentage_model.joblib", help="模型文件路径")
    parser.add_argument("--config-file", default="sui_model_config_percentage.json", help="模型配置文件路径")
    parser.add_argument("--log-file", default="sui_prediction_log.txt", help="日志文件路径")
    parser.add_argument("--update-interval", type=int, default=30, help="价格和预测检查的更新间隔（秒）")
    parser.add_argument("--no-sound", action="store_true", help="禁用报警声")
    args = parser.parse_args()

    if not os.path.exists(args.model_file) or not os.path.exists(args.config_file):
        print(f"❌ 错误: 模型文件 '{args.model_file}' 或配置文件 '{args.config_file}' 不存在。")
        print("   请确保您已经训练了SUI模型，并提供了正确的文件路径。")
        return

    predictor = SuiRealTimePredictor(
        model_file=args.model_file, config_file=args.config_file,
        log_file=args.log_file, update_interval=args.update_interval,
        enable_sound=not args.no_sound
    )
    predictor.run()

if __name__ == "__main__":
    main()