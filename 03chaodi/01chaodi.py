# -*- coding: utf-8 -*-

# --- 第0步：导入所需库 ---
import pandas as pd
import numpy as np
import pandas_ta as ta
from tqdm import tqdm
import xgboost as xgb
import matplotlib.pyplot as plt
from sklearn.metrics import classification_report

# TQDM an-d pandas integration for progress bars
tqdm.pandas()

print("======================================================")
print("方案七：多周期共振底识别模型")
print("======================================================")

# --- 第1步：数据加载与准备 ---
print(">>> 1. 正在加载数据...")
# 假设我们使用的是5分钟K线数据
# 如果不是，需要调整下方的 PERIODS_IN_XXX 常量
df = pd.read_csv('sui.csv')
df['Timestamp'] = pd.to_datetime(df['Timestamp'], unit='s')
df.set_index('Timestamp', inplace=True)
df.sort_index(inplace=True)
df = df[~df.index.duplicated(keep='first')]

# --- 第2步：【核心改进】定义多周期共振目标变量 ---
print(">>> 2. 正在定义'多周期共振底'目标变量...")

# 假设数据是5分钟K线, 计算每个周期包含的K线数量
CANDLES_IN_HOUR = 12  # 60 / 5 = 12
CANDLES_IN_4_HOURS = 4 * CANDLES_IN_HOUR
CANDLES_IN_DAY = 24 * CANDLES_IN_HOUR

# 定义一个函数，用于计算未来价格的稳定性
def calculate_future_price_stability(series, period, threshold=0.90):
    """
    计算在未来`period`个周期内，价格高于或等于当前价格的时间点所占的比例。
    这是一个前瞻性(forward-looking)的计算。

    Args:
        series (pd.Series): 价格序列 (例如 df['Close'])
        period (int): 向前看的周期数 (K线数量)
        threshold (float): 比例阈值，例如0.9代表90%

    Returns:
        pd.Series: A boolean Series, True if the stability condition is met.
    """
    # 使用rolling来创建滑动窗口，但我们需要的是前瞻性窗口
    # 实现方法：对每个点，比较其与未来 period-1 个点的关系
    # .rolling().apply() 在大数据上会很慢，这里我们用一种更高效的向量化方法
    # Shift a copy of the series for each step in the future
    higher_counts = pd.Series(0, index=series.index)
    current_price = series

    # 遍历未来时间步
    for i in range(1, period):
        future_price = series.shift(-i)
        higher_counts += (future_price >= current_price).astype(int)

    # 计算比例 (因为我们比较了 period-1 次)
    stability_ratio = higher_counts / (period - 1)

    return stability_ratio >= threshold


print("    - 计算1小时稳定性...")
df['is_bottom_1h'] = calculate_future_price_stability(df['Close'], CANDLES_IN_HOUR, 0.90)
print("    - 计算4小时稳定性...")
df['is_bottom_4h'] = calculate_future_price_stability(df['Close'], CANDLES_IN_4_HOURS, 0.90)
print("    - 计算1天稳定性...")
df['is_bottom_1d'] = calculate_future_price_stability(df['Close'], CANDLES_IN_DAY, 0.90)

# 定义最终的“共振”信号：必须同时满足所有周期的条件
df['trade_signal'] = (df['is_bottom_1h'] & df['is_bottom_4h'] & df['is_bottom_1d']).astype(int)

# 清理数据：删除计算过程中产生的辅助列和因shift产生的NaN值
df.drop(columns=['is_bottom_1h', 'is_bottom_4h', 'is_bottom_1d'], inplace=True)
df.dropna(inplace=True)

print(f"\n找到了 {df['trade_signal'].sum()} 个满足条件的多周期共振底。")

# --- 第3步：构建特征库 ---
print(">>> 3. 正在构建特征库...")
df.ta.rsi(length=14, append=True)
df.ta.stoch(append=True)
df.ta.adx(length=14, append=True) # ADX可以衡量趋势强度，可能对判断底部反转有用
df.ta.bbands(length=20, append=True) # 布林带可以衡量波动性和价格相对位置

df.dropna(inplace=True)

# --- 第4步：准备训练和测试数据 ---
print(">>> 4. 正在划分数据集...")
# 使用所有计算出的指标作为特征
features = ['RSI_14', 'STOCHk_14_3_3', 'STOCHd_14_3_3', 'ADX_14', 'BBL_20_2.0', 'BBM_20_2.0', 'BBU_20_2.0']
X = df[features]
y = df['trade_signal']

# 检查并处理特征列中的无穷大值
X.replace([np.inf, -np.inf], np.nan, inplace=True)
X.dropna(inplace=True)
y = y[X.index] # 确保y和X的索引对齐

split_index = int(len(df) * 0.8)
X_train, X_test = X.iloc[:split_index], X.iloc[split_index:]
y_train, y_test = y.iloc[:split_index], y.iloc[split_index:]

# --- 第5步：训练模型 ---
print(">>> 5. 正在训练XGBoost模型...")
# 由于正样本（共振底）可能非常稀少，使用 scale_pos_weight 来平衡样本权重
pos_count = y_train.sum()
neg_count = len(y_train) - pos_count
scale_pos_weight = neg_count / pos_count if pos_count > 0 else 1
print(f"    - 正负样本比例不平衡，设定 scale_pos_weight = {scale_pos_weight:.2f}")

model = xgb.XGBClassifier(
    objective='binary:logistic',
    eval_metric='logloss',
    n_estimators=1000,
    learning_rate=0.01,
    max_depth=5,
    early_stopping_rounds=50,
    use_label_encoder=False,
    scale_pos_weight=scale_pos_weight # 应用样本权重
)
model.fit(X_train, y_train, eval_set=[(X_test, y_test)], verbose=False)

# --- 第6步：在测试集上进行预测 ---
print(">>> 6. 正在测试集上进行预测...")
y_pred_proba = model.predict_proba(X_test)[:, 1]
test_df = df.iloc[split_index:].copy()
test_df['pred_prob'] = y_pred_proba

# --- 第7步：评估和信号筛选 ---
# --- 第7步：【核心改进】评估和信号筛选（动态阈值版） ---
print(">>> 7. 正在进行信号筛选...")

# 我们不再使用固定的概率阈值，而是选择概率最高的Top N%的信号
QUANTILE_THRESHOLD = 0.95 # 设定分位数，0.95代表我们要找的阈值是95%分位点，即筛选出Top 5%的信号

# 检查测试集中是否有预测概率，防止在空数据上操作
if not test_df.empty and 'pred_prob' in test_df.columns and not test_df['pred_prob'].empty:
    # 计算动态概率阈值
    dynamic_prob_threshold = test_df['pred_prob'].quantile(QUANTILE_THRESHOLD)

    print(f"\n使用动态阈值筛选信号：只取预测概率最高的 { (1-QUANTILE_THRESHOLD)*100 :.0f}% 的信号")
    print(f"    - 计算出的动态概率阈值 (95%分位点) 为: {dynamic_prob_threshold:.4f}")

    # 应用动态阈值进行筛选
    final_signals = test_df[test_df['pred_prob'] >= dynamic_prob_threshold]
    print(f"    - 最终筛选出 {len(final_signals)} 个顶级信号")

    # 打印分类报告，看看模型在这个高阈值下的表现
    # 注意：y_pred_class也必须使用相同的动态阈值来计算，以保证报告的准确性
    y_pred_class = (y_pred_proba >= dynamic_prob_threshold).astype(int)
    print("\n--- 模型在【高置信度阈值】下的分类报告 ---")
    # 设置 zero_division_behavior='warn' 或 0 来处理潜在的除零问题
    print(classification_report(y_test, y_pred_class, target_names=['Not a Bottom', 'Resonance Bottom'], zero_division=0))

else:
    print("\n测试集为空或无预测概率，无法进行筛选。")
    final_signals = pd.DataFrame() # 创建一个空的DataFrame，防止后续代码出错


# --- 第8步：结果可视化 ---
print(">>> 8. 正在生成结果图表...")
plt.style.use('seaborn-v0_8-darkgrid') # 使用更美观的样式
plt.figure(figsize=(20, 10))

# 绘制价格
plt.plot(test_df.index, test_df['Close'], label='SUI Price', color='dodgerblue', alpha=0.9, linewidth=1.5)

# 绘制真实的共振底（ground truth），用于对比
actual_bottoms = test_df[y_test == 1]
if not actual_bottoms.empty:
    plt.scatter(actual_bottoms.index, actual_bottoms['Low'] * 0.99, marker='^', color='limegreen', s=150,
                label=f'实际共振底 (共{len(actual_bottoms)}个)', zorder=5, edgecolors='black')

# 绘制模型预测出的信号
if not final_signals.empty:
    plt.scatter(final_signals.index, final_signals['Low'] * 0.98, marker='*', color='gold', s=300,
                label=f'模型预测信号 (共{len(final_signals)}个)', zorder=6, edgecolors='black', linewidth=1)

plt.title(f'【方案七】SUI价格 与 多周期共振底模型信号', fontsize=18)
plt.ylabel('Price', fontsize=14)
plt.legend(fontsize=12, loc='upper left')
plt.grid(True, linestyle='--', alpha=0.6)
plt.show()