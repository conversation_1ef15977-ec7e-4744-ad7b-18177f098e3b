# model_utils.py
# 公共函数库，用于数据加载、特征计算和标签生成

import pandas as pd
import numpy as np

def load_and_prepare_data(csv_file):
    """加载和预处理指定的CSV文件的数据"""
    print(f"加载和预处理数据: {csv_file}")
    try:
        df = pd.read_csv(csv_file)
        if 'Timestamp' not in df.columns:
            raise ValueError("CSV文件缺少 'Timestamp' 列")
        
        df['Timestamp'] = pd.to_datetime(df['Timestamp'], unit='s')
        df.set_index('Timestamp', inplace=True)
        df.rename(columns={'Open': 'open', 'High': 'high', 'Low': 'low', 'Close': 'close', 'Volume': 'volume'}, inplace=True)
        
        required_cols = ['open', 'high', 'low', 'close', 'volume']
        if not all(col in df.columns for col in required_cols):
            raise ValueError("CSV文件缺少必要的OHLCV列")
            
        df = df[required_cols]
        df.dropna(inplace=True)
        df.sort_index(inplace=True)
        print(f"数据预处理完成，共有 {len(df)} 条记录")
        return df
    except Exception as e:
        print(f"加载数据时出错: {e}")
        return None

def create_percentage_target(df, up_threshold, down_threshold, max_lookforward_minutes, timeframe):
    """为回测数据生成真实标签"""
    max_lookforward_candles = max_lookforward_minutes // timeframe
    print(f"为回测数据生成真实标签 (先涨{up_threshold*100:.1f}% vs 先跌{down_threshold*100:.1f}%)")
    
    labels = []
    valid_indices = []
    for i in range(len(df)):
        current_price = df.iloc[i]['close']
        up_target = current_price * (1 + up_threshold)
        down_target = current_price * (1 - down_threshold)
        label = None
        for j in range(1, min(max_lookforward_candles + 1, len(df) - i)):
            future_price = df.iloc[i + j]['close']
            if future_price >= up_target:
                label = 1
                break
            elif future_price <= down_target:
                label = 0
                break
        if label is not None:
            labels.append(label)
            valid_indices.append(i)
            
    label_series = pd.Series(index=df.index[valid_indices], data=labels, name='ActualResult')
    print(f"生成了 {len(label_series)} 个有效标签用于回测比对。")
    return label_series

def calculate_features(df, timeframe):
    """计算技术指标特征 (与训练时完全一致)"""
    print(f"开始计算特征 ({timeframe}-min K线)...")
    epsilon = 1e-9
    FEATURE_WINDOWS_MINUTES = [120, 360, 720]
    
    df['hour'], df['day_of_week'] = df.index.hour, df.index.dayofweek
    
    # K线基础特征计算
    df = get_standardized_kline_features(df, timeframe_suffix=f'_{timeframe}m', epsilon=epsilon)
    
    # 动量特征
    for n_minutes in FEATURE_WINDOWS_MINUTES:
        n_candles = n_minutes // timeframe
        if n_candles >= 1: df[f'return_{n_minutes}min'] = df['close'].pct_change(n_candles)
    df['return_60min'] = df['close'].pct_change(60 // timeframe)
    
    # 波动率和趋势特征
    for n_minutes in FEATURE_WINDOWS_MINUTES:
        n_candles = n_minutes // timeframe
        if n_candles >= 1:
            df[f'volatility_ratio_{n_minutes}'] = df['close'].rolling(window=n_candles).std() / (df['close'] + epsilon)
            df[f'sma_{n_minutes}'] = df['close'].rolling(window=n_candles).mean()
            df[f'price_div_sma_{n_minutes}'] = df['close'] / (df[f'sma_{n_minutes}'] + epsilon)
            
    if (120 // timeframe > 0 and 720 // timeframe > 0) and f'sma_120' in df.columns and f'sma_720' in df.columns:
         df['sma_120_div_sma_720'] = df['sma_120'] / (df['sma_720'] + epsilon)
         
    # VWAP特征
    vwap_candles = 360 // timeframe
    if vwap_candles > 0:
        df['price_x_volume'] = df['close'] * df['volume']
        vwap_numerator = df['price_x_volume'].rolling(window=vwap_candles).sum()
        vwap_denominator = df['volume'].rolling(window=vwap_candles).sum()
        df['vwap_360'] = vwap_numerator / (vwap_denominator + epsilon)
        df['price_div_vwap_360'] = df['close'] / (df['vwap_360'] + epsilon)
        df.drop('price_x_volume', axis=1, inplace=True)
        
    # VMA特征
    vma_candles = 360 // timeframe
    if vma_candles > 0:
        df['vma_360'] = df['volume'].rolling(window=vma_candles).mean()
        df['volume_div_vma_360'] = df['volume'] / (df['vma_360'] + epsilon)
        
    # 滚动K线特征 - 更健壮的版本
    for window in [5, 12, 24]:
        range_col = f'range_norm_by_atr_{timeframe}m'
        body_col = f'body_percent_of_range_{timeframe}m'
        
        if range_col in df.columns:
            df[f'range_norm_by_atr_mean_{window}'] = df[range_col].rolling(window=window).mean()
        
        if body_col in df.columns:
             df[f'body_percent_mean_{window}'] = df[body_col].rolling(window=window).mean()

    print("特征计算完成。")
    return df

def get_standardized_kline_features(df, timeframe_suffix='', epsilon=1e-9):
    """
    计算标准化的K线特征的辅助函数 (修正版)
    """
    try:
        # 1. 计算ATR
        high_low = df['high'] - df['low']
        high_prev_close = abs(df['high'] - df['close'].shift(1))
        low_prev_close = abs(df['low'] - df['close'].shift(1))
        tr = pd.concat([high_low, high_prev_close, low_prev_close], axis=1).max(axis=1)
        atr_col = f'atr_14{timeframe_suffix}'
        df[atr_col] = tr.rolling(window=14).mean()

        # 2. 计算K线形态
        body_size = abs(df['close'] - df['open'])
        price_range = df['high'] - df['low']
        
        # 3. 标准化 - 确保这两个关键特征都被创建
        df[f'body_percent_of_range{timeframe_suffix}'] = body_size / (price_range + epsilon)
        df[f'range_norm_by_atr{timeframe_suffix}'] = price_range / (df[atr_col] + epsilon)

    except Exception as e:
        print(f"K线特征计算出错: {e}")
        
    return df