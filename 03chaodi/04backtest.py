# backtest.py
# 使用已保存的模型，对新的历史数据CSV文件进行回测

import pandas as pd
import numpy as np
import joblib
import json
import argparse
import os
from model_utils import load_and_prepare_data, calculate_features, create_percentage_target

def run_backtest(model_file, config_file, input_csv, output_log_csv):
    """执行回测的核心函数"""
    print("--- 开始历史回测 ---")

    # 1. 加载模型和配置
    print(f"加载模型: {model_file}")
    if not os.path.exists(model_file):
        print(f"错误: 模型文件 '{model_file}' 未找到。"); return
    model = joblib.load(model_file)

    print(f"加载配置: {config_file}")
    if not os.path.exists(config_file):
        print(f"错误: 配置文件 '{config_file}' 未找到。"); return
    with open(config_file, 'r') as f:
        config = json.load(f)

    # 2. 加载并准备回测数据
    df = load_and_prepare_data(input_csv)
    if df is None: return

    # 3. 为回测数据生成真实标签 (这是回测的关键)
    labels = create_percentage_target(
        df,
        config['up_threshold'],
        config['down_threshold'],
        config['max_lookforward_minutes'],
        config['timeframe_minutes']
    )

    # 4. 准备特征
    df_labeled = df.join(labels, how='inner') # 只保留有真实标签的行
    df_with_features = calculate_features(df_labeled.copy(), timeframe=config['timeframe_minutes'])
    df_clean = df_with_features.dropna()
    
    if len(df_clean) == 0:
        print("错误: 数据处理后没有可用于回测的样本。"); return
        
    print(f"准备好 {len(df_clean)} 条样本进行回测。")

    # 5. 执行预测
    features = config['feature_list']
    
    # 确保所有需要的特征都存在于df_clean中
    missing_features = [f for f in features if f not in df_clean.columns]
    if missing_features:
        print(f"错误: 计算后仍然缺少以下特征: {missing_features}")
        print("请检查 model_utils.py 中的 calculate_features 函数。")
        return
        
    X_test = df_clean[features]
    y_test = df_clean['ActualResult']
    
    print("模型开始预测...")
    probabilities = model.predict_proba(X_test)[:, 1]
    
    # 6. 评估和记录
    best_threshold = config['best_threshold']
    print(f"使用阈值 {best_threshold:.3f} 进行评估...")
    
    trade_log = []
    for i in range(len(probabilities)):
        prob = probabilities[i]
        actual = y_test.iloc[i]
        timestamp = y_test.index[i]
        price = df_clean.loc[timestamp]['close']
        
        prediction, action = -1, "放弃"
        if prob > best_threshold:
            prediction, action = 1, "猜先涨"
        elif prob < (1 - best_threshold):
            prediction, action = 0, "猜先跌"
            
        score, outcome = 0, "-"
        if prediction != -1:
            if prediction == actual:
                score, outcome = 1, "成功✅"
            else:
                score, outcome = -1, "失败❌"
        
        trade_log.append({
            'Timestamp': timestamp,
            'ClosePrice': price,
            'ConfidenceUp': prob,
            'Action': action,
            'Prediction': prediction,
            'ActualResult': actual,
            'Outcome': outcome,
            'Score': score
        })

    # 7. 生成报告并保存
    if not trade_log:
        print("没有生成任何交易日志，无法进行评估。")
        return
        
    log_df = pd.DataFrame(trade_log)
    
    trades_made = (log_df['Prediction'] != -1).sum()
    wins = (log_df['Score'] == 1).sum()
    losses = (log_df['Score'] == -1).sum()
    total_score = wins - losses

    print("\n--- 回测结果摘要 ---")
    print(f"回测时间范围: {log_df['Timestamp'].min()} to {log_df['Timestamp'].max()}")
    print(f"总样本数: {len(log_df)}")
    print(f"交易次数: {trades_made} (交易频率: {trades_made/len(log_df)*100:.2f}%)")
    if trades_made > 0:
        print(f"胜率: {wins/trades_made*100:.2f}% ({wins}胜 / {losses}负)")
        print(f"最终得分: {total_score:+d}")
    else:
        print("没有发生任何交易。")

    log_df.to_csv(output_log_csv, index=False, float_format='%.4f')
    print(f"\n详细交易日志已保存到: {output_log_csv}")


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="使用已保存的模型对新的历史数据进行回测。")
    parser.add_argument("--model-file", required=True, help="已训练的模型文件路径 (.joblib)")
    parser.add_argument("--config-file", required=True, help="模型对应的配置文件路径 (.json)")
    parser.add_argument("--input-csv", required=True, help="用于回测的新的历史数据CSV文件路径")
    parser.add_argument("--output-log-csv", default="backtest_log.csv", help="保存详细回测日志的CSV文件路径")
    
    args = parser.parse_args()
    
    run_backtest(args.model_file, args.config_file, args.input_csv, args.output_log_csv)