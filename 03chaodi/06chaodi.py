# 导入所需库
import pandas as pd
import numpy as np
from datetime import datetime
import lightgbm as lgb
import joblib
import json
import argparse
import os
import pickle
import plotly.graph_objects as go

# --- SUI 5分钟数据配置 (基于未来收益率的回归模型) ---
TIMEFRAME_MINUTES = 5
# 我们不再需要预设的涨跌阈值，而是预测固定时间后的收益率
LOOKFORWARD_MINUTES = 24 * 60  # 预测未来1天的收益率

DATA_CSV_FILE = 'sui.csv'
# 模型基本名称，反映新的目标
MODEL_BASENAME = f'sui_{TIMEFRAME_MINUTES}min_predict_{LOOKFORWARD_MINUTES//60}h_return_回归'
# -------------------------------------------------------------

### 1. 全新的目标创建函数 ###
def 创建未来收益率目标(df, lookforward_minutes, timeframe):
    """
    创建一个简单的回归目标：未来特定时间点的价格百分比变化（收益率）。
    """
    lookforward_candles = lookforward_minutes // timeframe
    
    print(f"创建回归目标：未来 {lookforward_minutes} 分钟 ({lookforward_candles} 根K线) 的收益率")

    # 使用 .shift() 函数进行高效的向量化计算
    # target 是 (future_price / current_price) - 1
    df['label'] = df['close'].shift(-lookforward_candles) / df['close'] - 1
    
    # 由于shift操作，最后 `lookforward_candles` 行的label会是NaN，需要处理
    print(f"目标创建完成。将移除末尾 {lookforward_candles} 条无目标的数据。")
    df.dropna(subset=['label'], inplace=True)
    
    # 打印目标分布的统计信息
    print("目标(未来收益率)分布统计:")
    print(df['label'].describe())
    
    return df


# --- 特征计算和数据加载函数保持不变 ---
def get_standardized_kline_features(df, timeframe_suffix='', epsilon=1e-9):
    # (此函数代码不变)
    try:
        high_low = df['high'] - df['low']
        high_prev_close = abs(df['high'] - df['close'].shift(1))
        low_prev_close = abs(df['low'] - df['close'].shift(1))
        tr = pd.concat([high_low, high_prev_close, low_prev_close], axis=1).max(axis=1)
        atr_col = f'atr_14{timeframe_suffix}'
        df[atr_col] = tr.rolling(window=14).mean()
        body_size = abs(df['close'] - df['open'])
        price_range = df['high'] - df['low']
        df[f'body_percent_of_range{timeframe_suffix}'] = body_size / (price_range + epsilon)
        df[f'range_norm_by_atr{timeframe_suffix}'] = price_range / (df[atr_col] + epsilon)
    except Exception as e:
        print(f"K线特征计算出错: {e}")
    return df

def calculate_features(df, timeframe=TIMEFRAME_MINUTES):
    # (此函数代码不变)
    print(f"开始计算长周期特征 ({timeframe}-min K线)...")
    epsilon = 1e-9
    FEATURE_WINDOWS_MINUTES = [120, 360, 720] # 2小时, 6小时, 12小时
    df['hour'] = df.index.hour
    df['day_of_week'] = df.index.dayofweek
    df = get_standardized_kline_features(df, timeframe_suffix=f'_{timeframe}m', epsilon=epsilon)

    for n_minutes in FEATURE_WINDOWS_MINUTES:
        n_candles = n_minutes // timeframe
        if n_candles < 1: continue
        df[f'return_{n_minutes}min'] = df['close'].pct_change(n_candles)
    
    candles_60min = 60 // timeframe
    if candles_60min > 0:
        df['return_60min'] = df['close'].pct_change(candles_60min)

    for n_minutes in FEATURE_WINDOWS_MINUTES:
        n_candles = n_minutes // timeframe
        if n_candles < 1: continue
        df[f'volatility_{n_minutes}'] = df['close'].rolling(window=n_candles).std()
        df[f'volatility_ratio_{n_minutes}'] = df[f'volatility_{n_minutes}'] / (df['close'] + epsilon)

    for n_minutes in FEATURE_WINDOWS_MINUTES:
        n_candles = n_minutes // timeframe
        if n_candles < 1: continue
        df[f'sma_{n_minutes}'] = df['close'].rolling(window=n_candles).mean()
        df[f'price_div_sma_{n_minutes}'] = df['close'] / (df[f'sma_{n_minutes}'] + epsilon)

    sma_120_candles = 120 // timeframe
    sma_720_candles = 720 // timeframe
    if sma_120_candles > 0 and sma_720_candles > 0:
        df[f'sma_{120}'] = df['close'].rolling(window=sma_120_candles).mean()
        df[f'sma_{720}'] = df['close'].rolling(window=sma_720_candles).mean()
        df['sma_120_div_sma_720'] = df[f'sma_{120}'] / (df[f'sma_{720}'] + epsilon)

    vwap_candles = 360 // timeframe
    if vwap_candles > 0:
        df['price_x_volume'] = df['close'] * df['volume']
        vwap_numerator = df['price_x_volume'].rolling(window=vwap_candles).sum()
        vwap_denominator = df['volume'].rolling(window=vwap_candles).sum()
        df['vwap_360'] = vwap_numerator / (vwap_denominator + epsilon)
        df['price_div_vwap_360'] = df['close'] / (df['vwap_360'] + epsilon)
        df.drop('price_x_volume', axis=1, inplace=True)

    vma_candles = 360 // timeframe
    if vma_candles > 0:
        df['vma_360'] = df['volume'].rolling(window=vma_candles).mean()
        df['volume_div_vma_360'] = df['volume'] / (df['vma_360'] + epsilon)

    for window in [5, 12, 24]:
        if f'range_norm_by_atr_{timeframe}m' in df.columns:
            df[f'range_norm_by_atr_mean_{window}'] = df[f'range_norm_by_atr_{timeframe}m'].rolling(window=window).mean()
        if f'body_percent_of_range_{timeframe}m' in df.columns:
             df[f'body_percent_mean_{window}'] = df[f'body_percent_of_range_{timeframe}m'].rolling(window=window).mean()

    print("长周期特征计算完成")
    return df

def load_and_prepare_data():
    # (此函数代码不变)
    print(f"加载并预处理数据: {DATA_CSV_FILE}")
    if not os.path.exists(DATA_CSV_FILE):
        print(f"错误: 数据文件 '{DATA_CSV_FILE}' 不存在。请确保文件路径正确。")
        exit()
    df = pd.read_csv(DATA_CSV_FILE)
    df['Timestamp'] = pd.to_datetime(df['Timestamp'], unit='s')
    df.set_index('Timestamp', inplace=True)
    df.rename(columns={'Open': 'open', 'High': 'high', 'Low': 'low', 'Close': 'close', 'Volume': 'volume'}, inplace=True)
    df = df[['open', 'high', 'low', 'close', 'volume']]
    df.dropna(inplace=True)
    df.sort_index(inplace=True)
    print(f"数据预处理完成，共 {len(df)} 条记录。")
    return df

def prepare_features_and_labels(df):
    # 使用新的目标创建函数
    df_with_target = 创建未来收益率目标(df, LOOKFORWARD_MINUTES, TIMEFRAME_MINUTES)
    
    df_with_features = calculate_features(df_with_target, timeframe=TIMEFRAME_MINUTES)
    df_clean = df_with_features.dropna() # 移除因计算特征产生的NaN
    print(f"清理NaN后，剩余 {len(df_clean)} 条记录用于训练。")
    return df_clean

# --- 数据分割和特征列表函数保持不变 ---
def split_data(df_clean):
    # (此函数代码不变)
    train_size = int(len(df_clean) * 0.70)
    val_size = int(len(df_clean) * 0.15)
    train_df = df_clean.iloc[:train_size]
    val_df = df_clean.iloc[train_size:train_size + val_size]
    test_df = df_clean.iloc[train_size + val_size:]
    print(f"训练集: {len(train_df)} ({train_df.index.min()} to {train_df.index.max()})")
    print(f"验证集: {len(val_df)} ({val_df.index.min()} to {val_df.index.max()})")
    print(f"测试集: {len(test_df)} ({test_df.index.min()} to {test_df.index.max()})")
    return train_df, val_df, test_df

def get_feature_list(df_clean):
    # (此函数代码不变)
    long_term_features = [
        'hour', 'day_of_week', f'atr_14_{TIMEFRAME_MINUTES}m', f'range_norm_by_atr_{TIMEFRAME_MINUTES}m',
        'return_60min', 'return_120min', 'return_360min', 'return_720min',
        'volatility_ratio_120', 'volatility_ratio_360', 'volatility_ratio_720',
        'price_div_sma_120', 'price_div_sma_360', 'price_div_sma_720', 'sma_120_div_sma_720',
        'price_div_vwap_360', 'volume_div_vma_360', 'range_norm_by_atr_mean_12',
        'range_norm_by_atr_mean_24', 'body_percent_mean_12', 'body_percent_mean_24'
    ]
    available_features = [col for col in long_term_features if col in df_clean.columns]
    missing = [col for col in long_term_features if col not in df_clean.columns]
    if missing: print(f"警告：以下特征在数据中不存在: {missing}")
    print(f"使用的特征数量: {len(available_features)}")
    return available_features

# --- 数据保存和加载函数保持不变 ---
def save_processed_data(df_clean, train_df, val_df, test_df, features, data_file):
    # (此函数代码不变)
    print(f"保存预处理数据到 {data_file}...")
    data_dict = {'df_clean': df_clean, 'train_df': train_df, 'val_df': val_df, 'test_df': test_df, 'features': features, 'save_time': datetime.now().isoformat()}
    with open(data_file, 'wb') as f: pickle.dump(data_dict, f)
    print(f"数据已保存到 {data_file}")

def load_processed_data(data_file):
    # (此函数代码不变)
    print(f"从 {data_file} 加载预处理数据...")
    if not os.path.exists(data_file):
        print(f"错误：数据文件 {data_file} 不存在！"); return None, None, None, None, None
    with open(data_file, 'rb') as f: data_dict = pickle.load(f)
    print(f"数据加载完成，保存时间: {data_dict.get('save_time', '未知')}")
    return data_dict['df_clean'], data_dict['train_df'], data_dict['val_df'], data_dict['test_df'], data_dict['features']

### 2. 重写的模型训练函数 ###
def train_model(save_data=False, load_data=False, data_file=f'{MODEL_BASENAME}.pkl'):
    print(f"开始训练 LightGBM 模型 (预测未来收益率) - {MODEL_BASENAME.replace('_', ' ').title()}")
    # 数据加载部分不变
    if load_data:
        df_clean, train_df, val_df, test_df, features = load_processed_data(data_file)
        if df_clean is None: load_data = False
    if not load_data:
        df = load_and_prepare_data()
        df_clean = prepare_features_and_labels(df)
        train_df, val_df, test_df = split_data(df_clean)
        features = get_feature_list(df_clean)
        if save_data:
            save_processed_data(df_clean, train_df, val_df, test_df, features, data_file)
    
    target = 'label'
    X_train, y_train = train_df[features], train_df[target]
    X_val, y_val = val_df[features], val_df[target]
    X_test, y_test = test_df[features], test_df[target]

    print("开始训练 LightGBM Regressor 模型...")
    lgbm = lgb.LGBMRegressor(
        objective='regression_l1', # MAE, 对异常值不敏感
        metric='rmse',
        n_estimators=2000,
        learning_rate=0.05,
        n_jobs=-1,
        random_state=42,
        verbose=-1
    )
    lgbm.fit(X_train, y_train,
             eval_set=[(X_val, y_val)],
             eval_metric='rmse',
             callbacks=[lgb.early_stopping(stopping_rounds=100, verbose=False)])

    print("模型训练完成。开始优化决策阈值...")
    
    val_predictions = lgbm.predict(X_val)
    
    # 优化目标：最大化夏普比率或总收益
    # 这里我们用一个简化的目标：最大化总收益
    # 假设每次交易的名义价值为1，无交易成本
    best_score = -np.inf
    best_up_threshold = 0
    best_down_threshold = 0
    
    # 搜索上涨和下跌的开仓阈值
    # 例如，当预测收益 > 1% 时做多，预测收益 < -1% 时做空
    for up_thresh in np.arange(0.005, 0.05, 0.005): # 0.5% to 5%
        for down_thresh in np.arange(-0.05, -0.005, 0.005): # -5% to -0.5%
            # 计算在该阈值下的总收益
            # 做多收益
            long_returns = y_val[(val_predictions > up_thresh)].sum()
            # 做空收益 (做空所以是负的y_val)
            short_returns = -y_val[(val_predictions < down_thresh)].sum()
            
            total_return = long_returns + short_returns
            
            if total_return > best_score:
                best_score = total_return
                best_up_threshold = up_thresh
                best_down_threshold = down_thresh

    print(f"验证集上找到的最优策略：当预测 > {best_up_threshold*100:.2f}% 做多，< {best_down_threshold*100:.2f}% 做空")
    print(f"该策略在验证集上的总收益(无成本): {best_score:+.4f}")
    
    # 使用找到的最佳阈值在测试集上进行最终评估
    evaluate_on_test_set(lgbm, X_test, y_test, test_df, best_up_threshold, best_down_threshold)
    
    # 保存模型和配置
    config_thresholds = {'up': best_up_threshold, 'down': best_down_threshold}
    save_model_and_config(lgbm, features, config_thresholds, len(X_train), len(X_val), len(X_test))
    
    # 分析特征重要性
    analyze_feature_importance(lgbm, features)

### 3. 重写的评估和绘图函数 ###
def evaluate_on_test_set(model, X_test, y_test, test_df, up_threshold, down_threshold):
    """
    在测试集上评估模型，并计算策略收益。
    """
    print(f"\n--- 测试集评估 (做多阈值: >{up_threshold*100:.2f}%, 做空阈值: <{down_threshold*100:.2f}%) ---")
    
    predictions = model.predict(X_test)
    
    # 根据阈值决定操作
    decisions = np.zeros(len(predictions)) # 0: 不操作
    decisions[predictions > up_threshold] = 1 # 1: 做多
    decisions[predictions < down_threshold] = -1 # -1: 做空
    
    # 计算每次操作的收益率 (无成本)
    # 做多时，收益是 y_test
    # 做空时，收益是 -y_test
    returns = decisions * y_test
    
    results_log = []
    for i in range(len(predictions)):
        log_entry = {
            'Timestamp': test_df.index[i],
            'PredictedReturn': predictions[i],
            'ActualReturn': y_test.iloc[i],
            'Decision': decisions[i],
            'StrategyReturn': returns.iloc[i]
        }
        results_log.append(log_entry)
    
    results_df = pd.DataFrame(results_log)
    trades_df = results_df[results_df['Decision'] != 0]

    print(f"总样本数: {len(test_df)}")
    print(f"总交易次数: {len(trades_df)}")
    if len(trades_df) > 0:
        long_trades = trades_df[trades_df['Decision'] == 1]
        short_trades = trades_df[trades_df['Decision'] == -1]
        print(f"  - 做多次数: {len(long_trades)}, 平均收益: {long_trades['StrategyReturn'].mean():.4%}")
        print(f"  - 做空次数: {len(short_trades)}, 平均收益: {short_trades['StrategyReturn'].mean():.4%}")

        # 计算累积收益曲线
        results_df['CumulativeReturn'] = results_df['StrategyReturn'].cumsum()
        total_return = results_df['CumulativeReturn'].iloc[-1]
        
        print(f"\n测试集总收益 (无成本): {total_return:.4%}")
        
        # 绘制累积收益曲线
        fig = go.Figure()
        fig.add_trace(go.Scatter(x=results_df['Timestamp'], y=results_df['CumulativeReturn'], mode='lines', name='策略累积收益'))
        fig.update_layout(title=f'策略累积收益曲线 - {MODEL_BASENAME}', xaxis_title='日期', yaxis_title='累积收益率', template='plotly_dark')
        returns_curve_filename = f'returns_curve_{MODEL_BASENAME}.html'
        fig.write_html(returns_curve_filename)
        print(f"累积收益曲线图已保存至: {returns_curve_filename}")
        
        # 绘制K线和交易点
        plot_trades_on_kline(test_df, trades_df, filename=f'kline_trades_{MODEL_BASENAME}.html')
        
def plot_trades_on_kline(test_df, trades_df, filename):
    print(f"正在生成带交易标记的K线图...")
    fig = go.Figure(data=[go.Candlestick(x=test_df.index, open=test_df['open'], high=test_df['high'], low=test_df['low'], close=test_df['close'], name='K线')])

    long_trades = trades_df[trades_df['Decision'] == 1]
    short_trades = trades_df[trades_df['Decision'] == -1]
    
    # 用颜色表示交易盈亏
    long_trades['color'] = np.where(long_trades['StrategyReturn'] > 0, 'green', 'red')
    short_trades['color'] = np.where(short_trades['StrategyReturn'] > 0, 'green', 'red')
    
    fig.add_trace(go.Scatter(
        x=long_trades['Timestamp'], y=test_df.loc[long_trades['Timestamp']]['low'] * 0.99, mode='markers',
        marker=dict(symbol='triangle-up', color=long_trades['color'], size=10), name='做多',
        text=[f"预测: {p:+.2%}<br>实际: {a:+.2%}" for p, a in zip(long_trades['PredictedReturn'], long_trades['ActualReturn'])],
        hoverinfo='text+x'
    ))
    fig.add_trace(go.Scatter(
        x=short_trades['Timestamp'], y=test_df.loc[short_trades['Timestamp']]['high'] * 1.01, mode='markers',
        marker=dict(symbol='triangle-down', color=short_trades['color'], size=10), name='做空',
        text=[f"预测: {p:+.2%}<br>实际: {a:+.2%}" for p, a in zip(short_trades['PredictedReturn'], short_trades['ActualReturn'])],
        hoverinfo='text+x'
    ))
    
    fig.update_layout(title=f'K线图与交易点 - {MODEL_BASENAME}', template='plotly_dark')
    fig.write_html(filename)
    print(f"K线交易图已保存至: {filename}")


# --- 保存/特征重要性/主函数需要微调 ---
def save_model_and_config(model, features, thresholds, train_size, val_size, test_size):
    print("\n正在保存模型和配置...")
    model_file = f'{MODEL_BASENAME}_model.joblib'
    config_file = f'{MODEL_BASENAME}_config.json'
    joblib.dump(model, model_file)
    
    config = {
        'thresholds': thresholds, 
        'feature_list': features,
        'model_type': f'LGBM_未来收益率回归_{MODEL_BASENAME}',
        'target_description': f'预测未来 {LOOKFORWARD_MINUTES} 分钟的收益率。',
        'training_date': datetime.now().isoformat(),
        'train_size': train_size, 'val_size': val_size, 'test_size': test_size,
        'lookforward_minutes': LOOKFORWARD_MINUTES, 
        'timeframe_minutes': TIMEFRAME_MINUTES
    }
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    print(f"模型已保存: {model_file}\n配置已保存: {config_file}")

def analyze_feature_importance(lgbm, features):
    # (此函数代码不变)
    print("\n" + "="*60 + "\n特征重要性分析\n" + "="*60)
    importance_df = pd.DataFrame({'feature': features, 'importance': lgbm.feature_importances_}).sort_values('importance', ascending=False)
    print(importance_df.head(20).to_string(index=False))
    importance_file = f'feature_importance_{MODEL_BASENAME}.csv'
    importance_df.to_csv(importance_file, index=False, encoding='utf-8-sig')
    print(f"\n特征重要性已保存到 {importance_file}")

def validate_model(model_file, config_file, load_data=False, data_file=f'{MODEL_BASENAME}.pkl'):
    # (此函数代码需要适配)
    print(f"加载模型: {model_file}\n加载配置: {config_file}")
    if not os.path.exists(model_file) or not os.path.exists(config_file):
        print(f"错误：模型或配置文件不存在！"); return
    model = joblib.load(model_file)
    with open(config_file, 'r', encoding='utf-8') as f: config = json.load(f)
    print(f"模型类型: {config.get('model_type', '未知')}, 训练日期: {config.get('training_date', '未知')}")
    
    if load_data:
        df_clean, _, _, test_df, _ = load_processed_data(data_file)
        if df_clean is None: load_data = False
    
    if not load_data:
        df = load_and_prepare_data()
        df_clean = prepare_features_and_labels(df)
        _, _, test_df = split_data(df_clean)
        
    features = config.get('feature_list', [])
    thresholds = config.get('thresholds', {'up': 0.01, 'down': -0.01}) # 提供一个默认值
    
    X_test, y_test = test_df[features], test_df['label']
    print(f"测试集大小: {len(X_test)}")
    
    evaluate_on_test_set(model, X_test, y_test, test_df, thresholds['up'], thresholds['down'])
    print("\n验证完成！")

def main():
    # (此函数代码不变)
    parser = argparse.ArgumentParser(description=f"LGBM 收益率预测回归模型 - {MODEL_BASENAME.replace('_', ' ').title()}")
    parser.add_argument("--mode", choices=['train', 'validate'], default='train', help="运行模式: 'train' (训练新模型) 或 'validate' (验证现有模型)")
    parser.add_argument("--model-file", default=f'{MODEL_BASENAME}_model.joblib', help="用于验证的模型文件路径")
    parser.add_argument("--config-file", default=f'{MODEL_BASENAME}_config.json', help="用于验证的配置文件路径")
    parser.add_argument("--save-data", action='store_true', help="保存预处理后的数据，以加速后续运行")
    parser.add_argument("--load-data", action='store_true', help="如果存在，则加载预处理的数据，以加速运行")
    parser.add_argument("--data-file", default=f'{MODEL_BASENAME}.pkl', help="预处理数据的存取路径")
    args = parser.parse_args()
    if args.mode == 'train':
        train_model(args.save_data, args.load_data, args.data_file)
    else:
        validate_model(args.model_file, args.config_file, args.load_data, args.data_file)

if __name__ == '__main__':
    main()