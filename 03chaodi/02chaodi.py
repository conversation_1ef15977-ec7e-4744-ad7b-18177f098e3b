# -*- coding: utf-8 -*-

# --- 第0步：导入所需库 ---
from sklearn.metrics import classification_report
import pandas as pd
import numpy as np
import pandas_ta as ta
from tqdm import tqdm
import xgboost as xgb

# =========================================================
# 【修正1】: 启用交互式绘图后端，并解决中文乱码问题
# =========================================================
import matplotlib
# 重要: 使用交互式后端以实现缩放/平移功能。
# 这通常需要安装一个GUI库，如 PyQt5。如果没有，请先运行: pip install PyQt5
try:
    matplotlib.use('Qt5Agg')
except ImportError:
    print("警告: Qt5Agg后端未找到，绘图可能无法交互。请尝试安装 'pip install PyQt5'")
import matplotlib.pyplot as plt

# --- 解决中文乱码和负号显示问题 ---
# 根据你的操作系统选择一个支持中文的字体
# Windows: SimHei, Microsoft YaHei
# macOS: PingFang SC, Heiti SC
# Linux: WenQuanYi Micro Hei, Noto Sans CJK SC
plt.rcParams['font.sans-serif'] = ['SimHei']  # 指定默认字体为黑体
plt.rcParams['axes.unicode_minus'] = False  # 解决负号'-'显示为方块的问题
# =========================================================

# TQDM and pandas integration for progress bars
tqdm.pandas()

print("======================================================")
print("方案融合：基于'先涨1%vs先跌1%'目标的抄底模型")
print("======================================================")

# --- 第1步：数据加载与准备 ---
print(">>> 1. 正在加载数据...")
df = pd.read_csv('sui.csv')
df['Timestamp'] = pd.to_datetime(df['Timestamp'], unit='s')
df.set_index('Timestamp', inplace=True)
df.sort_index(inplace=True)
df = df[~df.index.duplicated(keep='first')]
df.rename(columns={'Open': 'open', 'High': 'high', 'Low': 'low', 'Close': 'close', 'Volume': 'volume'}, inplace=True)

# --- 第2步：【核心改进】定义'先涨1% vs 先跌1%'目标变量 ---
print(">>> 2. 正在定义'先涨2% vs 先跌2%'目标变量...")

def define_forward_return_target(df, up_threshold=0.02, down_threshold=0.02, max_lookforward_periods=48):
    print(f"    - 定义目标：先涨 {up_threshold*100:.1f}% 还是先跌 {down_threshold*100:.1f}%")
    print(f"    - 最大前瞻K线数: {max_lookforward_periods} ({max_lookforward_periods * 5} 分钟)")
    labels = pd.Series(np.nan, index=df.index)
    close_prices = df['close'].values
    indices = np.arange(len(df))
    for i in tqdm(indices, desc="    - 计算目标标签"):
        current_price = close_prices[i]
        up_target = current_price * (1 + up_threshold)
        down_target = current_price * (1 - down_threshold)
        lookforward_window = close_prices[i + 1 : i + 1 + max_lookforward_periods]
        if len(lookforward_window) == 0:
            continue
        up_hits = np.where(lookforward_window >= up_target)[0]
        down_hits = np.where(lookforward_window <= down_target)[0]
        first_up_hit = up_hits[0] if len(up_hits) > 0 else np.inf
        first_down_hit = down_hits[0] if len(down_hits) > 0 else np.inf
        if first_up_hit < first_down_hit:
            labels.iloc[i] = 1
        elif first_down_hit < first_up_hit:
            labels.iloc[i] = 0
    return labels

df['target'] = define_forward_return_target(df, up_threshold=0.02, down_threshold=0.02, max_lookforward_periods=48)
df.dropna(subset=['target'], inplace=True)
df['target'] = df['target'].astype(int)
print(f"\n找到了 {df['target'].sum()} 个'先涨'样本 和 {(len(df) - df['target'].sum())} 个'先跌'样本。")

# --- 第3步：【核心改进】构建强大的特征库 ---
print(">>> 3. 正在构建高级特征库...")

def calculate_advanced_features(df):
    epsilon = 1e-9
    df['hour'] = df.index.hour
    df['day_of_week'] = df.index.dayofweek
    for n_min in [15, 30, 60, 120]:
        periods = n_min // 5
        df[f'return_{n_min}min'] = df['close'].pct_change(periods)
    for n_min in [30, 60, 120]:
        periods = n_min // 5
        df[f'volatility_{n_min}min'] = df['close'].rolling(window=periods).std()
        df[f'volatility_ratio_{n_min}min'] = df[f'volatility_{n_min}min'] / (df['close'] + epsilon)
    for n_min in [30, 60, 120]:
        periods = n_min // 5
        df[f'sma_{n_min}min'] = df['close'].rolling(window=periods).mean()
        df[f'price_div_sma_{n_min}min'] = df['close'] / (df[f'sma_{n_min}min'] + epsilon)
    periods_30min = 30 // 5
    df['price_x_volume'] = df['close'] * df['volume']
    vwap_numerator = df['price_x_volume'].rolling(window=periods_30min).sum()
    vwap_denominator = df['volume'].rolling(window=periods_30min).sum()
    df['vwap_30min'] = vwap_numerator / (vwap_denominator + epsilon)
    df['price_div_vwap_30min'] = df['close'] / (df['vwap_30min'] + epsilon)
    df.drop('price_x_volume', axis=1, inplace=True)
    df.ta.rsi(length=14, append=True)
    df.ta.adx(length=14, append=True)
    df.ta.bbands(length=20, append=True)
    print("    - 特征计算完成。")
    return df

df = calculate_advanced_features(df)
df.dropna(inplace=True)

# --- 第4步：准备训练和测试数据 ---
print(">>> 4. 正在划分数据集...")
features = [col for col in df.columns if col not in ['open', 'high', 'low', 'close', 'volume', 'target']]
print(f"    - 使用的特征数量: {len(features)}")
X = df[features]
y = df['target']
X.replace([np.inf, -np.inf], np.nan, inplace=True)
X.dropna(inplace=True)
y = y[X.index]
split_index = int(len(df) * 0.8)
X_train, X_test = X.iloc[:split_index], X.iloc[split_index:]
y_train, y_test = y.iloc[:split_index], y.iloc[split_index:]

# --- 第5步：训练模型 ---
print(">>> 5. 正在训练XGBoost模型...")
pos_count = y_train.sum()
neg_count = len(y_train) - pos_count
scale_pos_weight = neg_count / pos_count if pos_count > 0 else 1
print(f"    - 正负样本比例不平衡，设定 scale_pos_weight = {scale_pos_weight:.2f}")
model = xgb.XGBClassifier(
    objective='binary:logistic', eval_metric='logloss', n_estimators=1000,
    learning_rate=0.01, max_depth=5, early_stopping_rounds=50,
    use_label_encoder=False, scale_pos_weight=scale_pos_weight
)
model.fit(X_train, y_train, eval_set=[(X_test, y_test)], verbose=False)

# --- 第6步：在测试集上进行预测 ---
print(">>> 6. 正在测试集上进行预测...")
y_pred_proba = model.predict_proba(X_test)[:, 1]
test_df = df.iloc[split_index:].loc[X_test.index].copy()
test_df['pred_prob'] = y_pred_proba

# --- 第7步：评估和信号筛选 ---
print(">>> 7. 正在进行信号筛选和评估...")
CONFIDENCE_THRESHOLD = 0.69
print(f"\n使用固定高置信度阈值筛选信号：只考虑上涨概率 > {CONFIDENCE_THRESHOLD*100:.0f}% 的情况")
buy_signals = test_df[test_df['pred_prob'] > CONFIDENCE_THRESHOLD]
print(f"    - 最终筛选出 {len(buy_signals)} 个高置信度'买入'信号")
y_pred_class = (y_pred_proba > CONFIDENCE_THRESHOLD).astype(int)
print("\n--- 模型在【高置信度阈值】下的分类报告 (只看预测为1的精度) ---")
print(classification_report(y_test, y_pred_class, target_names=['先跌2%', '先涨2%'], zero_division=0))

# --- 第8步：结果可视化 ---
print(">>> 8. 正在生成结果图表...")
plt.style.use('seaborn-v0_8-darkgrid')
fig, ax = plt.subplots(figsize=(20, 12)) # 使用 fig, ax 以便更好地控制

# 绘制测试集价格
ax.plot(test_df.index, test_df['close'], label='SUI 价格 (测试集)', color='lightgray', linewidth=1, zorder=1)

# 绘制模型预测出的高置信度“买入”信号
if not buy_signals.empty:
    ax.scatter(buy_signals.index, buy_signals['low'] * 0.998, marker='^', s=150,
               label=f'模型预测买点 (>{CONFIDENCE_THRESHOLD*100:.0f}%概率, 共{len(buy_signals)}个)', 
               zorder=3, color='gold', edgecolors='black') # 初始颜色设为金色

# 为了验证，我们把这些信号的真实结果也标出来
correct_buys = buy_signals[buy_signals['target'] == 1]
incorrect_buys = buy_signals[buy_signals['target'] == 0]

if not correct_buys.empty:
    ax.scatter(correct_buys.index, correct_buys['low'] * 0.998, marker='^', s=150,
               label=f'预测正确 (真的先涨了, 共{len(correct_buys)}个)', zorder=4, color='limegreen', edgecolors='black')

if not incorrect_buys.empty:
    ax.scatter(incorrect_buys.index, incorrect_buys['low'] * 0.998, marker='^', s=150,
               label=f'预测错误 (结果先跌了, 共{len(incorrect_buys)}个)', zorder=4, color='red', edgecolors='black')

ax.set_title(f'【融合方案】SUI 抄底模型预测 vs 实际结果 (目标: 先涨2% vs 先跌2%)', fontsize=18)
ax.set_ylabel('价格', fontsize=14)
ax.set_xlabel('日期', fontsize=14)
ax.legend(fontsize=12, loc='upper left')
ax.grid(True, linestyle='--', alpha=0.6)

# 自动格式化日期显示
fig.autofmt_xdate()
plt.tight_layout() # 调整布局防止标签重叠

plt.show()