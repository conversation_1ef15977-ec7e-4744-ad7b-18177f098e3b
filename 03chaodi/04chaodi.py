import pandas as pd
import numpy as np
from datetime import datetime
import lightgbm as lgb
from sklearn.calibration import CalibratedClassifierCV
import joblib
import json
import argparse
import os
import pickle
from model_utils import calculate_features
# --- Configuration for SUI 5-minute data with new target ---
TIMEFRAME_MINUTES = 5
UP_THRESHOLD = 0.05  # Target: 5% up
DOWN_THRESHOLD = 0.05 # Target: 5% down
MAX_LOOKFORWARD_MINUTES = 24 * 60  # Lookforward period: 1 day (1440 minutes)

DATA_CSV_FILE = 'sui.csv'
MODEL_BASENAME = f'pepe_{TIMEFRAME_MINUTES}min_{int(UP_THRESHOLD*100)}pct_{MAX_LOOKFORWARD_MINUTES//1440}day'
# -------------------------------------------------------------

def create_percentage_target(df, up_threshold, down_threshold, max_lookforward_minutes, timeframe):
    """
    创建新的目标标签：判断在指定时间内是先涨 X% 还是先跌 Y%
    """
    max_lookforward_candles = max_lookforward_minutes // timeframe

    print(f"创建目标标签：先涨{up_threshold*100:.1f}%还是先跌{down_threshold*100:.1f}%")
    print(f"最大前瞻时间：{max_lookforward_minutes}分钟 ({max_lookforward_candles}根 {timeframe}-min K线)")

    labels = []
    valid_indices = []

    for i in range(len(df)):
        if i % 10000 == 0:
            print(f"处理进度: {i}/{len(df)} ({i/len(df)*100:.1f}%)")

        current_price = df.iloc[i]['close']
        up_target = current_price * (1 + up_threshold)
        down_target = current_price * (1 - down_threshold)

        label = None
        for j in range(1, min(max_lookforward_candles + 1, len(df) - i)):
            future_price = df.iloc[i + j]['close']
            if future_price >= up_target:
                label = 1
                break
            elif future_price <= down_target:
                label = 0
                break

        if label is not None:
            labels.append(label)
            valid_indices.append(i)

    print(f"有效标签数量: {len(labels)}/{len(df)} ({len(labels)/len(df)*100:.1f}%)")

    label_series = pd.Series(index=df.index[valid_indices], data=labels)
    if len(labels) > 0:
        up_count = sum(labels)
        down_count = len(labels) - up_count
        print(f"标签分布: 先涨 = {up_count} ({up_count/len(labels)*100:.1f}%), 先跌 = {down_count} ({down_count/len(labels)*100:.1f}%)")
    else:
        print("未生成任何有效标签。")

    return label_series

def calculate_features1(df, timeframe=TIMEFRAME_MINUTES):
    """
    计算技术指标特征，调整为更长的时间范围以匹配1天预测目标
    """
    print(f"开始计算长周期特征 ({timeframe}-min K线)...")
    epsilon = 1e-9
    FEATURE_WINDOWS_MINUTES = [120, 360, 720] # 2-hour, 6-hour, 12-hour
    df['hour'] = df.index.hour
    df['day_of_week'] = df.index.dayofweek
    df = get_standardized_kline_features(df, timeframe_suffix=f'_{timeframe}m', epsilon=epsilon)

    for n_minutes in FEATURE_WINDOWS_MINUTES:
        n_candles = n_minutes // timeframe
        if n_candles < 1: continue
        df[f'return_{n_minutes}min'] = df['close'].pct_change(n_candles)
    df['return_60min'] = df['close'].pct_change(60 // timeframe)

    for n_minutes in FEATURE_WINDOWS_MINUTES:
        n_candles = n_minutes // timeframe
        if n_candles < 1: continue
        df[f'volatility_{n_minutes}'] = df['close'].rolling(window=n_candles).std()
        df[f'volatility_ratio_{n_minutes}'] = df[f'volatility_{n_minutes}'] / (df['close'] + epsilon)

    for n_minutes in FEATURE_WINDOWS_MINUTES:
        n_candles = n_minutes // timeframe
        if n_candles < 1: continue
        df[f'sma_{n_minutes}'] = df['close'].rolling(window=n_candles).mean()
        df[f'price_div_sma_{n_minutes}'] = df['close'] / (df[f'sma_{n_minutes}'] + epsilon)

    sma_120_candles = 120 // timeframe
    sma_720_candles = 720 // timeframe
    if sma_120_candles > 0 and sma_720_candles > 0:
        df['sma_120_div_sma_720'] = df[f'sma_{120}'] / (df[f'sma_{720}'] + epsilon)

    vwap_candles = 360 // timeframe
    if vwap_candles > 0:
        df['price_x_volume'] = df['close'] * df['volume']
        vwap_numerator = df['price_x_volume'].rolling(window=vwap_candles).sum()
        vwap_denominator = df['volume'].rolling(window=vwap_candles).sum()
        df['vwap_360'] = vwap_numerator / (vwap_denominator + epsilon)
        df['price_div_vwap_360'] = df['close'] / (df['vwap_360'] + epsilon)
        df.drop('price_x_volume', axis=1, inplace=True)

    vma_candles = 360 // timeframe
    if vma_candles > 0:
        df['vma_360'] = df['volume'].rolling(window=vma_candles).mean()
        df['volume_div_vma_360'] = df['volume'] / (df['vma_360'] + epsilon)

    for window in [5, 12, 24]: # approx 25min, 1hr, 2hr
        if f'range_norm_by_atr_{timeframe}m' in df.columns:
            df[f'range_norm_by_atr_mean_{window}'] = df[f'range_norm_by_atr_{timeframe}m'].rolling(window=window).mean()
        if f'body_percent_of_range_{timeframe}m' in df.columns:
             df[f'body_percent_mean_{window}'] = df[f'body_percent_of_range_{timeframe}m'].rolling(window=window).mean()

    print("长周期特征计算完成")
    return df

def get_standardized_kline_features(df, timeframe_suffix='', epsilon=1e-9):
    try:
        high_low = df['high'] - df['low']
        high_prev_close = abs(df['high'] - df['close'].shift(1))
        low_prev_close = abs(df['low'] - df['close'].shift(1))
        tr = pd.concat([high_low, high_prev_close, low_prev_close], axis=1).max(axis=1)
        atr_col = f'atr_14{timeframe_suffix}'
        df[atr_col] = tr.rolling(window=14).mean()
        body_size = abs(df['close'] - df['open'])
        price_range = df['high'] - df['low']
        df[f'body_percent_of_range{timeframe_suffix}'] = body_size / (price_range + epsilon)
        df[f'range_norm_by_atr{timeframe_suffix}'] = price_range / (df[atr_col] + epsilon)
    except Exception as e:
        print(f"K线特征计算出错: {e}")
    return df

def load_and_prepare_data():
    print(f"加载和预处理数据: {DATA_CSV_FILE}")
    df = pd.read_csv(DATA_CSV_FILE)
    df['Timestamp'] = pd.to_datetime(df['Timestamp'], unit='s')
    df.set_index('Timestamp', inplace=True)
    df.rename(columns={'Open': 'open', 'High': 'high', 'Low': 'low', 'Close': 'close', 'Volume': 'volume'}, inplace=True)
    df = df[['open', 'high', 'low', 'close', 'volume']]
    df.dropna(inplace=True)
    df.sort_index(inplace=True)
    print(f"数据预处理完成，共有 {len(df)} 条记录")
    return df

def prepare_features_and_labels(df):
    target_labels = create_percentage_target(df, UP_THRESHOLD, DOWN_THRESHOLD, MAX_LOOKFORWARD_MINUTES, TIMEFRAME_MINUTES)
    df_labeled = df.loc[target_labels.index].copy()
    df_labeled['label'] = target_labels
    df_with_features = calculate_features(df_labeled, timeframe=TIMEFRAME_MINUTES)
    df_clean = df_with_features.dropna()
    print(f"清理NaN后剩余 {len(df_clean)} 条记录")
    return df_clean

def split_data(df_clean):
    train_size = int(len(df_clean) * 0.70)
    val_size = int(len(df_clean) * 0.15)
    train_df = df_clean.iloc[:train_size]
    val_df = df_clean.iloc[train_size:train_size + val_size]
    test_df = df_clean.iloc[train_size + val_size:]
    print(f"训练集: {len(train_df)} ({train_df.index.min()} to {train_df.index.max()})")
    print(f"验证集: {len(val_df)} ({val_df.index.min()} to {val_df.index.max()})")
    print(f"测试集: {len(test_df)} ({test_df.index.min()} to {test_df.index.max()})")
    return train_df, val_df, test_df

def get_feature_list(df_clean):
    long_term_features = [
        'hour', 'day_of_week', f'atr_14_{TIMEFRAME_MINUTES}m', f'range_norm_by_atr_{TIMEFRAME_MINUTES}m',
        'return_60min', 'return_120min', 'return_360min', 'return_720min',
        'volatility_ratio_120', 'volatility_ratio_360', 'volatility_ratio_720',
        'price_div_sma_120', 'price_div_sma_360', 'price_div_sma_720', 'sma_120_div_sma_720',
        'price_div_vwap_360', 'volume_div_vma_360', 'range_norm_by_atr_mean_12',
        'range_norm_by_atr_mean_24', 'body_percent_mean_12', 'body_percent_mean_24'
    ]
    available_features = [col for col in long_term_features if col in df_clean.columns]
    missing = [col for col in long_term_features if col not in df_clean.columns]
    if missing: print(f"警告：以下特征在数据中不存在: {missing}")
    print(f"使用的特征数量: {len(available_features)}")
    return available_features

def save_processed_data(df_clean, train_df, val_df, test_df, features, data_file):
    print(f"保存预处理数据到 {data_file}...")
    data_dict = {'df_clean': df_clean, 'train_df': train_df, 'val_df': val_df, 'test_df': test_df, 'features': features, 'save_time': datetime.now().isoformat()}
    with open(data_file, 'wb') as f: pickle.dump(data_dict, f)
    print(f"数据已保存到 {data_file}")

def load_processed_data(data_file):
    print(f"从 {data_file} 加载预处理数据...")
    if not os.path.exists(data_file):
        print(f"错误：数据文件 {data_file} 不存在！"); return None, None, None, None, None
    with open(data_file, 'rb') as f: data_dict = pickle.load(f)
    print(f"数据加载完成，保存时间: {data_dict.get('save_time', 'Unknown')}")
    return data_dict['df_clean'], data_dict['train_df'], data_dict['val_df'], data_dict['test_df'], data_dict['features']

def train_model(save_data=False, load_data=False, data_file=f'{MODEL_BASENAME}.pkl'):
    print(f"开始训练LightGBM模型 - {MODEL_BASENAME.replace('_', ' ').title()}")
    if load_data:
        df_clean, train_df, val_df, test_df, features = load_processed_data(data_file)
        if df_clean is None: load_data = False
    if not load_data:
        df = load_and_prepare_data()
        df_clean = prepare_features_and_labels(df)
        train_df, val_df, test_df = split_data(df_clean)
        features = get_feature_list(df_clean)
        if save_data:
            save_processed_data(df_clean, train_df, val_df, test_df, features, data_file)
    target = 'label'
    X_train, y_train = train_df[features], train_df[target]
    X_val, y_val = val_df[features], val_df[target]
    X_test, y_test = test_df[features], test_df[target]
    print("开始训练LightGBM模型...")
    lgbm = lgb.LGBMClassifier(objective='binary', metric='auc', n_estimators=2000, learning_rate=0.05, n_jobs=-1, random_state=42, verbose=-1)
    lgbm.fit(X_train, y_train, eval_set=[(X_val, y_val)], eval_metric='auc', callbacks=[lgb.early_stopping(stopping_rounds=100, verbose=False)])
    print("基础模型训练完成，开始概率校准...")
    calibrated_model = CalibratedClassifierCV(lgbm, method='isotonic', cv='prefit')
    calibrated_model.fit(X_val, y_val)
    print("概率校准完成，开始阈值优化...")
    val_probabilities = calibrated_model.predict_proba(X_val)[:, 1]
    thresholds_to_test = np.arange(0.52, 0.80, 0.01)
    best_score, best_threshold = -np.inf, 0.5
    for threshold in thresholds_to_test:
        predictions = np.where(val_probabilities > threshold, 1, np.where(val_probabilities < (1 - threshold), 0, -1))
        correct_trades = (predictions == y_val) & (predictions != -1)
        incorrect_trades = (predictions != y_val) & (predictions != -1)
        current_score = correct_trades.sum() - incorrect_trades.sum()
        if current_score > best_score:
            best_score, best_threshold = current_score, threshold
    print(f"验证集上的最优得分: {best_score:.0f}, 最优信心阈值: {best_threshold:.3f}")
    evaluate_on_test_set(calibrated_model, X_test, y_test, test_df, best_threshold)
    save_model_and_config(calibrated_model, features, best_threshold, len(X_train), len(X_val), len(X_test))
    analyze_feature_importance(lgbm, features)

def evaluate_on_test_set(model, X_test, y_test, test_df, best_threshold):
    """
    在测试集上评估模型, 并将详细结果保存到CSV文件。
    """
    best_threshold=0.9
    print(f"\n--- 测试集评估 (使用阈值: {best_threshold:.3f}) ---")
    
    test_probabilities = model.predict_proba(X_test)[:, 1]
    
    # 用于保存每一行详细结果的列表
    results_log = []

    for i in range(len(test_probabilities)):
        prob = test_probabilities[i]
        actual_result = y_test.iloc[i]
        current_price = test_df.iloc[i]['close']
        timestamp = test_df.index[i]

        guess = -1  # -1 代表放弃
        action_str = "放弃"
        result_str = "-"
        score_for_this_trade = 0

        if prob > best_threshold:
            guess = 1
            action_str = "\033[92m猜先涨\033[0m"
        elif prob < (1 - best_threshold):
            guess = 0
            action_str = "\033[91m猜先跌\033[0m"

        if guess != -1:
            if guess == actual_result:
                score_for_this_trade = 1
                result_str = "成功✅"
            else:
                score_for_this_trade = -1
                result_str = "失败❌"
        
        # 创建日志条目
        log_entry = {
            'Timestamp': timestamp,
            'ClosePrice': current_price,
            'ConfidenceUp': prob,
            'Action': action_str.replace('\033[92m', '').replace('\033[91m', '').replace('\033[0m', ''), # 清除颜色代码
            'Prediction': guess, # 1=涨, 0=跌, -1=放弃
            'ActualResult': actual_result,
            'Outcome': result_str,
            'Score': score_for_this_trade
        }
        results_log.append(log_entry)

    # 将日志转换为DataFrame
    results_df = pd.DataFrame(results_log)
    
    # 计算最终统计数据
    trades_made = (results_df['Prediction'] != -1).sum()
    wins = (results_df['Score'] == 1).sum()
    losses = (results_df['Score'] == -1).sum()
    test_score = wins - losses

    # 打印最终评估结果
    print(f"总样本数: {len(test_df)}")
    print(f"猜测次数: {trades_made} (占比: {trades_made/len(test_df)*100:.2f}%)")
    if trades_made > 0:
        print(f"胜率: {wins/trades_made*100:.2f}%")
    print(f"总得分: {test_score:+d}")

    # 保存到CSV文件
    results_filename = f'test_results_{MODEL_BASENAME}.csv'
    try:
        results_df.to_csv(results_filename, index=False, float_format='%.4f')
        print(f"\n详细测试结果已保存到文件: {results_filename}")
    except Exception as e:
        print(f"\n保存测试结果到CSV时出错: {e}")

def save_model_and_config(model, features, best_threshold, train_size, val_size, test_size):
    print("\n保存模型和配置...")
    model_file = f'{MODEL_BASENAME}_model.joblib'
    config_file = f'{MODEL_BASENAME}_config.json'
    joblib.dump(model, model_file)
    config = {
        'best_threshold': best_threshold, 'feature_list': features,
        'model_type': f'LGBM_{MODEL_BASENAME}',
        'target_description': f'predict_first_{UP_THRESHOLD*100}%_move_within_{MAX_LOOKFORWARD_MINUTES}_minutes',
        'training_date': datetime.now().isoformat(),
        'train_size': train_size, 'val_size': val_size, 'test_size': test_size,
        'up_threshold': UP_THRESHOLD, 'down_threshold': DOWN_THRESHOLD,
        'max_lookforward_minutes': MAX_LOOKFORWARD_MINUTES, 'timeframe_minutes': TIMEFRAME_MINUTES
    }
    with open(config_file, 'w') as f: json.dump(config, f, indent=2)
    print(f"模型文件: {model_file}\n配置文件: {config_file}")

def analyze_feature_importance(lgbm, features):
    print("\n" + "="*60 + "\n特征重要性分析\n" + "="*60)
    importance_df = pd.DataFrame({'feature': features, 'importance': lgbm.feature_importances_}).sort_values('importance', ascending=False)
    print(importance_df.head(20).to_string(index=False))
    importance_file = f'feature_importance_{MODEL_BASENAME}.csv'
    importance_df.to_csv(importance_file, index=False)
    print(f"\n特征重要性已保存到 {importance_file}")

def validate_model(model_file, config_file, load_data=False, data_file=f'{MODEL_BASENAME}.pkl'):
    print(f"加载模型: {model_file}\n加载配置: {config_file}")
    if not os.path.exists(model_file) or not os.path.exists(config_file):
        print(f"错误：模型或配置文件不存在！"); return
    model = joblib.load(model_file)
    with open(config_file, 'r') as f: config = json.load(f)
    print(f"模型类型: {config.get('model_type', 'Unknown')}, 训练日期: {config.get('training_date', 'Unknown')}")
    if load_data:
        df_clean, _, _, test_df, _ = load_processed_data(data_file)
        if df_clean is None: load_data = False
    if not load_data:
        df = load_and_prepare_data()
        df_clean = prepare_features_and_labels(df)
        _, _, test_df = split_data(df_clean)
    features = config.get('feature_list', [])
    missing_features = [f for f in features if f not in df_clean.columns]
    if missing_features:
        print(f"错误：以下特征在数据中不存在: {missing_features}"); return
    target = 'label'
    X_test, y_test = test_df[features], test_df[target]
    print(f"测试集大小: {len(X_test)}")
    evaluate_on_test_set(model, X_test, y_test, test_df, config.get('best_threshold', 0.5))
    print("\n验证完成！")

def main():
    parser = argparse.ArgumentParser(description=f"LGBM 模型 - {MODEL_BASENAME.replace('_', ' ').title()}")
    parser.add_argument("--mode", choices=['train', 'validate'], default='train', help="运行模式")
    parser.add_argument("--model-file", default=f'{MODEL_BASENAME}_model.joblib', help="模型文件路径")
    parser.add_argument("--config-file", default=f'{MODEL_BASENAME}_config.json', help="配置文件路径")
    parser.add_argument("--save-data", action='store_true', help="保存预处理后的数据")
    parser.add_argument("--load-data", action='store_true', help="加载预处理后的数据")
    parser.add_argument("--data-file", default=f'{MODEL_BASENAME}.pkl', help="预处理数据文件路径")
    args = parser.parse_args()
    if args.mode == 'train':
        train_model(args.save_data, args.load_data, args.data_file)
    else:
        validate_model(args.model_file, args.config_file, args.load_data, args.data_file)

if __name__ == '__main__':
    main()