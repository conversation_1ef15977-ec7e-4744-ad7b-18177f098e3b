{"cells": [{"cell_type": "code", "execution_count": 5, "id": "001438a3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["开始计算抄底分数，这可能需要几分钟...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 47935/47935 [00:00<00:00, 292272.50it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "抄底分数计算完成!\n", "抄底分数分布预览:\n", "count    47935.000000\n", "mean        -0.118748\n", "std          0.673749\n", "min         -1.000000\n", "25%         -0.732143\n", "50%         -0.241071\n", "75%          0.504960\n", "max          1.000000\n", "Name: bottom_score, dtype: float64\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import numpy as np\n", "import pandas_ta as ta\n", "from tqdm import tqdm\n", "import matplotlib.pyplot as plt\n", "\n", "# --- 1. 加载数据 ---\n", "# (这部分代码不变)\n", "df = pd.read_csv('sui.csv')\n", "df['Timestamp'] = pd.to_datetime(df['Timestamp'], unit='s')\n", "df.set_index('Timestamp', inplace=True)\n", "df.sort_index(inplace=True)\n", "df = df[~df.index.duplicated(keep='first')]\n", "\n", "# --- 2. 定义目标变量 Y (抄底分数) - 修正后的高效计算方法 ---\n", "# --- 参数定义 ---\n", "# 假设数据是5分钟K线, 一天 = 24 * 60 / 5 = 288 周期\n", "# 未来7天 = 288 * 7 = 2016 个周期\n", "LOOK_FORWARD_PERIODS = 288 * 7\n", "\n", "print(\"开始计算抄底分数，这可能需要几分钟...\")\n", "\n", "# 将需要用到的列转换为NumPy数组，极大提升速度\n", "lows = df['Low'].to_numpy()\n", "closes = df['Close'].to_numpy()\n", "n = len(df)\n", "\n", "# 创建一个空的数组来存放分数\n", "scores = np.full(n, np.nan) # 使用 np.nan 作为默认值\n", "\n", "# 使用高效的循环和NumPy操作\n", "# 我们只计算那些有足够未来数据的点\n", "for i in tqdm(range(n - LOOK_FORWARD_PERIODS)):\n", "    # 当前的 Low 价\n", "    current_low = lows[i]\n", "    \n", "    # 未来 N 个周期的收盘价窗口\n", "    future_window = closes[i + 1 : i + 1 + LOOK_FORWARD_PERIODS]\n", "    \n", "    # 计算未来价格高于当前 'Low' 的比例 P\n", "    higher_count = np.sum(future_window > current_low)\n", "    p = higher_count / LOOK_FORWARD_PERIODS # 分母是固定的周期数\n", "    \n", "    # 根据公式计算分数\n", "    score = (p - 0.5) * 2\n", "    \n", "    # 将计算出的分数存入数组\n", "    scores[i] = score\n", "\n", "# 将计算好的分数作为一个新列添加回DataFrame\n", "df['bottom_score'] = scores\n", "\n", "# 清理掉尾部那些无法计算分数的行 (值为NaN的行)\n", "df.dropna(subset=['bottom_score'], inplace=True)\n", "\n", "print(\"\\n抄底分数计算完成!\")\n", "print(\"抄底分数分布预览:\")\n", "print(df['bottom_score'].describe())\n", "\n", "# 绘制分数分布图，直观感受\n", "df['bottom_score'].hist(bins=50)\n", "plt.title('Distribution of Bottom Score')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "3fddaaa3", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/7k/mcrgjq_x5l7bjfqb_bszt78w0000gn/T/ipykernel_64050/4045911640.py:25: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df['sma_20_daily'].ffill(inplace=True)\n"]}], "source": ["# --- 3. 构建特征 X ---\n", "df.ta.sma(length=20, append=True)\n", "df.ta.ema(length=50, append=True)\n", "df.ta.rsi(length=14, append=True)\n", "df.ta.macd(fast=12, slow=26, signal=9, append=True)\n", "df.ta.bbands(length=20, std=2, append=True)\n", "df.ta.atr(length=14, append=True)\n", "df.ta.obv(append=True)\n", "\n", "df['taker_buy_ratio'] = df['TakerBuyBaseVolume'] / df['Volume']\n", "df['dist_from_sma20'] = (df['Close'] / df['SMA_20']) - 1\n", "df['high_roll_288'] = df['High'].rolling(288).max() # 1-day high\n", "df['low_roll_288'] = df['Low'].rolling(288).min()   # 1-day low\n", "df['dist_from_high_roll'] = (df['Close'] / df['high_roll_288']) - 1\n", "\n", "# 例如，计算日线级别的移动平均线，并将其应用到分钟线数据上\n", "# 先计算日线数据\n", "df_daily = df.resample('D').agg({\n", "    'Open': 'first', 'High': 'max', 'Low': 'min', 'Close': 'last', 'Volume': 'sum'\n", "})\n", "df_daily['sma_20_daily'] = ta.sma(df_daily['Close'], length=20)\n", "\n", "# 将日线指标映射回分钟线 (使用 ffill 向前填充)\n", "df = df.merge(df_daily[['sma_20_daily']], left_index=True, right_index=True, how='left')\n", "df['sma_20_daily'].ffill(inplace=True)\n", "\n", "# 创建一个新特征：当前价格与日线级别均线的偏离度\n", "df['dist_from_daily_sma'] = (df['Close'] / df['sma_20_daily']) - 1\n", "\n", "\n", "# 清理因计算指标产生的NaN\n", "df.dropna(inplace=True)\n", "\n", "# 准备最终的训练数据\n", "features = [col for col in df.columns if col not in ['bottom_score', 'CloseTime']]\n", "X = df[features]\n", "y = df['bottom_score']"]}, {"cell_type": "code", "execution_count": 9, "id": "d39430c7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["开始训练模型...\n", "[0]\tvalidation_0-rmse:0.67794\n", "[1]\tvalidation_0-rmse:0.67947\n", "[2]\tvalidation_0-rmse:0.68047\n", "[3]\tvalidation_0-rmse:0.68180\n", "[4]\tvalidation_0-rmse:0.68294\n", "[5]\tvalidation_0-rmse:0.68443\n", "[6]\tvalidation_0-rmse:0.68558\n", "[7]\tvalidation_0-rmse:0.68689\n", "[8]\tvalidation_0-rmse:0.68833\n", "[9]\tvalidation_0-rmse:0.68925\n", "[10]\tvalidation_0-rmse:0.69032\n", "[11]\tvalidation_0-rmse:0.69168\n", "[12]\tvalidation_0-rmse:0.69263\n", "[13]\tvalidation_0-rmse:0.69410\n", "[14]\tvalidation_0-rmse:0.69520\n", "[15]\tvalidation_0-rmse:0.69638\n", "[16]\tvalidation_0-rmse:0.69708\n", "[17]\tvalidation_0-rmse:0.69870\n", "[18]\tvalidation_0-rmse:0.69936\n", "[19]\tvalidation_0-rmse:0.70090\n", "[20]\tvalidation_0-rmse:0.70266\n", "[21]\tvalidation_0-rmse:0.70423\n", "[22]\tvalidation_0-rmse:0.70595\n", "[23]\tvalidation_0-rmse:0.70664\n", "[24]\tvalidation_0-rmse:0.70823\n", "[25]\tvalidation_0-rmse:0.70891\n", "[26]\tvalidation_0-rmse:0.71067\n", "[27]\tvalidation_0-rmse:0.71251\n", "[28]\tvalidation_0-rmse:0.71383\n", "[29]\tvalidation_0-rmse:0.71491\n", "[30]\tvalidation_0-rmse:0.71556\n", "[31]\tvalidation_0-rmse:0.71660\n", "[32]\tvalidation_0-rmse:0.71832\n", "[33]\tvalidation_0-rmse:0.71960\n", "[34]\tvalidation_0-rmse:0.71772\n", "[35]\tvalidation_0-rmse:0.71925\n", "[36]\tvalidation_0-rmse:0.71995\n", "[37]\tvalidation_0-rmse:0.72173\n", "[38]\tvalidation_0-rmse:0.72259\n", "[39]\tvalidation_0-rmse:0.72372\n", "[40]\tvalidation_0-rmse:0.72508\n", "[41]\tvalidation_0-rmse:0.72740\n", "[42]\tvalidation_0-rmse:0.72880\n", "[43]\tvalidation_0-rmse:0.72689\n", "[44]\tvalidation_0-rmse:0.72846\n", "[45]\tvalidation_0-rmse:0.72956\n", "[46]\tvalidation_0-rmse:0.73028\n", "[47]\tvalidation_0-rmse:0.73137\n", "[48]\tvalidation_0-rmse:0.73219\n", "[49]\tvalidation_0-rmse:0.73280\n", "[50]\tvalidation_0-rmse:0.73042\n", "\n", "模型训练完成！\n"]}], "source": ["import xgboost as xgb\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score\n", "\n", "# --- 4. 数据集划分 (时间序列) ---\n", "# (这部分代码不变)\n", "split_index = int(len(df) * 0.8)\n", "X_train, X_test = X.iloc[:split_index], X.iloc[split_index:]\n", "y_train, y_test = y.iloc[:split_index], y.iloc[split_index:]\n", "\n", "# --- 5. 训练 XGBoost 回归模型 (修正) ---\n", "\n", "# 注意：我们将 early_stopping_rounds 移到了这里！\n", "model = xgb.XGBRegressor(\n", "    objective='reg:squarederror', \n", "    eval_metric='rmse',\n", "    n_estimators=1000,\n", "    learning_rate=0.01,\n", "    max_depth=5,\n", "    subsample=0.8,\n", "    colsample_bytree=0.8,\n", "    random_state=42,\n", "    n_jobs=-1,\n", "    # 将 early_stopping_rounds 参数放在模型初始化中\n", "    early_stopping_rounds=50 \n", ")\n", "\n", "# 现在 fit 方法中不再需要这个参数\n", "print(\"开始训练模型...\")\n", "model.fit(X_train, y_train, \n", "          eval_set=[(X_test, y_test)],\n", "          verbose=True # verbose可以保留，用于打印训练过程\n", "         )\n", "\n", "print(\"\\n模型训练完成！\")"]}, {"cell_type": "code", "execution_count": 10, "id": "b574cc3a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- 回归模型性能评估 ---\n", "均方根误差 (RMSE): 0.6779\n", "平均绝对误差 (MAE): 0.5946\n", "R^2 分数: -0.0329\n"]}], "source": ["# --- 6. 模型评估 ---\n", "y_pred = model.predict(X_test)\n", "\n", "rmse = np.sqrt(mean_squared_error(y_test, y_pred))\n", "mae = mean_absolute_error(y_test, y_pred)\n", "r2 = r2_score(y_test, y_pred)\n", "\n", "print(\"\\n--- 回归模型性能评估 ---\")\n", "print(f\"均方根误差 (RMSE): {rmse:.4f}\")\n", "print(f\"平均绝对误差 (MAE): {mae:.4f}\")\n", "print(f\"R^2 分数: {r2:.4f}\")\n", "\n", "# MAE的解读：例如 MAE=0.2，表示模型预测的分数平均偏离真实分数0.2。\n", "# R^2的解读：表示模型解释了目标变量（分数）方差的百分比。越高越好。"]}, {"cell_type": "code", "execution_count": 13, "id": "79151277", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 2000x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "# --- 7. 生成交易信号并可视化 ---\n", "# 这是一个关键参数，需要你根据回测和风险偏好来调整\n", "# 0.8 意味着我们想找一个点，模型预测在未来7天，90%的时间价格都会比它高\n", "# (0.9 - 0.5) * 2 = 0.8\n", "BUY_SCORE_THRESHOLD = 0.7\n", "\n", "# 将预测分数添加到测试集的DataFrame中\n", "test_df = df.iloc[split_index:].copy()\n", "test_df['predicted_score'] = y_pred\n", "\n", "# 找到模型发出买入信号的时间点\n", "buy_signals = test_df[test_df['predicted_score'] >= BUY_SCORE_THRESHOLD]\n", "\n", "# 绘图\n", "plt.figure(figsize=(20, 8))\n", "plt.plot(test_df.index, test_df['Close'], label='BTC Price', color='skyblue', alpha=0.8)\n", "plt.scatter(buy_signals.index, buy_signals['Low'], marker='^', color='red', s=120, label=f'Buy Signal (Score >= {BUY_SCORE_THRESHOLD})', zorder=5)\n", "\n", "# 为了对比，我们把真实的“好”买点也画出来\n", "true_good_buys = test_df[test_df['bottom_score'] >= BUY_SCORE_THRESHOLD]\n", "# plt.scatter(true_good_buys.index, true_good_buys['Low'], marker='o', color='green', s=50, alpha=0.7, label=f'True Good Buy (Score >= {BUY_SCORE_THRESHOLD})', zorder=4)\n", "\n", "plt.title('BTC Price with Predicted Buy Signals vs. True Good Buys')\n", "plt.legend()\n", "plt.grid(True, linestyle='--', alpha=0.5)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 14, "id": "eb119bef", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["模型预测分数的统计描述:\n", "count    9530.000000\n", "mean       -0.143029\n", "std         0.002517\n", "min        -0.146598\n", "25%        -0.145174\n", "50%        -0.145174\n", "75%        -0.140705\n", "max        -0.135002\n", "dtype: float64\n"]}], "source": ["# 在你生成 y_pred 之后，运行这行代码\n", "predicted_scores_series = pd.Series(y_pred, index=y_test.index)\n", "\n", "print(\"模型预测分数的统计描述:\")\n", "print(predicted_scores_series.describe())"]}, {"cell_type": "code", "execution_count": 16, "id": "0b44343b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- 训练集真实分数 y_train 的分布 ---\n", "count    38118.000000\n", "mean        -0.140457\n", "std          0.675315\n", "min         -1.000000\n", "25%         -0.757937\n", "50%         -0.285714\n", "75%          0.457341\n", "max          1.000000\n", "Name: bottom_score, dtype: float64\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 检查训练集中 y_train 的分布\n", "print(\"--- 训练集真实分数 y_train 的分布 ---\")\n", "print(y_train.describe())\n", "\n", "# 绘制 y_train 的直方图\n", "y_train.hist(bins=100)\n", "plt.title('Distribution of True Scores in Training Set (y_train)')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 17, "id": "f7465dfd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["New Target Distribution:\n", "trade_signal\n", "1    27663\n", "0     8056\n", "2     6622\n", "Name: count, dtype: int64\n"]}], "source": ["# Define the thresholds for your categories. These can be tuned.\n", "# We're saying anything with a score > 0.8 is a 'good buy'\n", "# and anything < -0.8 is a 'bad buy'.\n", "GOOD_BUY_THRESHOLD = 0.8\n", "BAD_BUY_THRESHOLD = -0.8\n", "\n", "# Create a function to apply the labels\n", "def classify_score(score):\n", "    if score >= GOOD_BUY_THRESHOLD:\n", "        return 2  # 'GOOD_BUY'\n", "    elif score <= BAD_BUY_THRESHOLD:\n", "        return 0  # 'BAD_BUY'\n", "    else:\n", "        return 1  # 'NEUTRAL'\n", "\n", "# Apply this function to create the new target column\n", "df['trade_signal'] = df['bottom_score'].apply(classify_score)\n", "\n", "# Check the distribution of our new target. It should be unbalanced, which is fine.\n", "print(\"New Target Distribution:\")\n", "print(df['trade_signal'].value_counts())\n", "\n", "# Our new features X remain the same\n", "features = [col for col in df.columns if col not in ['bottom_score', 'trade_signal', 'CloseTime']] \n", "X = df[features]\n", "\n", "# Our new target y is the categorical signal\n", "y = df['trade_signal']"]}, {"cell_type": "code", "execution_count": 18, "id": "5ea804d0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting Multiclass Model Training...\n", "[0]\tvalidation_0-mlogloss:1.11187\n", "[1]\tvalidation_0-mlogloss:1.12502\n", "[2]\tvalidation_0-mlogloss:1.14337\n", "[3]\tvalidation_0-mlogloss:1.15779\n", "[4]\tvalidation_0-mlogloss:1.17424\n", "[5]\tvalidation_0-mlogloss:1.19140\n", "[6]\tvalidation_0-mlogloss:1.21427\n", "[7]\tvalidation_0-mlogloss:1.21424\n", "[8]\tvalidation_0-mlogloss:1.23794\n", "[9]\tvalidation_0-mlogloss:1.24049\n", "[10]\tvalidation_0-mlogloss:1.26252\n", "[11]\tvalidation_0-mlogloss:1.26958\n", "[12]\tvalidation_0-mlogloss:1.29427\n", "[13]\tvalidation_0-mlogloss:1.30115\n", "[14]\tvalidation_0-mlogloss:1.32407\n", "[15]\tvalidation_0-mlogloss:1.33102\n", "[16]\tvalidation_0-mlogloss:1.35318\n", "[17]\tvalidation_0-mlogloss:1.35922\n", "[18]\tvalidation_0-mlogloss:1.36290\n", "[19]\tvalidation_0-mlogloss:1.37368\n", "[20]\tvalidation_0-mlogloss:1.38320\n", "[21]\tvalidation_0-mlogloss:1.39405\n", "[22]\tvalidation_0-mlogloss:1.40241\n", "[23]\tvalidation_0-mlogloss:1.42991\n", "[24]\tvalidation_0-mlogloss:1.45807\n", "[25]\tvalidation_0-mlogloss:1.47859\n", "[26]\tvalidation_0-mlogloss:1.47130\n", "[27]\tvalidation_0-mlogloss:1.47709\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/project/ai/vibe/daily/btc-quant/.venv/lib/python3.12/site-packages/xgboost/callback.py:386: UserWarning: [21:57:40] WARNING: /Users/<USER>/work/xgboost/xgboost/src/learner.cc:738: \n", "Parameters: { \"use_label_encoder\" } are not used.\n", "\n", "  self.starting_round = model.num_boosted_rounds()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[28]\tvalidation_0-mlogloss:1.47900\n", "[29]\tvalidation_0-mlogloss:1.48125\n", "[30]\tvalidation_0-mlogloss:1.47538\n", "[31]\tvalidation_0-mlogloss:1.46331\n", "[32]\tvalidation_0-mlogloss:1.45189\n", "[33]\tvalidation_0-mlogloss:1.45198\n", "[34]\tvalidation_0-mlogloss:1.44480\n", "[35]\tvalidation_0-mlogloss:1.43523\n", "[36]\tvalidation_0-mlogloss:1.42570\n", "[37]\tvalidation_0-mlogloss:1.42006\n", "[38]\tvalidation_0-mlogloss:1.41078\n", "[39]\tvalidation_0-mlogloss:1.40958\n", "[40]\tvalidation_0-mlogloss:1.40849\n", "[41]\tvalidation_0-mlogloss:1.40620\n", "[42]\tvalidation_0-mlogloss:1.40347\n", "[43]\tvalidation_0-mlogloss:1.40996\n", "[44]\tvalidation_0-mlogloss:1.41094\n", "[45]\tvalidation_0-mlogloss:1.41293\n", "[46]\tvalidation_0-mlogloss:1.41092\n", "[47]\tvalidation_0-mlogloss:1.41200\n", "[48]\tvalidation_0-mlogloss:1.41825\n", "[49]\tvalidation_0-mlogloss:1.42168\n"]}, {"data": {"text/html": ["<style>#sk-container-id-1 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: #000;\n", "  --sklearn-color-text-muted: #666;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-1 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-1 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-1 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-1 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-1 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-1 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-1 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: flex;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "  align-items: start;\n", "  justify-content: space-between;\n", "  gap: 0.5em;\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label .caption {\n", "  font-size: 0.6rem;\n", "  font-weight: lighter;\n", "  color: var(--sklearn-color-text-muted);\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-1 div.sk-toggleable__content {\n", "  display: none;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  display: block;\n", "  width: 100%;\n", "  overflow: visible;\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-1 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-1 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-1 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-1 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-1 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-1 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-1 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-1 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-1 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 0.5em;\n", "  text-align: center;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-1 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-1 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".estimator-table summary {\n", "    padding: .5rem;\n", "    font-family: monospace;\n", "    cursor: pointer;\n", "}\n", "\n", ".estimator-table details[open] {\n", "    padding-left: 0.1rem;\n", "    padding-right: 0.1rem;\n", "    padding-bottom: 0.3rem;\n", "}\n", "\n", ".estimator-table .parameters-table {\n", "    margin-left: auto !important;\n", "    margin-right: auto !important;\n", "}\n", "\n", ".estimator-table .parameters-table tr:nth-child(odd) {\n", "    background-color: #fff;\n", "}\n", "\n", ".estimator-table .parameters-table tr:nth-child(even) {\n", "    background-color: #f6f6f6;\n", "}\n", "\n", ".estimator-table .parameters-table tr:hover {\n", "    background-color: #e0e0e0;\n", "}\n", "\n", ".estimator-table table td {\n", "    border: 1px solid rgba(106, 105, 104, 0.232);\n", "}\n", "\n", ".user-set td {\n", "    color:rgb(255, 94, 0);\n", "    text-align: left;\n", "}\n", "\n", ".user-set td.value pre {\n", "    color:rgb(255, 94, 0) !important;\n", "    background-color: transparent !important;\n", "}\n", "\n", ".default td {\n", "    color: black;\n", "    text-align: left;\n", "}\n", "\n", ".user-set td i,\n", ".default td i {\n", "    color: black;\n", "}\n", "\n", ".copy-paste-icon {\n", "    background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA0NDggNTEyIj48IS0tIUZvbnQgQXdlc29tZSBGcmVlIDYuNy4yIGJ5IEBmb250YXdlc29tZSAtIGh0dHBzOi8vZm9udGF3ZXNvbWUuY29tIExpY2Vuc2UgLSBodHRwczovL2ZvbnRhd2Vzb21lLmNvbS9saWNlbnNlL2ZyZWUgQ29weXJpZ2h0IDIwMjUgRm9udGljb25zLCBJbmMuLS0+PHBhdGggZD0iTTIwOCAwTDMzMi4xIDBjMTIuNyAwIDI0LjkgNS4xIDMzLjkgMTQuMWw2Ny45IDY3LjljOSA5IDE0LjEgMjEuMiAxNC4xIDMzLjlMNDQ4IDMzNmMwIDI2LjUtMjEuNSA0OC00OCA0OGwtMTkyIDBjLTI2LjUgMC00OC0yMS41LTQ4LTQ4bDAtMjg4YzAtMjYuNSAyMS41LTQ4IDQ4LTQ4ek00OCAxMjhsODAgMCAwIDY0LTY0IDAgMCAyNTYgMTkyIDAgMC0zMiA2NCAwIDAgNDhjMCAyNi41LTIxLjUgNDgtNDggNDhMNDggNTEyYy0yNi41IDAtNDgtMjEuNS00OC00OEwwIDE3NmMwLTI2LjUgMjEuNS00OCA0OC00OHoiLz48L3N2Zz4=);\n", "    background-repeat: no-repeat;\n", "    background-size: 14px 14px;\n", "    background-position: 0;\n", "    display: inline-block;\n", "    width: 14px;\n", "    height: 14px;\n", "    cursor: pointer;\n", "}\n", "</style><body><div id=\"sk-container-id-1\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>XGBClassifier(base_score=None, booster=None, callbacks=None,\n", "              colsample_bylevel=None, colsample_bynode=None,\n", "              colsample_bytree=None, device=None, early_stopping_rounds=50,\n", "              enable_categorical=False, eval_metric=&#x27;mlogloss&#x27;,\n", "              feature_types=None, feature_weights=None, gamma=None,\n", "              grow_policy=None, importance_type=None,\n", "              interaction_constraints=None, learning_rate=0.05, max_bin=None,\n", "              max_cat_threshold=None, max_cat_to_onehot=None,\n", "              max_delta_step=None, max_depth=None, max_leaves=None,\n", "              min_child_weight=None, missing=nan, monotone_constraints=None,\n", "              multi_strategy=None, n_estimators=500, n_jobs=None, num_class=3, ...)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-1\" type=\"checkbox\" checked><label for=\"sk-estimator-id-1\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>XGBClassifier</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://xgboost.readthedocs.io/en/release_3.0.0/python/python_api.html#xgboost.XGBClassifier\">?<span>Documentation for XGBClassifier</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></div></label><div class=\"sk-toggleable__content fitted\" data-param-prefix=\"\">\n", "        <div class=\"estimator-table\">\n", "            <details>\n", "                <summary>Parameters</summary>\n", "                <table class=\"parameters-table\">\n", "                  <tbody>\n", "                    \n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('objective',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">objective&nbsp;</td>\n", "            <td class=\"value\">&#x27;multi:softprob&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('base_score',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">base_score&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('booster',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">booster&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('callbacks',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">callbacks&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('colsample_bylevel',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">colsample_bylevel&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('colsample_bynode',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">colsample_bynode&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('colsample_bytree',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">colsample_bytree&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('device',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">device&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('early_stopping_rounds',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">early_stopping_rounds&nbsp;</td>\n", "            <td class=\"value\">50</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('enable_categorical',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">enable_categorical&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('eval_metric',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">eval_metric&nbsp;</td>\n", "            <td class=\"value\">&#x27;mlogloss&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('feature_types',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">feature_types&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('feature_weights',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">feature_weights&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('gamma',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">gamma&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('grow_policy',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">grow_policy&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('importance_type',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">importance_type&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('interaction_constraints',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">interaction_constraints&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('learning_rate',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">learning_rate&nbsp;</td>\n", "            <td class=\"value\">0.05</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('max_bin',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">max_bin&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('max_cat_threshold',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">max_cat_threshold&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('max_cat_to_onehot',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">max_cat_to_onehot&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('max_delta_step',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">max_delta_step&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('max_depth',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">max_depth&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('max_leaves',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">max_leaves&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('min_child_weight',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">min_child_weight&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('missing',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">missing&nbsp;</td>\n", "            <td class=\"value\">nan</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('monotone_constraints',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">monotone_constraints&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('multi_strategy',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">multi_strategy&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('n_estimators',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">n_estimators&nbsp;</td>\n", "            <td class=\"value\">500</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('n_jobs',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">n_jobs&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('num_parallel_tree',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">num_parallel_tree&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('random_state',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">random_state&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('reg_alpha',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">reg_alpha&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('reg_lambda',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">reg_lambda&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('sampling_method',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">sampling_method&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('scale_pos_weight',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">scale_pos_weight&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('subsample',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">subsample&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('tree_method',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">tree_method&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('validate_parameters',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">validate_parameters&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('verbosity',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">verbosity&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('num_class',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">num_class&nbsp;</td>\n", "            <td class=\"value\">3</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('use_label_encoder',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">use_label_encoder&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "                  </tbody>\n", "                </table>\n", "            </details>\n", "        </div>\n", "    </div></div></div></div></div><script>function copyToClipboard(text, element) {\n", "    // Get the parameter prefix from the closest toggleable content\n", "    const toggleableContent = element.closest('.sk-toggleable__content');\n", "    const paramPrefix = toggleableContent ? toggleableContent.dataset.paramPrefix : '';\n", "    const fullParamName = paramPrefix ? `${paramPrefix}${text}` : text;\n", "\n", "    const originalStyle = element.style;\n", "    const computedStyle = window.getComputedStyle(element);\n", "    const originalWidth = computedStyle.width;\n", "    const originalHTML = element.innerHTML.replace('Copied!', '');\n", "\n", "    navigator.clipboard.writeText(fullParamName)\n", "        .then(() => {\n", "            element.style.width = originalWidth;\n", "            element.style.color = 'green';\n", "            element.innerHTML = \"Copied!\";\n", "\n", "            setTimeout(() => {\n", "                element.innerHTML = originalHTML;\n", "                element.style = originalStyle;\n", "            }, 2000);\n", "        })\n", "        .catch(err => {\n", "            console.error('Failed to copy:', err);\n", "            element.style.color = 'red';\n", "            element.innerHTML = \"Failed!\";\n", "            setTimeout(() => {\n", "                element.innerHTML = originalHTML;\n", "                element.style = originalStyle;\n", "            }, 2000);\n", "        });\n", "    return false;\n", "}\n", "\n", "document.querySelectorAll('.fa-regular.fa-copy').forEach(function(element) {\n", "    const toggleableContent = element.closest('.sk-toggleable__content');\n", "    const paramPrefix = toggleableContent ? toggleableContent.dataset.paramPrefix : '';\n", "    const paramName = element.parentElement.nextElementSibling.textContent.trim();\n", "    const fullParamName = paramPrefix ? `${paramPrefix}${paramName}` : paramName;\n", "\n", "    element.setAttribute('title', fullParamName);\n", "});\n", "</script></body>"], "text/plain": ["XGBClassifier(base_score=None, booster=None, callbacks=None,\n", "              colsample_bylevel=None, colsample_bynode=None,\n", "              colsample_bytree=None, device=None, early_stopping_rounds=50,\n", "              enable_categorical=False, eval_metric='mlogloss',\n", "              feature_types=None, feature_weights=None, gamma=None,\n", "              grow_policy=None, importance_type=None,\n", "              interaction_constraints=None, learning_rate=0.05, max_bin=None,\n", "              max_cat_threshold=None, max_cat_to_onehot=None,\n", "              max_delta_step=None, max_depth=None, max_leaves=None,\n", "              min_child_weight=None, missing=nan, monotone_constraints=None,\n", "              multi_strategy=None, n_estimators=500, n_jobs=None, num_class=3, ...)"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["import xgboost as xgb\n", "from sklearn.metrics import classification_report\n", "\n", "# Split data (same as before)\n", "split_index = int(len(df) * 0.8)\n", "X_train, X_test = X.iloc[:split_index], X.iloc[split_index:]\n", "y_train, y_test = y.iloc[:split_index], y.iloc[split_index:]\n", "\n", "# --- Train a MULTICLASS XGBoost Classifier ---\n", "# Note: XGBoost will automatically handle the class weights if you want,\n", "# but for now let's start simple.\n", "model = xgb.XGBClassifier(\n", "    # The objective is now 'multi:softprob' which gives probabilities for each class\n", "    objective='multi:softprob', \n", "    eval_metric='mlogloss',      # Evaluation metric for multiclass\n", "    num_class=3,                 # We have 3 classes (0, 1, 2)\n", "    n_estimators=500,\n", "    learning_rate=0.05,\n", "    # You can add the rest of your parameters (max_depth, etc.)\n", "    early_stopping_rounds=50,    # Add this back in for the new XGBoost versions\n", "    use_label_encoder=False      # Good practice\n", ")\n", "\n", "print(\"Starting Multiclass Model Training...\")\n", "model.fit(X_train, y_train, \n", "          eval_set=[(X_test, y_test)],\n", "          verbose=True)"]}, {"cell_type": "code", "execution_count": 19, "id": "e1e708cb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- Multiclass Classification Report ---\n", "              precision    recall  f1-score   support\n", "\n", " BAD_BUY (0)       0.22      0.32      0.26      1450\n", " NEUTRAL (1)       0.85      0.15      0.26      5808\n", "GOOD_BUY (2)       0.12      0.53      0.20      1211\n", "\n", "    accuracy                           0.24      8469\n", "   macro avg       0.40      0.34      0.24      8469\n", "weighted avg       0.64      0.24      0.25      8469\n", "\n"]}], "source": ["# This will return an array with shape (n_samples, 3)\n", "# Each row contains the probability for class 0, 1, and 2 respectively.\n", "y_pred_proba = model.predict_proba(X_test)\n", "\n", "# To get the final predicted class, use .predict()\n", "y_pred = model.predict(X_test)\n", "\n", "print(\"\\n--- Multiclass Classification Report ---\")\n", "print(classification_report(y_test, y_pred, target_names=['BAD_BUY (0)', 'NEUTRAL (1)', 'GOOD_BUY (2)']))\n", "\n", "# --- TO FIND YOUR BUY SIGNALS ---\n", "# You want to find where the model is confident about class 2 ('GOOD_BUY')\n", "good_buy_probability = y_pred_proba[:, 2] # Probabilities for the 'GOOD_BUY' class\n", "\n", "# Add this to your test dataframe\n", "test_df = df.iloc[split_index:].copy()\n", "test_df['good_buy_prob'] = good_buy_probability\n", "\n", "# Set a probability threshold. Let's say we only trust signals with > 70% confidence.\n", "PROB_THRESHOLD = 0.70\n", "buy_signals = test_df[test_df['good_buy_prob'] >= PROB_THRESHOLD]\n", "\n", "# Now, visualize these 'buy_signals' on your chart. You should see some results!"]}, {"cell_type": "code", "execution_count": 20, "id": "318d9aff", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Found 0 high-confidence buy signals with a threshold of 0.7\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 2000x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# --- Use Probabilities to Generate High-Confidence Signals ---\n", "\n", "# This gives us the probability for each class [prob_bad, prob_neutral, prob_good]\n", "y_pred_proba = model.predict_proba(X_test)\n", "\n", "# We only care about the probability of our target class, 'GOOD_BUY' (which is the last column, index 2)\n", "good_buy_probability = y_pred_proba[:, 2] \n", "\n", "# Add this to your test dataframe for analysis\n", "test_df = df.iloc[split_index:].copy()\n", "test_df['good_buy_prob'] = good_buy_probability\n", "\n", "# --- THIS IS THE KEY ---\n", "# Set a high threshold. Don't listen to the model unless it's very confident.\n", "# Let's start with 70%. You can tune this parameter.\n", "PROB_THRESHOLD = 0.70 \n", "\n", "buy_signals = test_df[test_df['good_buy_prob'] >= PROB_THRESHOLD]\n", "\n", "print(f\"\\nFound {len(buy_signals)} high-confidence buy signals with a threshold of {PROB_THRESHOLD}\")\n", "\n", "# Now, visualize these new, filtered 'buy_signals' on your chart.\n", "# (Your visualization code for plotting the signals remains the same)\n", "plt.figure(figsize=(20, 8))\n", "plt.plot(test_df.index, test_df['Close'], label='BTC Price', color='skyblue', alpha=0.8)\n", "if not buy_signals.empty:\n", "    plt.scatter(buy_signals.index, buy_signals['Low'], marker='^', color='red', s=120, label=f'Confident Buy Signal (Prob >= {PROB_THRESHOLD})', zorder=5)\n", "plt.title('BTC Price with High-Confidence Buy Signals')\n", "plt.legend()\n", "plt.grid(True, linestyle='--', alpha=0.5)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 21, "id": "6cd6a88b", "metadata": {}, "outputs": [], "source": ["from sklearn.utils.class_weight import compute_class_weight\n", "\n", "# Calculate weights before training the model\n", "# This will give more weight to the classes with fewer samples\n", "weights = compute_class_weight(class_weight='balanced', classes=np.unique(y_train), y=y_train)\n", "# The output will be an array of weights, one for each class [w_bad, w_neutral, w_good]\n", "\n", "# Then, you would ideally pass this to your model. \n", "# XGBoost doesn't have a direct `class_weight` param like scikit-learn,\n", "# but for binary cases you use `scale_pos_weight`. For multiclass, it's more complex.\n", "#\n", "# **For now, focus on Action 1 and 2. They are more straightforward and often sufficient.**"]}, {"cell_type": "code", "execution_count": 22, "id": "1a8289e8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Statistics for the 'GOOD_BUY' probability predictions:\n", "count    8469.000000\n", "mean        0.346272\n", "std         0.014351\n", "min         0.315527\n", "25%         0.345428\n", "50%         0.347016\n", "75%         0.358178\n", "max         0.358580\n", "Name: good_buy_prob, dtype: float64\n", "\n", "A more realistic threshold (99th percentile) would be: 0.3586\n"]}], "source": ["# (Code from before)\n", "y_pred_proba = model.predict_proba(X_test)\n", "good_buy_probability = y_pred_proba[:, 2] \n", "test_df = df.iloc[split_index:].copy()\n", "test_df['good_buy_prob'] = good_buy_probability\n", "\n", "# --- INVESTIGATE THE PROBABILITIES ---\n", "print(\"Statistics for the 'GOOD_BUY' probability predictions:\")\n", "print(test_df['good_buy_prob'].describe())\n", "\n", "# --- FIND A REALISTIC THRESHOLD ---\n", "# Let's see what the top 1% of predictions look like.\n", "# This gives us a threshold that will guarantee we get some signals to analyze.\n", "realistic_threshold = test_df['good_buy_prob'].quantile(0.99)\n", "print(f\"\\nA more realistic threshold (99th percentile) would be: {realistic_threshold:.4f}\")"]}], "metadata": {"kernelspec": {"display_name": "btc-quant", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}