# -*- coding: utf-8 -*-
"""
多时间框架 (MTF) 趋势跟踪策略回测框架 (V6 - 支持任意周期组合)

新功能:
- 大周期和小周期都可以从最原始的数据（如1分钟）向上合并K线，
  支持任意时间框架组合，如 4H-15T, 1H-5T 等。
"""
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
import sys
import os

warnings.simplefilter(action='ignore', category=FutureWarning)

# --- 0. 全局参数设置 ---
DATA_FILE_PATH = 'idex.csv' 
TARGET_TIMEZONE = 'Asia/Shanghai'

# --- 【核心修改】可配置的任意时间框架组合 ---
# Pandas时间频率字符串: 'T' or 'min' for minutes, 'H' for hours, 'D' for days.
# 您现在可以设置任意组合，例如:
# TREND_TIMEFRAME = '1H'
# ENTRY_TIMEFRAME = '5T'
TREND_TIMEFRAME = '15T'  # 用于判断主趋势的大周期
ENTRY_TIMEFRAME = '1T'   # 用于寻找入场点的小周期


# --- 交易和账户参数 ---
INITIAL_BALANCE = 10000.0
TRADE_AMOUNT = 100.0
SINGLE_SIDE_FEE_RATE = 0.0005

class Logger(object):
    def __init__(self, filename="Default.log"):
        self.terminal = sys.stdout
        self.log = open(filename, "w", encoding="utf-8")
    def write(self, message):
        self.terminal.write(message)
        self.log.write(message)
    def flush(self):
        pass

# --- 1. 可复用的函数 (无变化) ---
def dema(series, length):
    ema1 = series.ewm(span=length, adjust=False).mean()
    ema2 = ema1.ewm(span=length, adjust=False).mean()
    return 2 * ema1 - ema2

def calculate_indicator(df, params, timeframe_str=""):
    # ... (此函数与之前版本完全相同，为节省篇幅已折叠)
    df_copy = df.copy(); src1 = df_copy['close']
    v = (np.sign(src1.diff().fillna(0)) * df_copy['volume']).cumsum()
    smooth = v.rolling(window=params['v_len']).mean()
    v_spread = (v - smooth).rolling(window=params['window_len']).std()
    price_spread = (df_copy['high'] - df_copy['low']).rolling(window=params['window_len']).std()
    shadow = ((v - smooth) / v_spread.replace(0, np.nan).ffill() * price_spread).fillna(0)
    out = df_copy['low'] + shadow; out[shadow > 0] = df_copy['high'] + shadow
    obvema = out.ewm(span=params['len10'], adjust=False).mean()
    ma = dema(obvema, params['len'])
    slow_ma = df_copy['close'].ewm(span=params['slow_length'], adjust=False).mean()
    macd = ma - slow_ma
    def calc_slope_py(y):
        if np.all(np.isnan(y)): return np.nan
        n = len(y); x = np.arange(1, n + 1); valid_indices = ~np.isnan(y)
        y_valid, x_valid = y[valid_indices], x[valid_indices]
        if len(y_valid) < 2: return np.nan
        n_valid = len(y_valid); sumX, sumY = np.sum(x_valid), np.sum(y_valid)
        sumXSqr, sumXY = np.sum(x_valid*x_valid), np.sum(x_valid*y_valid)
        denominator = (n_valid * sumXSqr - sumX * sumX)
        if denominator == 0: return 0.0
        slope = (n_valid * sumXY - sumX * sumY) / denominator
        intercept = (sumY / n_valid) - slope * (sumX / n_valid)
        return intercept + slope * (n - params['offset'])
    tt1 = macd.rolling(window=params['len5']).apply(calc_slope_py, raw=True)
    df_copy['tt1'] = tt1; df_copy.dropna(subset=['tt1'], inplace=True)
    original_index_name = df_copy.index.name
    df_copy.reset_index(inplace=True)
    p = 1; b5 = pd.Series(np.nan, index=df_copy.index); oc = pd.Series(np.nan, index=df_copy.index)
    tt1_b5_abs_diff = pd.Series(np.nan, index=df_copy.index)
    b5.iloc[0], oc.iloc[0], tt1_b5_abs_diff.iloc[0] = df_copy['tt1'].iloc[0], 0, 0
    for i in range(1, len(df_copy)):
        current_diff = abs(df_copy['tt1'].iloc[i] - b5.iloc[i-1])
        tt1_b5_abs_diff.iloc[i] = current_diff
        a15 = tt1_b5_abs_diff.iloc[:i+1].mean() * p
        if df_copy['tt1'].iloc[i] > b5.iloc[i-1] + a15: b5.iloc[i] = df_copy['tt1'].iloc[i]
        elif df_copy['tt1'].iloc[i] < b5.iloc[i-1] - a15: b5.iloc[i] = df_copy['tt1'].iloc[i]
        else: b5.iloc[i] = b5.iloc[i-1]
        b5_change = b5.iloc[i] - b5.iloc[i-1]
        if b5_change > 0: oc.iloc[i] = 1
        elif b5_change < 0: oc.iloc[i] = -1
        else: oc.iloc[i] = oc.iloc[i-1]
    df_copy[f'oc{timeframe_str}'] = oc.values
    df_copy.set_index(original_index_name, inplace=True)
    oc_change = df_copy[f'oc{timeframe_str}'].diff()
    df_copy[f'signal{timeframe_str}'] = 0
    df_copy.loc[oc_change > 0, f'signal{timeframe_str}'] = 1
    df_copy.loc[oc_change < 0, f'signal{timeframe_str}'] = -1
    return df_copy

# --- 2. 高级回测引擎 (无变化) ---
def run_mtf_backtest_pro(df, trend_col, entry_col):
    TOTAL_FEE_RATE = 2 * SINGLE_SIDE_FEE_RATE
    balance = INITIAL_BALANCE; peak_balance = INITIAL_BALANCE; max_drawdown = 0.0
    position = 0; entry_price = 0.0; quantity = 0.0; entry_time = None
    trades = []; equity_curve = []
    win_streak = 0; loss_streak = 0; max_win_streak = 0; max_loss_streak = 0

    for i in range(1, len(df)):
        trend = df[trend_col].iloc[i-1]
        signal = df[entry_col].iloc[i-1]
        current_price = df['open'].iloc[i]
        
        final_signal = 0
        if trend == 1 and signal == 1: final_signal = 1
        elif trend == -1 and signal == -1: final_signal = -1
        elif trend == 1 and signal == -1 and position == 1: final_signal = -2
        elif trend == -1 and signal == 1 and position == -1: final_signal = 2

        def close_position(exit_price):
            nonlocal balance, peak_balance, max_drawdown, win_streak, loss_streak, max_win_streak, max_loss_streak
            if position == 1: gross_pnl = (exit_price - entry_price) * quantity
            else: gross_pnl = (entry_price - exit_price) * quantity
            entry_fee = entry_price * quantity * SINGLE_SIDE_FEE_RATE
            exit_fee = exit_price * quantity * SINGLE_SIDE_FEE_RATE
            net_pnl = gross_pnl - entry_fee - exit_fee
            
            trades.append({'entry_time': entry_time, 'exit_time': df.index[i],
                           'type': 'Long' if position == 1 else 'Short', 'net_pnl': net_pnl,
                           'entry_price': entry_price, 'exit_price': exit_price, 'quantity': quantity})
            balance += net_pnl
            if net_pnl > 0: win_streak += 1; loss_streak = 0
            else: loss_streak += 1; win_streak = 0
            max_win_streak = max(max_win_streak, win_streak)
            max_loss_streak = max(max_loss_streak, loss_streak)
            equity_curve.append(balance)
            peak_balance = max(peak_balance, balance)
            drawdown = (peak_balance - balance) / peak_balance
            max_drawdown = max(max_drawdown, drawdown)

        if (final_signal == 1 and position <= 0) or (final_signal == -1 and position >= 0):
            if position != 0: close_position(current_price)
            position = final_signal; entry_price = current_price
            quantity = TRADE_AMOUNT / entry_price; entry_time = df.index[i]
        elif (final_signal == -2 and position == 1) or (final_signal == 2 and position == -1):
            close_position(current_price); position = 0
            
    return pd.DataFrame(trades), max_drawdown, max_win_streak, max_loss_streak, balance


def main():
    base_filename = os.path.splitext(os.path.basename(DATA_FILE_PATH))[0]
    log_filename = f"{base_filename}_{TREND_TIMEFRAME}_{ENTRY_TIMEFRAME}.log"
    trades_filename = f"{base_filename}_{TREND_TIMEFRAME}_{ENTRY_TIMEFRAME}_trades.csv"
    
    sys.stdout = Logger(log_filename)
    
    print(f"--- 回测开始: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ---")
    print(f"输入数据文件: {DATA_FILE_PATH}")
    print(f"日志将输出到: {log_filename}")
    print(f"详细交易记录将保存到: {trades_filename}")
    print(f"所有时间将以 {TARGET_TIMEZONE} (UTC+8) 显示。")
    print(f"趋势判断周期: {TREND_TIMEFRAME}")
    print(f"入场信号周期: {ENTRY_TIMEFRAME}")
    print("-" * 50)
    
    try:
        df_raw = pd.read_csv(DATA_FILE_PATH)
    except FileNotFoundError:
        print(f"错误: 未找到 '{DATA_FILE_PATH}'。将使用模拟数据。")
        start_ts = int(datetime(2022, 1, 1).timestamp()); num_records = 50000
        timestamps = np.arange(start_ts, start_ts + num_records * 60, 60)
        price = 100 + np.random.randn(num_records).cumsum() * 0.1
        df_raw = pd.DataFrame({'Timestamp': timestamps, 'Open': price, 'High': price + np.random.uniform(0, 0.5, num_records), 'Low': price - np.random.uniform(0, 0.5, num_records), 'Close': price + np.random.uniform(-0.1, 0.1, num_records), 'Volume': np.random.randint(10, 1000, num_records)})
    
    df_raw['Timestamp'] = pd.to_datetime(df_raw['Timestamp'], unit='s', utc=True)
    df_raw.set_index('Timestamp', inplace=True)
    df_raw.columns = [col.lower().replace(' ', '_') for col in df_raw.columns]
    df_raw.sort_index(inplace=True); df_raw = df_raw.dropna().copy()
    
    params = {'window_len': 28, 'v_len': 14, 'len10': 1, 'len': 9, 'slow_length': 26, 'len5': 2, 'offset': 0}
    
    print("\n--- 计算指标 (在UTC时区下) ---")
    agg_rules = {'open': 'first', 'high': 'max', 'low': 'min', 'close': 'last', 'volume': 'sum'}
    
    # --- 【核心修改】大小周期都从原始数据重采样 ---
    # 1. 生成大周期(Trend)数据并计算指标
    trend_suffix = f"_{TREND_TIMEFRAME.replace('T', 'm')}" # 创建一个友好的后缀, e.g., _15m
    print(f"计算 {TREND_TIMEFRAME} 周期趋势...")
    df_trend = df_raw.resample(TREND_TIMEFRAME).agg(agg_rules).dropna()
    df_trend_indicator = calculate_indicator(df_trend, params, timeframe_str=trend_suffix)
    
    # 2. 生成小周期(Entry)数据并计算指标
    entry_suffix = f"_{ENTRY_TIMEFRAME.replace('T', 'm')}"
    print(f"计算 {ENTRY_TIMEFRAME} 周期信号...")
    df_entry = df_raw.resample(ENTRY_TIMEFRAME).agg(agg_rules).dropna()
    df_entry_indicator = calculate_indicator(df_entry, params, timeframe_str=entry_suffix)
    
    # 3. 合并数据
    print("合并数据...")
    trend_col_name = f"oc{trend_suffix}"
    entry_signal_col_name = f"signal{entry_suffix}"
    
    # merge_asof的左表(left)应该是我们最终要交易的周期，即小周期(entry)
    cols_from_entry = ['open', entry_signal_col_name]
    cols_from_trend = [trend_col_name]
    
    merged_df = pd.merge_asof(df_entry_indicator[cols_from_entry], 
                              df_trend_indicator[cols_from_trend], 
                              left_index=True, right_index=True, 
                              direction='backward')
    merged_df.dropna(inplace=True)

    print("\n--- 执行高级回测 ---")
    final_trades_df, max_dd, max_ws, max_ls, final_balance = run_mtf_backtest_pro(merged_df, trend_col_name, entry_signal_col_name)

    # --- 结果分析 ---
    print("\n" + "="*80)
    print(f"--- MTF 策略高级回测报告 (时区: {TARGET_TIMEZONE}) ---")
    print("="*80)
    if final_trades_df.empty:
        print("回测期间没有产生任何交易。")
    else:
        # ... (报告部分与之前完全相同)
        report_start_time = merged_df.index.min().tz_convert(TARGET_TIMEZONE)
        report_end_time = merged_df.index.max().tz_convert(TARGET_TIMEZONE)
        print(f"策略周期配置: 趋势={TREND_TIMEFRAME}, 入场={ENTRY_TIMEFRAME}")
        print(f"测试时间范围: {report_start_time.strftime('%Y-%m-%d %H:%M')} -> {report_end_time.strftime('%Y-%m-%d %H:%M')}")
        print(f"交易参数: 初始资金=${INITIAL_BALANCE:,.2f}, 单笔名义本金=${TRADE_AMOUNT:,.2f}")
        print(f"手续费率: 单边 {SINGLE_SIDE_FEE_RATE:.4f} (万分之{SINGLE_SIDE_FEE_RATE*10000:.0f})")
        print("\n--- 总体表现 ---")
        total_trades = len(final_trades_df); total_net_pnl = final_trades_df['net_pnl'].sum()
        total_return_pct = (final_balance - INITIAL_BALANCE) / INITIAL_BALANCE
        print(f"{'期末总资金':<25}: ${final_balance:,.2f}")
        print(f"{'总净盈亏':<25}: ${total_net_pnl:,.2f}")
        print(f"{'总回报率':<25}: {total_return_pct:.2%}")
        print(f"{'最大回撤 (Max Drawdown)':<25}: {max_dd:.2%}")
        print("\n--- 交易统计 ---")
        win_trades = final_trades_df[final_trades_df['net_pnl'] > 0]
        loss_trades = final_trades_df[final_trades_df['net_pnl'] <= 0]
        win_rate = len(win_trades) / total_trades if total_trades > 0 else 0
        avg_win_pnl = win_trades['net_pnl'].mean() if len(win_trades) > 0 else 0
        avg_loss_pnl = loss_trades['net_pnl'].mean() if len(loss_trades) > 0 else 0
        pnl_std = final_trades_df['net_pnl'].std()
        print(f"{'总交易次数':<25}: {total_trades}")
        print(f"{'胜率 (Win Rate)':<25}: {win_rate:.2%}")
        print(f"{'平均每笔盈利':<25}: ${avg_win_pnl:,.2f}")
        print(f"{'平均每笔亏损':<25}: ${avg_loss_pnl:,.2f}")
        print(f"{'盈亏金额标准差 (方差)':<25}: ${pnl_std:,.2f}")
        print(f"{'最大连续盈利次数':<25}: {max_ws}")
        print(f"{'最大连续亏损次数':<25}: {max_ls}")

        try:
            trades_to_save = final_trades_df.copy()
            trades_to_save['balance_after_trade'] = trades_to_save['net_pnl'].cumsum() + INITIAL_BALANCE
            trades_to_save['entry_time'] = trades_to_save['entry_time'].dt.tz_convert(TARGET_TIMEZONE)
            trades_to_save['exit_time'] = trades_to_save['exit_time'].dt.tz_convert(TARGET_TIMEZONE)
            trades_to_save.to_csv(trades_filename, index=False, float_format='%.6f')
            print(f"\n详细交易记录已成功保存到: {trades_filename}")
        except Exception as e:
            print(f"\n保存交易记录文件时出错: {e}")

if __name__ == '__main__':
    main()