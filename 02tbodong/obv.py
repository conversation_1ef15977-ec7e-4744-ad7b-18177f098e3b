import pandas as pd
import numpy as np
from numba import njit

# --- 0. 指标和信号计算函数 (这部分不变) ---
def ema(series, length):
    return series.ewm(span=length, adjust=False).mean()
def dema(series, length):
    ema1 = ema(series, length); ema2 = ema(ema1, length); return 2 * ema1 - ema2
def stdev(series, length):
    return series.rolling(window=length).std()
def sma(series, length):
    return series.rolling(window=length).mean()
def rsi(series, length=14):
    delta = series.diff(); gain = (delta.where(delta > 0, 0)).rolling(window=length).mean(); loss = (-delta.where(delta < 0, 0)).rolling(window=length).mean(); rs = gain / loss; return 100 - (100 / (1 + rs))

@njit
def calculate_oc_numba(tt1_values):
    n = len(tt1_values); b5 = np.full(n, np.nan); oc = np.full(n, np.nan); p = 1.0; first_valid_index = -1
    for i in range(n):
        if not np.isnan(tt1_values[i]): first_valid_index = i; b5[i] = tt1_values[i]; oc[i] = 0.0; break
    if first_valid_index == -1: return b5, oc
    cum_abs_change = 0.0
    for i in range(first_valid_index + 1, n):
        cum_abs_change += abs(tt1_values[i] - b5[i-1]); n5 = i - first_valid_index; a15_val = (cum_abs_change / n5) * p if n5 > 0 else 0
        if tt1_values[i] > b5[i-1] + a15_val: b5[i] = tt1_values[i]
        elif tt1_values[i] < b5[i-1] - a15_val: b5[i] = tt1_values[i]
        else: b5[i] = b5[i-1]
        if b5[i] > b5[i-1]: oc[i] = 1.0
        elif b5[i] < b5[i-1]: oc[i] = -1.0
        else: oc[i] = oc[i-1]
    return b5, oc

# --- 修改：为单个周期计算指标，并返回 触发信号(signal) 和 趋势状态(oc) ---
def calculate_signals_for_timeframe(df_tf, params):
    df = df_tf.copy()
    # 指标计算... (与之前相同)
    df['price_spread'] = stdev(df['High'] - df['Low'], length=params['window_len']); v = (np.sign(df['Close'].diff()) * df['Volume']).fillna(0).cumsum(); df['v'] = v; df['smooth'] = sma(df['v'], length=params['v_len']); df['v_spread'] = stdev(df['v'] - df['smooth'], length=params['window_len']); df['shadow'] = (df['v'] - df['smooth']) / df['v_spread'] * df['price_spread']; df['out'] = np.where(df['shadow'] > 0, df['High'] + df['shadow'], df['Low'] + df['shadow'])
    if params['obv_len'] == 1: df['obvema'] = df['out']
    else: df['obvema'] = ema(df['out'], length=params['obv_len'])
    df['ma'] = dema(df['obvema'], length=params['ma_len']); df['slow_ma'] = ema(df['Close'], length=params['slow_length']); df['macd_custom'] = df['ma'] - df['slow_ma']; df['tt1'] = 2 * df['macd_custom'] - df['macd_custom'].shift(1)
    _, oc_vals = calculate_oc_numba(df['tt1'].to_numpy()); df['oc'] = oc_vals
    df['rsi'] = rsi(df['Close'], length=params['rsi_len'])
    
    # 生成交易信号 (Trigger)
    prev_oc = df['oc'].shift(1); prev_rsi = df['rsi'].shift(1)
    buy_cond = (prev_oc == -1) & (df['oc'] == 1) & (prev_rsi <= params['rsi_os']) & (df['rsi'] > params['rsi_os'])
    sell_cond = (prev_oc == 1) & (df['oc'] == -1) & (prev_rsi >= params['rsi_ob']) & (df['rsi'] < params['rsi_ob'])
    df['signal'] = np.select([buy_cond, sell_cond], [1, -1], default=0)
    
    # 返回 触发信号(signal) 和 趋势状态(oc)
    return df[['signal', 'oc']]

# --- 1. 参数配置与数据加载 ---
params = {'window_len': 28, 'v_len': 14, 'obv_len': 1, 'ma_len': 9, 'slow_length': 26, 'rsi_len': 14, 'rsi_ob': 70, 'rsi_os': 30}
timeframes = ['5min', '15min', '1h'] 
base_tf = timeframes[0] # 定义基础周期

try: 
    df_base = pd.read_csv("aave.csv")
except FileNotFoundError: print("错误：请将您的数据保存为 'sui.csv' 文件。"); exit()

# 数据预处理
for col in ['Open', 'High', 'Low', 'Close', 'Volume']: df_base[col] = pd.to_numeric(df_base[col])
if df_base['Timestamp'].max() > 10**12: df_base['Timestamp'] = df_base['Timestamp'] // 1000
df_base['datetime'] = pd.to_datetime(df_base['Timestamp'], unit='s'); df_base.set_index('datetime', inplace=True)

# --- 2. 多周期信号计算与合并 (逻辑修改) ---
df_final = df_base.copy()
for tf in timeframes:
    print(f"正在计算 {tf} 周期的信号和状态...")
    if tf == base_tf: df_tf = df_base.copy()
    else: agg_rules = {'Open': 'first', 'High': 'max', 'Low': 'min', 'Close': 'last', 'Volume': 'sum'}; df_tf = df_base.resample(tf, label='right', closed='right').agg(agg_rules).dropna()
    
    # 获取该周期的信号和状态
    tf_results = calculate_signals_for_timeframe(df_tf, params)
    
    # 重命名列以区分不同周期
    tf_results.columns = [f'signal_{tf}', f'oc_{tf}']
    
    # 合并回主DataFrame
    df_final = df_final.join(tf_results)

# 向前填充所有周期的信号和状态
for tf in timeframes:
    for col_prefix in ['signal', 'oc']:
        col_name = f"{col_prefix}_{tf}"
        if col_name in df_final.columns:
            df_final[col_name] = df_final[col_name].ffill().fillna(0).astype(int)

# --- 3. 多周期共振回测引擎 (逻辑修改) ---
initial_capital = 10000.0; capital = initial_capital; position = 0; entry_price = 0; trades = []; fee_rate = 0.001
print("\n开始进行多周期共振回测 (小周期触发, 大周期过滤)...")
df_final.reset_index(inplace=True)

for i in range(1, len(df_final)):
    if i + 1 >= len(df_final): continue
    
    # --- 新的共振信号判断 ---
    # 1. 基础周期必须在当前K线产生一个买入/卖出“触发”信号
    buy_trigger = df_final.loc[i, f'signal_{base_tf}'] == 1
    sell_trigger = df_final.loc[i, f'signal_{base_tf}'] == -1
    
    # 2. 所有周期（包括基础周期）的“趋势状态”必须是看涨/看跌
    is_bullish_state = all(df_final.loc[i, f'oc_{tf}'] == 1 for tf in timeframes)
    is_bearish_state = all(df_final.loc[i, f'oc_{tf}'] == -1 for tf in timeframes)

    # 最终的买卖信号
    final_buy_signal = buy_trigger and is_bullish_state
    final_sell_signal = sell_trigger and is_bearish_state

    # 交易价格
    trade_price = df_final.loc[i + 1, 'Open']
    
    # --- 执行交易 ---
    if position == 0 and final_buy_signal:
        position = 1; entry_price = trade_price; entry_time = df_final.loc[i + 1, 'datetime']
        print(f"{entry_time} | 信号: 多周期共振买入 | 价格: {entry_price:.6f}")
        
    elif position == 1 and final_sell_signal:
        position = 0; exit_price = trade_price; exit_time = df_final.loc[i + 1, 'datetime']
        profit_pct = (exit_price - entry_price) / entry_price - 2 * fee_rate
        trades.append({'entry_time': entry_time, 'exit_time': exit_time, 'entry_price': entry_price, 'exit_price': exit_price, 'profit_pct': profit_pct})
        capital *= (1 + profit_pct)
        print(f"{exit_time} | 信号: 多周期共振卖出 | 价格: {exit_price:.6f} | 收益率: {profit_pct:.2%} | 当前资金: {capital:.2f}")
        entry_price = 0

# --- 4. 性能报告 (这部分不变) ---
print("\n--- 回测性能报告 ---")
if not trades:
    print("回测期间没有发生任何交易。")
else:
    trades_df = pd.DataFrame(trades)
    total_trades = len(trades_df); winning_trades = trades_df[trades_df['profit_pct'] > 0]; win_rate = len(winning_trades) / total_trades if total_trades > 0 else 0; avg_win_pct = winning_trades['profit_pct'].mean(); avg_loss_pct = trades_df[trades_df['profit_pct'] <= 0]['profit_pct'].mean()
    if avg_loss_pct is not None and not np.isnan(avg_loss_pct) and abs(avg_loss_pct) > 0: profit_factor_ratio = abs(avg_win_pct / avg_loss_pct)
    else: profit_factor_ratio = float('inf')
    total_return = (capital / initial_capital) - 1
    print(f"回测时间范围: {df_final['datetime'].iloc[0]} 至 {df_final['datetime'].iloc[-1]}"); print(f"分析周期: {', '.join(timeframes)}"); print(f"初始资金: {initial_capital:.2f}"); print(f"最终资金: {capital:.2f}"); print(f"总收益率: {total_return:.2%}")
    print("-" * 25); print(f"总交易次数: {total_trades}"); print(f"胜率: {win_rate:.2%}"); print(f"平均盈利: {avg_win_pct:.2%}" if pd.notna(avg_win_pct) else "平均盈利: N/A"); print(f"平均亏损: {avg_loss_pct:.2%}" if pd.notna(avg_loss_pct) else "平均亏损: N/A"); print(f"盈亏比: {profit_factor_ratio:.2f}" if profit_factor_ratio != float('inf') else "盈亏比: ∞")
    print("\n--- 交易记录 (前5笔) ---"); print(trades_df.head().to_string())