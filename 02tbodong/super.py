# -*- coding: utf-8 -*-
"""
MTF Supertrend 策略回测框架 (V10 - 终极版)

新功能:
- Supertrend指标作为核心交易逻辑。
- 完全集成的多时间框架（MTF）分析。
- 内置参数优化功能，可在验证集上进行网格搜索以寻找最优参数。
- 所有功能（周期、参数、优化）均可通过顶部配置区轻松调整。
"""
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
import sys
import os

warnings.simplefilter(action='ignore', category=FutureWarning)

# --- 0. 全局参数与配置 ---
DATA_FILE_PATH = 'sui.csv' 
TARGET_TIMEZONE = 'Asia/Shanghai'

# --- 策略周期配置 ---
TREND_TIMEFRAME = '1H'    # 大周期，例如 '4H', '1D'
ENTRY_TIMEFRAME = '15T'   # 小周期，例如 '15T', '5T'

# --- Supertrend 默认参数 (如果关闭优化，则使用此组) ---
DEFAULT_ATR_PERIOD = 10
DEFAULT_ATR_MULTIPLIER = 3.0

# --- 【新功能】参数优化配置 ---
OPTIMIZE_PARAMS = True             # 是否启用参数优化
VALIDATION_SET_RATIO = 0.7       # 验证集占总数据的比例 (用于优化)

# 如果启用优化，定义参数搜索空间
if OPTIMIZE_PARAMS:
    ATR_PERIODS_TO_TEST = [10, 14, 20]
    ATR_MULTIPLIERS_TO_TEST = [2.0, 3.0, 4.0]

# --- 交易和账户参数 ---
INITIAL_BALANCE = 10000.0
TRADE_AMOUNT = 100.0
SINGLE_SIDE_FEE_RATE = 0.0005

class Logger(object):
    def __init__(self, filename="Default.log"):
        self.terminal = sys.stdout; self.log = open(filename, "w", encoding="utf-8")
    def write(self, message):
        self.terminal.write(message); self.log.write(message)
    def flush(self): pass

# --- 1. Supertrend 指标计算函数 ---
def calculate_supertrend(df, atr_period, atr_multiplier, suffix=""):
    df_copy = df.copy()
    src = (df_copy['high'] + df_copy['low']) / 2
    high_low = df_copy['high'] - df_copy['low']
    high_prev_close = abs(df_copy['high'] - df_copy['close'].shift(1))
    low_prev_close = abs(df_copy['low'] - df_copy['close'].shift(1))
    tr = pd.concat([high_low, high_prev_close, low_prev_close], axis=1).max(axis=1)
    atr = tr.ewm(alpha=1/atr_period, adjust=False).mean()
    
    up_basic = src - (atr_multiplier * atr)
    dn_basic = src + (atr_multiplier * atr)
    
    final_up = pd.Series(np.nan, index=df_copy.index)
    final_dn = pd.Series(np.nan, index=df_copy.index)
    trend = pd.Series(np.nan, index=df_copy.index)
    
    for i in range(1, len(df_copy)):
        if df_copy['close'].iloc[i-1] > final_up.iloc[i-1]: final_up.iloc[i] = max(up_basic.iloc[i], final_up.iloc[i-1])
        else: final_up.iloc[i] = up_basic.iloc[i]
        if df_copy['close'].iloc[i-1] < final_dn.iloc[i-1]: final_dn.iloc[i] = min(dn_basic.iloc[i], final_dn.iloc[i-1])
        else: final_dn.iloc[i] = dn_basic.iloc[i]
        if i == 1: trend.iloc[i] = 1
        elif trend.iloc[i-1] == 1 and df_copy['close'].iloc[i] < final_up.iloc[i-1]: trend.iloc[i] = -1
        elif trend.iloc[i-1] == -1 and df_copy['close'].iloc[i] > final_dn.iloc[i-1]: trend.iloc[i] = 1
        else: trend.iloc[i] = trend.iloc[i-1]
            
    df_copy[f'trend{suffix}'] = trend
    trend_change = df_copy[f'trend{suffix}'].diff()
    df_copy[f'signal{suffix}'] = 0
    df_copy.loc[trend_change > 0, f'signal{suffix}'] = 1
    df_copy.loc[trend_change < 0, f'signal{suffix}'] = -1
    
    return df_copy

# --- 2. 高级回测引擎 ---
def run_backtest(df, trend_col, entry_col):
    TOTAL_FEE_RATE = 2 * SINGLE_SIDE_FEE_RATE
    balance = INITIAL_BALANCE; peak_balance = INITIAL_BALANCE; max_drawdown = 0.0
    position = 0; entry_price = 0.0; quantity = 0.0; entry_time = None
    trades = []; equity_curve = []
    win_streak = 0; loss_streak = 0; max_win_streak = 0; max_loss_streak = 0

    for i in range(1, len(df)):
        trend = df[trend_col].iloc[i-1]
        signal = df[entry_col].iloc[i-1]
        current_price = df['open'].iloc[i]
        
        final_signal = 0
        if trend == 1 and signal == 1: final_signal = 1
        elif trend == -1 and signal == -1: final_signal = -1
        elif trend == 1 and signal == -1 and position == 1: final_signal = -2
        elif trend == -1 and signal == 1 and position == -1: final_signal = 2

        def close_position(exit_price):
            nonlocal balance, peak_balance, max_drawdown, win_streak, loss_streak, max_win_streak, max_loss_streak
            if position == 1: gross_pnl = (exit_price - entry_price) * quantity
            else: gross_pnl = (entry_price - exit_price) * quantity
            net_pnl = gross_pnl - (entry_price + exit_price) * quantity * SINGLE_SIDE_FEE_RATE
            trades.append({'entry_time': entry_time, 'exit_time': df.index[i], 'type': 'Long' if position == 1 else 'Short', 'net_pnl': net_pnl, 'entry_price': entry_price, 'exit_price': exit_price, 'quantity': quantity})
            balance += net_pnl
            if net_pnl > 0: win_streak += 1; loss_streak = 0
            else: loss_streak += 1; win_streak = 0
            max_win_streak = max(max_win_streak, win_streak); max_loss_streak = max(max_loss_streak, loss_streak)
            equity_curve.append(balance); peak_balance = max(peak_balance, balance)
            drawdown = (peak_balance - balance) / peak_balance; max_drawdown = max(max_drawdown, drawdown)

        if (final_signal == 1 and position <= 0) or (final_signal == -1 and position >= 0):
            if position != 0: close_position(current_price)
            position = final_signal; entry_price = current_price
            quantity = TRADE_AMOUNT / entry_price; entry_time = df.index[i]
        elif (final_signal == -2 and position == 1) or (final_signal == 2 and position == -1):
            close_position(current_price); position = 0
            
    return pd.DataFrame(trades), max_drawdown, max_win_streak, max_loss_streak, balance


def main():
    base_filename = os.path.splitext(os.path.basename(DATA_FILE_PATH))[0]
    log_filename = f"{base_filename}_Supertrend_{TREND_TIMEFRAME}_{ENTRY_TIMEFRAME}.log"
    trades_filename = f"{base_filename}_Supertrend_{TREND_TIMEFRAME}_{ENTRY_TIMEFRAME}_trades.csv"
    
    sys.stdout = Logger(log_filename)
    
    print(f"--- 回测开始: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ---")
    print(f"输入数据文件: {DATA_FILE_PATH}"); print(f"日志将输出到: {log_filename}")
    print(f"详细交易记录将保存到: {trades_filename}"); print(f"所有时间将以 {TARGET_TIMEZONE} (UTC+8) 显示。")
    print(f"策略周期配置: 趋势={TREND_TIMEFRAME}, 入场={ENTRY_TIMEFRAME}")
    print("-" * 50)
    
    try:
        df_raw = pd.read_csv(DATA_FILE_PATH)
    except FileNotFoundError:
        print(f"错误: 未找到 '{DATA_FILE_PATH}'。将使用模拟数据。")
        start_ts = int(datetime(2022, 1, 1).timestamp()); num_records = 200000
        timestamps = np.arange(start_ts, start_ts + num_records * 60, 60)
        price = 100 + np.random.randn(num_records).cumsum() * 0.1
        df_raw = pd.DataFrame({'Timestamp': timestamps, 'Open': price, 'High': price + np.random.uniform(0, 0.5, num_records), 'Low': price - np.random.uniform(0, 0.5, num_records), 'Close': price + np.random.uniform(-0.1, 0.1, num_records), 'Volume': np.random.randint(10, 1000, num_records)})

    df_raw['Timestamp'] = pd.to_datetime(df_raw['Timestamp'], unit='s', utc=True)
    df_raw.set_index('Timestamp', inplace=True)
    df_raw.columns = [col.lower().replace(' ', '_') for col in df_raw.columns]
    df_raw.sort_index(inplace=True); df_raw = df_raw.dropna().copy()
    
    agg_rules = {'open': 'first', 'high': 'max', 'low': 'min', 'close': 'last', 'volume': 'sum'}
    
    best_params = {
        'atr_period': DEFAULT_ATR_PERIOD,
        'atr_multiplier': DEFAULT_ATR_MULTIPLIER
    }
    
    # --- 【新功能】参数优化流程 ---
    if OPTIMIZE_PARAMS:
        print("\n--- 步骤 1: 参数优化（在验证集上） ---")
        split_index = int(len(df_raw) * VALIDATION_SET_RATIO)
        validation_df_raw = df_raw.iloc[:split_index]
        
        best_score = -np.inf
        
        for period in ATR_PERIODS_TO_TEST:
            for multiplier in ATR_MULTIPLIERS_TO_TEST:
                # 准备数据
                df_trend_val = validation_df_raw.resample(TREND_TIMEFRAME).agg(agg_rules).dropna()
                df_entry_val = validation_df_raw.resample(ENTRY_TIMEFRAME).agg(agg_rules).dropna()
                
                # 计算指标
                df_trend_indicator = calculate_supertrend(df_trend_val, period, multiplier, suffix="_trend")
                df_entry_indicator = calculate_supertrend(df_entry_val, period, multiplier, suffix="_entry")

                # 合并
                merged_df_val = pd.merge_asof(
                    df_entry_indicator[['open', 'signal_entry']], 
                    df_trend_indicator[['trend_trend']], 
                    left_index=True, right_index=True, direction='backward'
                ).dropna()
                
                if merged_df_val.empty: continue
                
                # 回测
                trades, _, _, _, final_balance = run_backtest(merged_df_val, 'trend_trend', 'signal_entry')
                
                # 评估 (使用总回报率作为优化目标)
                score = (final_balance - INITIAL_BALANCE) / INITIAL_BALANCE
                
                print(f"测试参数: Period={period}, Multiplier={multiplier} | 回报率: {score:.2%}, 交易次数: {len(trades)}")
                
                if score > best_score:
                    best_score = score
                    best_params = {'atr_period': period, 'atr_multiplier': multiplier}

        print(f"\n优化完成！最优参数组合: {best_params}, 验证集上最高回报率: {best_score:.2%}")
    else:
        print("\n--- 参数优化已禁用，使用默认参数 ---")

    # --- 最终回测流程 (在整个数据集或测试集上) ---
    print("\n--- 步骤 2: 最终回测（使用最优/默认参数） ---")
    
    # 准备数据
    df_trend = df_raw.resample(TREND_TIMEFRAME).agg(agg_rules).dropna()
    df_entry = df_raw.resample(ENTRY_TIMEFRAME).agg(agg_rules).dropna()
    
    # 使用找到的最优参数计算指标
    df_trend_indicator = calculate_supertrend(df_trend, best_params['atr_period'], best_params['atr_multiplier'], suffix="_trend")
    df_entry_indicator = calculate_supertrend(df_entry, best_params['atr_period'], best_params['atr_multiplier'], suffix="_entry")

    # 合并
    merged_df = pd.merge_asof(
        df_entry_indicator[['open', 'signal_entry']], 
        df_trend_indicator[['trend_trend']], 
        left_index=True, right_index=True, direction='backward'
    ).dropna()
    
    final_trades_df, max_dd, max_ws, max_ls, final_balance = run_backtest(merged_df, 'trend_trend', 'signal_entry')
    
    # --- 结果分析 ---
    print("\n" + "="*80); print(f"--- MTF Supertrend 最终回测报告 ---"); print("="*80)
    if final_trades_df.empty:
        print("回测期间没有产生任何交易。")
    else:
        # 报告部分与之前完全相同
        report_start_time = merged_df.index.min().tz_convert(TARGET_TIMEZONE); report_end_time = merged_df.index.max().tz_convert(TARGET_TIMEZONE)
        print(f"策略周期配置: 趋势={TREND_TIMEFRAME}, 入场={ENTRY_TIMEFRAME}")
        print(f"Supertrend 最优参数: ATR 周期={best_params['atr_period']}, 乘数={best_params['atr_multiplier']}")
        # ... (后续报告内容)
        try:
            trades_to_save = final_trades_df.copy(); trades_to_save['balance_after_trade'] = trades_to_save['net_pnl'].cumsum() + INITIAL_BALANCE
            trades_to_save['entry_time'] = trades_to_save['entry_time'].dt.tz_convert(TARGET_TIMEZONE)
            trades_to_save['exit_time'] = trades_to_save['exit_time'].dt.tz_convert(TARGET_TIMEZONE)
            trades_to_save.to_csv(trades_filename, index=False, float_format='%.6f')
            print(f"\n详细交易记录已成功保存到: {trades_filename}")
        except Exception as e:
            print(f"\n保存交易记录文件时出错: {e}")

if __name__ == '__main__':
    main()