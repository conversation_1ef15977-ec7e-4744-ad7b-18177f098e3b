#!/usr/bin/env python3
"""
快速获取BTC历史数据脚本
专门用于获取大量历史K线数据，支持分批获取
"""

import requests
import pandas as pd
import time
from datetime import datetime, timezone
import argparse
import sys

def format_timestamp(timestamp):
    """格式化时间戳为北京时间"""
    dt = datetime.fromtimestamp(timestamp, tz=timezone.utc)
    beijing_dt = dt.astimezone(timezone.utc).replace(tzinfo=None) + pd.Timedelta(hours=8)
    return beijing_dt.strftime('%Y-%m-%d %H:%M:%S')

def fetch_klines(symbol, interval, limit=1000, end_time=None):
    """获取单批K线数据"""
    try:
        url = "https://api.binance.com/api/v3/klines"
        params = {
            'symbol': symbol,
            'interval': interval,
            'limit': limit
        }
        
        if end_time:
            params['endTime'] = end_time
        
        response = requests.get(url, params=params, timeout=30)
        response.raise_for_status()
        
        return response.json()
        
    except Exception as e:
        print(f"❌ 获取K线数据失败: {e}")
        return None

def get_historical_data(symbol='BTCUSDT', interval='1m', limit=10000, output_file='btcusd_1-min_data.csv'):
    """获取历史数据（支持大量数据分批获取）"""
    try:
        print(f"📚 开始获取历史数据")
        print(f"📊 交易对: {symbol}")
        print(f"⏱️  间隔: {interval}")
        print(f"🎯 目标: {limit} 条记录")
        print(f"📁 输出: {output_file}")
        print("-" * 50)
        
        # Binance API单次最大限制通常是1000
        max_limit_per_request = 1000
        all_data = []
        
        if limit <= max_limit_per_request:
            # 单次请求就够了
            print(f"🔄 单次获取 {limit} 条记录...")
            klines = fetch_klines(symbol, interval, limit=limit)
            if klines:
                all_data.extend(klines)
        else:
            # 需要分批获取
            remaining = limit
            end_time = None  # 从最新开始
            batch_count = 0
            
            while remaining > 0 and batch_count < 100:  # 最多100批
                batch_count += 1
                current_limit = min(remaining, max_limit_per_request)
                
                print(f"🔄 第{batch_count}批: 获取 {current_limit} 条记录... ", end="", flush=True)
                
                klines = fetch_klines(symbol, interval, limit=current_limit, end_time=end_time)
                if not klines:
                    print("失败❌")
                    break
                
                # 插入到开头（因为我们是从最新往前获取）
                all_data = klines + all_data
                remaining -= len(klines)
                
                # 设置下一批的结束时间为当前批最早的时间
                end_time = int(klines[0][0])  # 第一条记录的开始时间
                
                print(f"完成✅ (总计: {len(all_data)} 条)")
                
                # 避免请求过快
                time.sleep(0.1)
                
                # 如果获取的数据少于请求的数量，说明已经到了最早的数据
                if len(klines) < current_limit:
                    print(f"📅 已获取到最早的数据，停止获取")
                    break
        
        if not all_data:
            print("❌ 没有获取到任何数据")
            return False
        
        print(f"\n📊 数据处理中...")
        
        # 转换数据格式
        data_list = []
        for kline in all_data:
            data = {
                'Timestamp': int(kline[0]) // 1000,
                'Open': float(kline[1]),
                'High': float(kline[2]),
                'Low': float(kline[3]),
                'Close': float(kline[4]),
                'Volume': float(kline[5]),
                'CloseTime': int(kline[6]) // 1000,
                'QuoteVolume': float(kline[7]),
                'TradeCount': int(kline[8]),
                'TakerBuyBaseVolume': float(kline[9]),
                'TakerBuyQuoteVolume': float(kline[10])
            }
            data_list.append(data)
        
        # 按时间排序（确保从旧到新）
        data_list.sort(key=lambda x: x['Timestamp'])
        
        # 去重（基于时间戳）
        seen_timestamps = set()
        unique_data = []
        for data in data_list:
            if data['Timestamp'] not in seen_timestamps:
                seen_timestamps.add(data['Timestamp'])
                unique_data.append(data)
        
        print(f"📊 去重后数据: {len(unique_data)} 条记录")
        
        # 保存历史数据
        df = pd.DataFrame(unique_data)
        df.to_csv(output_file, index=False)
        
        print(f"\n✅ 历史数据已保存: {len(unique_data)} 条记录")
        print(f"📁 文件: {output_file}")
        print(f"📅 时间范围: {format_timestamp(unique_data[0]['Timestamp'])} 到 {format_timestamp(unique_data[-1]['Timestamp'])}")
        
        # 计算时间跨度
        time_span_hours = (unique_data[-1]['Timestamp'] - unique_data[0]['Timestamp']) / 3600
        print(f"⏰ 时间跨度: {time_span_hours:.1f} 小时 ({time_span_hours/24:.1f} 天)")
        
        return True
        
    except KeyboardInterrupt:
        print(f"\n📋 用户中断，正在退出...")
        return False
    except Exception as e:
        print(f"❌ 获取历史数据失败: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="快速获取BTC历史数据")
    parser.add_argument("--symbol", default="BTCUSDT", help="交易对符号 (默认: BTCUSDT)")
    parser.add_argument("--interval", default="1m", help="K线间隔 (默认: 1m)")
    parser.add_argument("--limit", type=int, default=10000, help="获取数据条数 (默认: 10000)")
    parser.add_argument("--output", default="btcusd_1-min_data.csv", help="输出文件名 (默认: btcusd_1-min_data.csv)")
    
    args = parser.parse_args()
    
    # 检查网络连接
    print("🔍 检查网络连接...")
    try:
        response = requests.get("https://api.binance.com/api/v3/time", timeout=10)
        response.raise_for_status()
        server_time = response.json()['serverTime']
        print(f"✅ 连接正常，服务器时间: {format_timestamp(server_time/1000)}")
    except Exception as e:
        print(f"❌ 无法连接到Binance API: {e}")
        print("请检查网络连接")
        return
    
    # 获取历史数据
    success = get_historical_data(
        symbol=args.symbol,
        interval=args.interval,
        limit=args.limit,
        output_file=args.output
    )
    
    if success:
        print(f"\n🎉 数据获取完成！")
        print(f"💡 可以使用以下命令运行回测:")
        print(f"   python backtest_percentage_v4.py {args.output} --start-row 1000")
    else:
        print(f"\n❌ 数据获取失败")
        sys.exit(1)

if __name__ == "__main__":
    main()

# 使用示例：
#
# 获取10000条BTC 1分钟数据：
# python get_btc_history.py --limit 10000
#
# 获取50000条数据（约35天）：
# python get_btc_history.py --limit 50000 --output btc_50k.csv
#
# 获取ETH数据：
# python get_btc_history.py --symbol ETHUSDT --output eth_data.csv
#
# 获取5分钟K线：
# python get_btc_history.py --interval 5m --limit 10000
