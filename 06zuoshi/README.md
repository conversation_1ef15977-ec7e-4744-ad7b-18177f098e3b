# ZKJUSDT魔改做市商策略

## 🚀 快速开始

### 1. 查看策略文档
```bash
cat /home/<USER>/output/zkj_strategy_doc.md
```

### 2. 运行策略
```bash
python /home/<USER>/output/zkj_startup.py
```

### 3. 回测验证
```bash
python /home/<USER>/output/zkj_backtest.py
```

## 📁 文件说明

- `zkj_config.json` - 策略配置文件
- `zkj_strategy_doc.md` - 详细策略文档
- `zkj_startup.py` - 快速启动脚本
- `zkj_backtest.py` - 回测验证脚本
- `zkj_trading.log` - 交易日志文件

## ⚠️ 重要提示

1. **高风险投资** - 月化100%目标伴随极高风险
2. **模拟环境** - 默认使用模拟交易环境
3. **小额测试** - 建议先用小额资金测试
4. **风险自负** - 使用者承担所有交易风险

## 🎯 策略特点

- 价格事件驱动交易
- 多层神经网络决策
- 激进风险管理
- 高频做市策略
- 实时PnL跟踪

## 📊 目标表现

- **初始资金**: 200 USDT
- **目标收益**: 月化100%
- **最大杠杆**: 10倍
- **最大回撤**: 5%
