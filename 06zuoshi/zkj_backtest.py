#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ZKJUSDT策略回测验证脚本
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import json
from datetime import datetime, timedelta

def generate_zkj_mock_data(days=30):
    """生成ZKJUSDT模拟历史数据"""
    np.random.seed(42)

    # 基础参数
    initial_price = 0.18
    volatility = 0.15  # 15%日波动率
    trend = 0.001      # 轻微上涨趋势

    # 生成时间序列
    timestamps = pd.date_range(
        start=datetime.now() - timedelta(days=days),
        end=datetime.now(),
        freq='1min'
    )

    # 生成价格数据
    returns = np.random.normal(trend/1440, volatility/np.sqrt(1440), len(timestamps))
    prices = [initial_price]

    for ret in returns[1:]:
        new_price = prices[-1] * (1 + ret)
        # 添加一些极端波动
        if np.random.random() < 0.001:  # 0.1%概率极端波动
            new_price *= np.random.choice([0.9, 1.1])  # ±10%跳跃
        prices.append(max(0.01, new_price))  # 价格不能为负

    # 生成成交量
    base_volume = 100000
    volume = np.random.lognormal(np.log(base_volume), 0.5, len(timestamps))

    # 创建DataFrame
    data = pd.DataFrame({
        'timestamp': timestamps,
        'price': prices,
        'volume': volume
    })

    return data

def simple_backtest(data, config):
    """简单回测"""
    initial_balance = config['initial_balance']
    balance = initial_balance
    position = 0
    trades = []

    price_threshold = config['price_threshold']
    spread_multiplier = config['spread_multiplier']

    for i in range(1, len(data)):
        current_price = data.iloc[i]['price']
        prev_price = data.iloc[i-1]['price']

        # 价格变化检测
        price_change = abs(current_price - prev_price) / prev_price

        if price_change > price_threshold:
            # 模拟做市商交易
            spread = current_price * 0.001 * spread_multiplier
            buy_price = current_price - spread/2
            sell_price = current_price + spread/2

            # 简单的盈利模拟
            if np.random.random() > 0.4:  # 60%成功率
                trade_size = min(balance * 0.1, 50)  # 10%仓位或50USDT
                profit = trade_size * spread / current_price

                balance += profit
                trades.append({
                    'timestamp': data.iloc[i]['timestamp'],
                    'price': current_price,
                    'profit': profit,
                    'balance': balance
                })

    return trades, balance

def run_backtest():
    """运行回测"""
    print("🔄 生成ZKJUSDT模拟数据...")
    data = generate_zkj_mock_data(30)

    print("📊 加载策略配置...")
    try:
        with open('zkj_config.json', 'r') as f:
            config = json.load(f)
    except:
        print("❌ 配置文件未找到，使用默认配置")
        config = {
            'initial_balance': 200,
            'price_threshold': 0.001,
            'spread_multiplier': 2.0
        }

    print("⚡ 执行回测...")
    trades, final_balance = simple_backtest(data, config)

    # 计算结果
    initial_balance = config['initial_balance']
    total_return = (final_balance - initial_balance) / initial_balance
    total_trades = len(trades)

    print("\n📈 回测结果:")
    print("=" * 40)
    print(f"初始资金: {initial_balance:.2f} USDT")
    print(f"最终资金: {final_balance:.2f} USDT")
    print(f"总收益: {final_balance - initial_balance:.2f} USDT")
    print(f"收益率: {total_return*100:.2f}%")
    print(f"交易次数: {total_trades}")
    print(f"平均每笔收益: {(final_balance - initial_balance)/max(total_trades, 1):.4f} USDT")

    if total_return >= 1.0:
        print("🎉 达到月化100%目标！")
    else:
        print(f"📊 距离目标还需: {(1.0 - total_return)*100:.1f}%")

    # 保存回测结果
    if trades:
        trades_df = pd.DataFrame(trades)
        trades_df.to_csv('zkj_backtest_results.csv', index=False)
        print("\n💾 回测结果已保存到: zkj_backtest_results.csv")

if __name__ == "__main__":
    run_backtest()
