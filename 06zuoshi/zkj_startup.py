#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ZKJUSDT魔改做市商策略 - 快速启动脚本
目标：200 USDT -> 400 USDT (月化100%)
"""

import asyncio
import json
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def load_config():
    """加载策略配置"""
    try:
        with open('zkj_config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ 配置文件未找到，请先运行完整策略代码")
        return None

async def run_zkj_strategy():
    """运行ZKJUSDT策略"""
    print("🚀 ZKJUSDT魔改做市商策略启动")
    print("=" * 50)

    # 加载配置
    config = load_config()
    if not config:
        return

    print(f"📊 策略配置:")
    print(f"   交易对: {config['symbol']}")
    print(f"   初始资金: {config['initial_balance']} USDT")
    print(f"   目标收益: {config['target_monthly_return']*100}%/月")
    print(f"   最大杠杆: {config['max_leverage']}倍")
    print(f"   最大回撤: {config['max_drawdown']*100}%")
    print("=" * 50)

    # 风险提示
    print("⚠️  风险提示:")
    print("   1. 高杠杆高风险，可能导致快速亏损")
    print("   2. 加密货币市场波动极大")
    print("   3. 建议先用小额资金测试")
    print("   4. 请确保已充分理解策略风险")
    print("=" * 50)

    # 用户确认
    confirm = input("是否继续运行策略？(y/N): ")
    if confirm.lower() != 'y':
        print("👋 策略取消运行")
        return

    # API配置提示
    print("\n🔑 API配置:")
    print("   当前使用模拟模式")
    print("   如需实盘交易，请配置真实API密钥")

    use_real_api = input("是否使用真实API？(y/N): ")

    api_key = None
    api_secret = None

    if use_real_api.lower() == 'y':
        api_key = input("请输入Binance API Key: ").strip()
        api_secret = input("请输入Binance API Secret: ").strip()

        if not api_key or not api_secret:
            print("❌ API密钥不能为空，使用模拟模式")
            api_key = api_secret = None

    print("\n🎯 策略开始运行...")
    print("   按 Ctrl+C 可随时停止策略")
    print("=" * 50)

    try:
        # 这里需要导入实际的策略类
        # 由于在脚本中，需要重新定义或导入
        print("📝 注意：请确保已运行完整策略代码以创建ZKJMarketMaker类")
        print("🔄 策略运行中... (模拟模式)")

        # 模拟运行状态
        import time
        start_time = time.time()
        cycle = 0

        while True:
            cycle += 1
            elapsed = time.time() - start_time

            # 模拟策略状态
            if cycle % 10 == 0:
                print(f"📊 周期 {cycle} | 运行时间: {elapsed:.1f}s | 状态: 正常")

            await asyncio.sleep(1)

            # 演示模式：运行60秒后停止
            if elapsed > 60:
                print("⏰ 演示时间结束")
                break

    except KeyboardInterrupt:
        print("\n👋 用户中断，策略停止")
    except Exception as e:
        print(f"❌ 策略运行错误: {e}")

    print("\n🛑 策略已停止")
    print("📊 查看详细日志: /home/<USER>/output/zkj_trading.log")

def show_strategy_info():
    """显示策略信息"""
    print("🎯 ZKJUSDT魔改做市商策略")
    print("=" * 50)
    print("📊 策略特点:")
    print("   • 价格事件驱动交易")
    print("   • 多层神经网络决策")
    print("   • 激进风险管理")
    print("   • 高频做市策略")
    print()
    print("🎯 目标收益: 月化100%")
    print("💰 初始资金: 200 USDT")
    print("⚡ 最大杠杆: 10倍")
    print("⚠️  最大回撤: 5%")
    print("=" * 50)

def main():
    """主函数"""
    show_strategy_info()

    print("\n选择操作:")
    print("1. 运行策略")
    print("2. 查看配置")
    print("3. 退出")

    choice = input("\n请选择 (1-3): ").strip()

    if choice == '1':
        asyncio.run(run_zkj_strategy())
    elif choice == '2':
        config = load_config()
        if config:
            print("\n📋 当前配置:")
            for key, value in config.items():
                print(f"   {key}: {value}")
    elif choice == '3':
        print("👋 再见！")
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
