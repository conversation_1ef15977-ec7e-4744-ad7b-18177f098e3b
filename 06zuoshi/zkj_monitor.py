#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ZKJUSDT策略性能监控脚本
"""

import time
import json
import matplotlib.pyplot as plt
import pandas as pd
from datetime import datetime

def monitor_performance():
    """监控策略性能"""
    print("📊 ZKJUSDT策略性能监控")
    print("=" * 40)

    # 模拟性能数据
    performance_data = {
        'timestamp': [],
        'balance': [],
        'pnl': [],
        'trades': [],
        'win_rate': []
    }

    initial_balance = 200
    current_balance = initial_balance

    try:
        for i in range(100):  # 监控100个周期
            # 模拟性能变化
            pnl_change = (random.uniform(-5, 10) if 'random' in globals() else 2.5)
            current_balance += pnl_change

            performance_data['timestamp'].append(datetime.now())
            performance_data['balance'].append(current_balance)
            performance_data['pnl'].append(current_balance - initial_balance)
            performance_data['trades'].append(i + 1)
            performance_data['win_rate'].append(min(0.8, 0.5 + i * 0.003))

            # 实时显示
            if i % 10 == 0:
                total_return = (current_balance - initial_balance) / initial_balance
                print(f"周期 {i+1:3d} | 余额: {current_balance:7.2f} USDT | 收益率: {total_return*100:6.2f}%")

            time.sleep(0.1)  # 0.1秒间隔

    except KeyboardInterrupt:
        print("\n👋 监控停止")

    # 保存监控数据
    df = pd.DataFrame(performance_data)
    df.to_csv('zkj_performance_monitor.csv', index=False)

    print(f"\n📈 最终结果:")
    print(f"最终余额: {current_balance:.2f} USDT")
    print(f"总收益: {current_balance - initial_balance:.2f} USDT")
    print(f"收益率: {(current_balance - initial_balance) / initial_balance * 100:.2f}%")

if __name__ == "__main__":
    import random
    monitor_performance()
