import pandas as pd
import pandas_ta as ta
from backtesting import Backtest, Strategy
from backtesting.lib import crossover

# --- 策略定义：均值回归 (这部分是定义，不是执行，所以放在外面) ---
class MeanReversionStrategy(Strategy):
    """
    布林带均值回归策略 (Bollinger Band Mean Reversion)
    """
    # 策略参数（可以用于优化）
    bb_len = 20      # 布林带计算周期
    bb_std = 2.0     # 布林带标准差倍数
    
    # 增加一个参数来控制仓位大小，例如每次交易账户资金的95%
    trade_size = 0.95 

    def init(self):
        # ... (策略的 init 方法)
        self.data.df.ta.bbands(length=self.bb_len, std=self.bb_std, append=True)
        self.bb_upper = self.I(lambda: self.data.df[f'BBU_{self.bb_len}_{self.bb_std}'])
        self.bb_middle = self.I(lambda: self.data.df[f'BBM_{self.bb_len}_{self.bb_std}'])
        self.bb_lower = self.I(lambda: self.data.df[f'BBL_{self.bb_len}_{self.bb_std}'])

    def next(self):
        # ... (策略的 next 方法)
        if self.position.is_long and crossover(self.data.Close, self.bb_middle):
            self.position.close()
        elif self.position.is_short and crossover(self.bb_middle, self.data.Close):
            self.position.close()

        if not self.position:
            if crossover(self.bb_lower, self.data.Close):
                self.buy(size=self.trade_size)
            elif crossover(self.data.Close, self.bb_upper):
                self.sell(size=self.trade_size)


# --- 只有当这个脚本被直接运行时，才执行以下代码 ---
if __name__ == '__main__':
    # --- 数据加载与准备 ---
    df = pd.read_csv('sui.csv')
    df['Timestamp'] = pd.to_datetime(df['Timestamp'], unit='s')
    df.set_index('Timestamp', inplace=True)
    df.rename(columns={
        'Open': 'Open', 'High': 'High', 'Low': 'Low', 'Close': 'Close', 'Volume': 'Volume'
    }, inplace=True)
    df = df[['Open', 'High', 'Low', 'Close', 'Volume']]

    # --- 运行回测 ---
    bt = Backtest(
        df,
        MeanReversionStrategy,
        cash=100000,
        commission=.0005,
        exclusive_orders=True
    )

    # --- 进行参数优化 ---
    print("开始进行参数优化，这可能需要一些时间...")
    stats = bt.optimize(
        bb_len=range(10, 51, 5),
        bb_std=[1.5, 2.0, 2.5, 3.0],
        maximize='Sharpe Ratio',
    )
    
    print("\n--- 优化结果 ---")
    print(stats)
    print("\n--- 最优参数下的详细回测报告 ---")
    print(stats._strategy)

    # 绘制最优策略的图表
    bt.plot(filename='mean_reversion_plot.html', open_browser=True)
    print("\n图表已生成：mean_reversion_plot.html")