import pandas as pd
import numpy as np
from sklearn.preprocessing import MinMaxScaler
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from tensorflow.keras.callbacks import EarlyStopping
import matplotlib.pyplot as plt

# --- 参数配置 ---
# 1. 模型参数
LOOKBACK_PERIOD = 60  # 使用过去60个时间点的数据
FUTURE_PERIOD = 12   # 预测未来12个时间点的波动范围

# 2. 交易策略参数
SIGNAL_THRESHOLD_GAIN = 0.015  # 预测收益率 > 1.5%
SIGNAL_THRESHOLD_RR = 2.0      # 预测盈亏比 > 2.0
SL_ATR_MULTIPLIER = 1.5        # 止损 = 1.5 * ATR
TP_ATR_MULTIPLIER = 3.0        # 止盈 = 3.0 * ATR (实现2:1的盈亏比)


# --- 数据加载与预处理 ---
def load_and_prepare_data(filepath):
    """加载数据并进行特征工程"""
    df = pd.read_csv(filepath)
    
    # 将Timestamp转换为datetime并设为索引
    df['Timestamp'] = pd.to_datetime(df['Timestamp'], unit='s')
    df.set_index('Timestamp', inplace=True)
    
    # 1. 计算ATR (Average True Range)
    high_low = df['High'] - df['Low']
    high_close = np.abs(df['High'] - df['Close'].shift())
    low_close = np.abs(df['Low'] - df['Close'].shift())
    tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
    df['ATR'] = tr.rolling(window=14).mean()
    
    # 2. 定义预测目标 (Target)
    # 未来N个周期内的最高价和最低价
    df['future_high'] = df['High'].rolling(window=FUTURE_PERIOD).max().shift(-FUTURE_PERIOD)
    df['future_low'] = df['Low'].rolling(window=FUTURE_PERIOD).min().shift(-FUTURE_PERIOD)
    
    # 计算未来N周期内的最大收益率和最大亏损率
    df['target_gain'] = (df['future_high'] - df['Close']) / df['Close']
    df['target_loss'] = (df['future_low'] - df['Close']) / df['Close']
    
    # 删除含有NaN的行 (由于rolling和shift操作产生)
    df.dropna(inplace=True)
    
    # 确保没有无穷大的值
    df.replace([np.inf, -np.inf], np.nan, inplace=True)
    df.dropna(inplace=True)
    
    return df

# 加载数据
df = load_and_prepare_data('sui.csv')

# --- 特征和目标选择 ---
features = ['Close', 'Volume', 'ATR']
targets = ['target_gain', 'target_loss']

# 数据归一化 (对LSTM非常重要)
scaler_features = MinMaxScaler(feature_range=(0, 1))
scaled_features = scaler_features.fit_transform(df[features])

# 目标也进行归一化，但要保留scaler以便之后逆向转换
scaler_targets = MinMaxScaler(feature_range=(0, 1))
scaled_targets = scaler_targets.fit_transform(df[targets])

# --- 创建时间序列数据集 ---
X, y = [], []
for i in range(LOOKBACK_PERIOD, len(scaled_features)):
    X.append(scaled_features[i-LOOKBACK_PERIOD:i])
    y.append(scaled_targets[i])

X, y = np.array(X), np.array(y)

# --- 划分训练集和测试集 (时间序列数据不应打乱) ---
split_ratio = 0.8
split_index = int(len(X) * split_ratio)

X_train, X_test = X[:split_index], X[split_index:]
y_train, y_test = y[:split_index], y[split_index:]
df_test = df.iloc[-len(X_test):] # 用于回测的原始数据

print(f"训练集大小: {X_train.shape}")
print(f"测试集大小: {X_test.shape}")


# --- 构建LSTM模型 ---
model = Sequential()
model.add(LSTM(units=50, return_sequences=True, input_shape=(X_train.shape[1], X_train.shape[2])))
model.add(Dropout(0.2))
model.add(LSTM(units=50, return_sequences=False))
model.add(Dropout(0.2))
model.add(Dense(units=25))
model.add(Dense(units=2)) # 输出层有2个神经元，对应gain和loss

model.compile(optimizer='adam', loss='mean_squared_error')
model.summary()

# --- 训练模型 ---
# 使用EarlyStopping防止过拟合
early_stopping = EarlyStopping(monitor='val_loss', patience=10, restore_best_weights=True)

history = model.fit(
    X_train, y_train,
    epochs=50,
    batch_size=32,
    validation_split=0.1, # 从训练集中再分出一部分做验证
    callbacks=[early_stopping],
    verbose=1
)

# 绘制训练历史
plt.plot(history.history['loss'], label='Train Loss')
plt.plot(history.history['val_loss'], label='Validation Loss')
plt.title('Model Loss')
plt.legend()
plt.show()

# --- 在测试集上进行预测 ---
predictions_scaled = model.predict(X_test)
# 将预测结果逆向转换为原始尺度
predictions = scaler_targets.inverse_transform(predictions_scaled)

# --- 回测模拟 ---
trades = []
in_position = False
for i in range(len(df_test) - 1):
    if in_position:
        # 检查是否触及止盈或止损
        current_price = df_test['Close'].iloc[i]
        
        # 检查止盈
        if current_price >= trade['tp_price']:
            trade['exit_price'] = trade['tp_price']
            trade['exit_time'] = df_test.index[i]
            trade['pnl_percent'] = (trade['exit_price'] - trade['entry_price']) / trade['entry_price']
            trades.append(trade)
            in_position = False
            continue
            
        # 检查止损
        if current_price <= trade['sl_price']:
            trade['exit_price'] = trade['sl_price']
            trade['exit_time'] = df_test.index[i]
            trade['pnl_percent'] = (trade['exit_price'] - trade['entry_price']) / trade['entry_price']
            trades.append(trade)
            in_position = False
            continue
    
    if not in_position:
        pred_gain = predictions[i][0]
        pred_loss = predictions[i][1]
        
        # 检查入场信号
        # 确保预测亏损不是0，避免除零错误
        if pred_loss != 0 and \
           pred_gain > SIGNAL_THRESHOLD_GAIN and \
           (pred_gain / abs(pred_loss)) > SIGNAL_THRESHOLD_RR:
            
            entry_price = df_test['Close'].iloc[i]
            current_atr = df_test['ATR'].iloc[i]
            
            # 检查ATR是否有效
            if pd.isna(current_atr) or current_atr == 0:
                continue

            sl_price = entry_price - SL_ATR_MULTIPLIER * current_atr
            tp_price = entry_price + TP_ATR_MULTIPLIER * current_atr
            
            in_position = True
            trade = {
                'entry_time': df_test.index[i],
                'entry_price': entry_price,
                'sl_price': sl_price,
                'tp_price': tp_price,
                'predicted_gain': pred_gain,
                'predicted_loss': pred_loss
            }

# --- 结果分析 ---
if not trades:
    print("回测期间没有产生任何交易信号。")
else:
    trade_df = pd.DataFrame(trades)
    
    # 计算统计数据
    win_trades = trade_df[trade_df['pnl_percent'] > 0]
    loss_trades = trade_df[trade_df['pnl_percent'] <= 0]
    
    win_rate = len(win_trades) / len(trade_df) if len(trade_df) > 0 else 0
    avg_win = win_trades['pnl_percent'].mean() if len(win_trades) > 0 else 0
    avg_loss = loss_trades['pnl_percent'].mean() if len(loss_trades) > 0 else 0
    profit_factor = abs(win_trades['pnl_percent'].sum() / loss_trades['pnl_percent'].sum()) if loss_trades['pnl_percent'].sum() != 0 else np.inf
    
    # 计算夏普比率 (简化版，假设无风险利率为0)
    # 假设每个交易周期的长度相同
    returns = trade_df['pnl_percent']
    sharpe_ratio = returns.mean() / returns.std() if returns.std() != 0 else np.inf
    # 年化夏普率 (需要知道交易频率，这里仅作示例)
    # 例如，如果数据是分钟线，一年大约有 365*24*60 个分钟
    # annual_sharpe_ratio = sharpe_ratio * np.sqrt(365 * 24 * 60 / len(df_test)) 

    print("--- 回测结果分析 ---")
    print(f"总交易次数: {len(trade_df)}")
    print(f"胜率: {win_rate:.2%}")
    print(f"平均盈利: {avg_win:.2%}")
    print(f"平均亏损: {avg_loss:.2%}")
    print(f"盈亏比 (Profit Factor): {profit_factor:.2f}")
    print(f"策略收益率均值: {returns.mean():.4%}")
    print(f"策略收益率标准差: {returns.std():.4%}")
    print(f"每笔交易夏普比率 (简化): {sharpe_ratio:.2f}")
    
    # 打印一些交易示例
    print("\n--- 交易示例 ---")
    print(trade_df.head().to_string())